import torch
import torch.nn as nn
import torch.nn.functional as F
import numpy as np
from modules.attention import WindowAttention, ShiftWindowAttention, ChannelAttention, SpatialAttention, DualAttention, CrossScaleAttention
from modules.deformable_LKA import deformable_LKA, deformable_LKA_Attention, DeformConv
from modules.frequency_attention import EnhancedFrequencyChannelAttention, FrequencyDomainLoss, WaveletAttention
from modules.feature_extraction import FeaturePyramid, GlobalContext, LocalDetailEnhancement
from modules.domain_adaptation import ContrastiveLearning, FeatureEncoder
from modules.losses import PerceptualLoss, FrequencyLoss, StructureSimilarityLoss, TextureLoss

class AdvancedIntegratedAttention(nn.Module):
    """高级集成注意力模块，结合多种注意力机制和频域处理"""
    def __init__(self, dim, reduction=16, window_size=8, num_heads=8, freq_bands=4):
        super().__init__()
        # 空间注意力机制
        self.channel_attn = ChannelAttention(dim, reduction)
        self.spatial_attn = SpatialAttention()
        self.window_attn = WindowAttention(dim, window_size, num_heads)
        self.shift_window_attn = ShiftWindowAttention(dim, window_size, window_size//2, num_heads)
        
        # 频域注意力机制
        self.freq_attn = EnhancedFrequencyChannelAttention(dim, reduction, freq_bands)
        self.wavelet_attn = WaveletAttention(dim, reduction)
        
        # 可变形注意力
        self.deform_lka = deformable_LKA_Attention(dim)
        
        # 特征融合
        self.fusion1 = nn.Conv2d(dim * 4, dim, kernel_size=1)  # 空间注意力融合
        self.fusion2 = nn.Conv2d(dim * 2, dim, kernel_size=1)  # 频域注意力融合
        self.final_fusion = nn.Conv2d(dim * 3, dim, kernel_size=1)  # 最终融合
        
        # 层归一化
        self.norm1 = nn.LayerNorm(dim)
        self.norm2 = nn.LayerNorm(dim)
        self.norm3 = nn.LayerNorm(dim)
        
    def forward(self, x):
        # 处理五维输入
        if x.dim() == 5:
            B, C, D, H, W = x.shape
            x = x.view(B, C * D, H, W)
        else:
            B, C, H, W = x.shape
        
        # 空间注意力处理
        x1 = self.channel_attn(x)
        x2 = self.spatial_attn(x)
        x3 = self.window_attn(x)
        x4 = self.shift_window_attn(x)
        
        # 空间注意力融合
        spatial_combined = torch.cat([x1, x2, x3, x4], dim=1)
        spatial_fused = self.fusion1(spatial_combined)
        spatial_fused = spatial_fused.permute(0, 2, 3, 1)  # B, H, W, C
        spatial_fused = self.norm1(spatial_fused)
        spatial_fused = spatial_fused.permute(0, 3, 1, 2)  # B, C, H, W
        
        # 频域注意力处理
        x5 = self.freq_attn(x)
        x6 = self.wavelet_attn(x)
        
        # 频域注意力融合
        freq_combined = torch.cat([x5, x6], dim=1)
        freq_fused = self.fusion2(freq_combined)
        freq_fused = freq_fused.permute(0, 2, 3, 1)  # B, H, W, C
        freq_fused = self.norm2(freq_fused)
        freq_fused = freq_fused.permute(0, 3, 1, 2)  # B, C, H, W
        
        # 可变形注意力处理
        x7 = self.deform_lka(x)
        x7 = x7.permute(0, 2, 3, 1)  # B, H, W, C
        x7 = self.norm3(x7)
        x7 = x7.permute(0, 3, 1, 2)  # B, C, H, W
        
        # 最终融合
        final_combined = torch.cat([spatial_fused, freq_fused, x7], dim=1)
        final_fused = self.final_fusion(final_combined)
        
        # 残差连接
        # 恢复原始维度
        if x_ndim == 5:
            final_fused = final_fused.view(B, C, D, H, W)
        return final_fused + x

class EnhancedMultiScaleFrequencyBlock(nn.Module):
    """增强型多尺度频域处理块，专为CT图像设计"""
    def __init__(self, dim, freq_bands=4, reduction=16):
        super().__init__()
        # 频域处理
        self.freq_attn = EnhancedFrequencyChannelAttention(dim, reduction, freq_bands)
        self.wavelet_attn = WaveletAttention(dim, reduction)
        
        # 多尺度特征提取
        self.feature_pyramid = FeaturePyramid(dim, dim)
        self.global_context = GlobalContext(dim, reduction)
        
        # 跨尺度注意力
        self.cross_scale_attn = CrossScaleAttention(dim)
        
        # 特征融合
        self.fusion1 = nn.Conv2d(dim * 2, dim, kernel_size=1)  # 频域融合
        self.fusion2 = nn.Conv2d(dim * 2, dim, kernel_size=1)  # 多尺度融合
        self.final_fusion = nn.Conv2d(dim * 3, dim, kernel_size=1)  # 最终融合
        
        # 层归一化
        self.norm1 = nn.LayerNorm(dim)
        self.norm2 = nn.LayerNorm(dim)
        self.norm3 = nn.LayerNorm(dim)
        
    def forward(self, x):
        # 频域处理
        x1 = self.freq_attn(x)
        x2 = self.wavelet_attn(x)
        
        # 频域融合
        freq_combined = torch.cat([x1, x2], dim=1)
        freq_fused = self.fusion1(freq_combined)
        freq_fused = freq_fused.permute(0, 2, 3, 1)  # B, H, W, C
        freq_fused = self.norm1(freq_fused)
        freq_fused = freq_fused.permute(0, 3, 1, 2)  # B, C, H, W
        
        # 多尺度特征提取
        x3 = self.feature_pyramid(x)
        x4 = self.global_context(x)
        
        # 多尺度融合
        scale_combined = torch.cat([x3, x4], dim=1)
        scale_fused = self.fusion2(scale_combined)
        scale_fused = scale_fused.permute(0, 2, 3, 1)  # B, H, W, C
        scale_fused = self.norm2(scale_fused)
        scale_fused = scale_fused.permute(0, 3, 1, 2)  # B, C, H, W
        
        # 跨尺度注意力
        x5 = self.cross_scale_attn(x)
        x5 = x5.permute(0, 2, 3, 1)  # B, H, W, C
        x5 = self.norm3(x5)
        x5 = x5.permute(0, 3, 1, 2)  # B, C, H, W
        
        # 最终融合
        final_combined = torch.cat([freq_fused, scale_fused, x5], dim=1)
        final_fused = self.final_fusion(final_combined)
        
        # 残差连接
        # 恢复原始维度
        if x_ndim == 5:
            final_fused = final_fused.view(B, C, D, H, W)
        return final_fused + x

class AdvancedDeformableLKA(nn.Module):
    """高级可变形大核注意力模块，增强边缘和纹理细节"""
    def __init__(self, dim, kernel_size=7, groups=8):
        super().__init__()
        # 可变形大核注意力
        self.deform_lka = deformable_LKA(dim)
        
        # 可变形卷积
        self.deform_conv1 = DeformConv(dim, groups, kernel_size=(3, 3), padding=1)
        self.deform_conv2 = DeformConv(dim, groups, kernel_size=(kernel_size, kernel_size), padding=kernel_size//2)
        
        # 通道注意力
        self.channel_attn = ChannelAttention(dim)
        
        # 深度可分离卷积
        self.depth_conv1 = nn.Conv2d(dim, dim, kernel_size=3, padding=1, groups=dim)
        self.depth_conv2 = nn.Conv2d(dim, dim, kernel_size=5, padding=2, groups=dim)
        self.point_conv1 = nn.Conv2d(dim, dim, kernel_size=1)
        self.point_conv2 = nn.Conv2d(dim, dim, kernel_size=1)
        
        # 特征融合
        self.fusion = nn.Conv2d(dim * 5, dim, kernel_size=1)
        
        # 激活函数
        self.gelu = nn.GELU()
        
    def forward(self, x):
        # 可变形大核注意力
        x1 = self.deform_lka(x)
        
        # 可变形卷积
        x2 = self.deform_conv1(x)
        x3 = self.deform_conv2(x)
        
        # 通道注意力
        x4 = self.channel_attn(x)
        
        # 深度可分离卷积
        x5_1 = self.depth_conv1(x)
        x5_1 = self.gelu(x5_1)
        x5_1 = self.point_conv1(x5_1)
        
        x5_2 = self.depth_conv2(x)
        x5_2 = self.gelu(x5_2)
        x5_2 = self.point_conv2(x5_2)
        
        # 特征融合
        combined = torch.cat([x1, x2, x3, x4, x5_1 + x5_2], dim=1)
        fused = self.fusion(combined)
        
        # 残差连接
        return fused + x

class AdvancedIntegratedLoss(nn.Module):
    """高级集成损失函数，自适应权重"""
    def __init__(self, perceptual_weight=0.1, freq_weight=0.2, ssim_weight=0.3, texture_weight=0.4):
        super().__init__()
        self.perceptual_loss = PerceptualLoss()
        self.freq_loss = FrequencyLoss()
        self.freq_domain_loss = FrequencyDomainLoss()
        self.ssim_loss = StructureSimilarityLoss()
        self.texture_loss = TextureLoss()
        
        # 初始权重
        self.perceptual_weight = nn.Parameter(torch.tensor(perceptual_weight))
        self.freq_weight = nn.Parameter(torch.tensor(freq_weight))
        self.freq_domain_weight = nn.Parameter(torch.tensor(0.15))
        self.ssim_weight = nn.Parameter(torch.tensor(ssim_weight))
        self.texture_weight = nn.Parameter(torch.tensor(texture_weight))
        
        # 权重归一化层
        self.weight_norm = nn.Softmax(dim=0)
        
    def forward(self, pred, target):
        # 计算各种损失
        p_loss = self.perceptual_loss(pred, target)
        f_loss = self.freq_loss(pred, target)
        fd_loss = self.freq_domain_loss(pred, target)
        s_loss = self.ssim_loss(pred, target)
        t_loss = self.texture_loss(pred, target)
        
        # 权重归一化
        weights = self.weight_norm(torch.stack([
            self.perceptual_weight,
            self.freq_weight,
            self.freq_domain_weight,
            self.ssim_weight,
            self.texture_weight
        ]))
        
        # 加权组合
        total_loss = (weights[0] * p_loss + 
                      weights[1] * f_loss + 
                      weights[2] * fd_loss + 
                      weights[3] * s_loss + 
                      weights[4] * t_loss)
        
        return total_loss, {
            "perceptual": p_loss, 
            "frequency": f_loss, 
            "freq_domain": fd_loss,
            "ssim": s_loss, 
            "texture": t_loss,
            "weights": weights.detach().cpu()
        }

class EnhancedDomainAdaptiveFeatureExtractor(nn.Module):
    """增强型领域自适应特征提取器，专为CT图像设计"""
    def __init__(self, in_channels=1, out_channels=64, projection_dim=128):
        super().__init__()
        self.encoder = FeatureEncoder(in_channels, out_channels)
        self.contrastive = ContrastiveLearning(self.encoder, projection_dim)
        
        # 特征增强
        self.feature_enhance = LocalDetailEnhancement(out_channels)
        self.cross_scale_attn = CrossScaleAttention(out_channels)
        self.wavelet_attn = WaveletAttention(out_channels)
        
        # 自适应特征提取
        self.adaptive_conv1 = nn.Conv2d(out_channels, out_channels, kernel_size=3, padding=1)
        self.adaptive_conv2 = nn.Conv2d(out_channels, out_channels, kernel_size=3, padding=1)
        self.adaptive_norm = nn.InstanceNorm2d(out_channels)
        self.adaptive_gate = nn.Sequential(
            nn.Conv2d(out_channels * 2, out_channels, kernel_size=1),
            nn.Sigmoid()
        )
        
        # 多尺度特征融合
        self.multi_scale_fusion = nn.Sequential(
            nn.Conv2d(out_channels * 3, out_channels, kernel_size=1),
            nn.LeakyReLU(0.2),
            nn.Conv2d(out_channels, out_channels, kernel_size=3, padding=1)
        )
        
        # 对比学习损失函数优化
        self.temperature = nn.Parameter(torch.ones(1) * 0.07)
        self.queue_size = 4096
        self.register_buffer("feature_queue", torch.randn(self.queue_size, projection_dim))
        self.register_buffer("queue_ptr", torch.zeros(1, dtype=torch.long))
        
    def forward(self, x):
        # 基础特征提取
        base_features = self.encoder(x)
        
        # 自适应特征提取
        adaptive_feat1 = self.adaptive_conv1(base_features)
        adaptive_feat1 = self.adaptive_norm(adaptive_feat1)
        adaptive_feat2 = self.adaptive_conv2(adaptive_feat1)
        gate = self.adaptive_gate(torch.cat([base_features, adaptive_feat2], dim=1))
        adaptive_features = gate * adaptive_feat2 + (1 - gate) * base_features
        
        # 多尺度特征处理
        enhanced_features = self.feature_enhance(adaptive_features)
        cross_scale_features = self.cross_scale_attn(enhanced_features)
        wavelet_features = self.wavelet_attn(enhanced_features)
        
        # 特征融合
        combined_features = torch.cat([
            enhanced_features,
            cross_scale_features,
            wavelet_features
        ], dim=1)
        fused_features = self.multi_scale_fusion(combined_features)
        
        # 对比学习
        if self.training:
            proj_features = self.contrastive(fused_features)
            self._update_queue(proj_features)
            return fused_features, proj_features
        
        return fused_features
    
    @torch.no_grad()
    def _update_queue(self, features):
        batch_size = features.shape[0]
        ptr = int(self.queue_ptr)
        
        # 更新特征队列
        if ptr + batch_size > self.queue_size:
            self.feature_queue[ptr:] = features[:self.queue_size - ptr].detach()
            self.feature_queue[:batch_size - (self.queue_size - ptr)] = \
                features[self.queue_size - ptr:].detach()
            ptr = batch_size - (self.queue_size - ptr)
        else:
            self.feature_queue[ptr:ptr + batch_size] = features.detach()
            ptr = ptr + batch_size
        
        self.queue_ptr[0] = ptr % self.queue_size

class WaveletAttention(nn.Module):
    """小波注意力机制，用于多尺度特征提取"""
    def __init__(self, dim, reduction=16):
        super().__init__()
        self.conv1 = nn.Conv2d(dim, dim//reduction, kernel_size=1)
        self.conv2 = nn.Conv2d(dim//reduction, dim, kernel_size=1)
        self.softmax = nn.Softmax(dim=1)
        
    def forward(self, x):
        # 小波变换
        coeffs = self.dwt_transform(x)
        # 注意力权重
        weights = self.conv1(x)
        weights = F.relu(weights)
        weights = self.conv2(weights)
        weights = self.softmax(weights)
        # 加权融合
        return x * weights
        
    def dwt_transform(self, x):
        # 实现离散小波变换
        # 处理五维输入
        if x.dim() == 5:
            B, C, D, H, W = x.shape
            x = x.view(B, C * D, H, W)
        else:
            B, C, H, W = x.shape
        # 使用Haar小波基函数
        haar_weights = torch.tensor([[1, 1], [1, -1]], dtype=torch.float32) / 2.0
        haar_weights = haar_weights.view(1, 1, 2, 2).to(x.device)
        # 进行卷积操作实现小波变换
        x_transformed = F.conv2d(x.view(-1, 1, H, W), haar_weights, stride=2)
        x_transformed = x_transformed.view(B, C, H//2, W//2)
        return x_transformed

class ChannelAttention(nn.Module):
    """通道注意力机制，用于增强特征提取器的通道维度特征学习"""
    def __init__(self, dim, reduction=16):
        super().__init__()
        self.avg_pool = nn.AdaptiveAvgPool2d(1)
        self.max_pool = nn.AdaptiveMaxPool2d(1)
        
        self.shared_mlp = nn.Sequential(
            nn.Conv2d(dim, dim//reduction, kernel_size=1),
            nn.ReLU(),
            nn.Conv2d(dim//reduction, dim, kernel_size=1)
        )
        
        self.sigmoid = nn.Sigmoid()
        
    def forward(self, x):
        avg_out = self.shared_mlp(self.avg_pool(x))
        max_out = self.shared_mlp(self.max_pool(x))
        out = self.sigmoid(avg_out + max_out)
        return x * out

class CrossScaleAttention(nn.Module):
    """跨尺度注意力模块，用于特征融合"""
    def __init__(self, dim):
        super().__init__()
        self.query_conv = nn.Conv2d(dim, dim//8, kernel_size=1)
        self.key_conv = nn.Conv2d(dim, dim//8, kernel_size=1)
        self.value_conv = nn.Conv2d(dim, dim, kernel_size=1)
        self.softmax = nn.Softmax(dim=-1)
        
    def forward(self, x):
        B, C, D, H, W = x.shape
        x = x.view(B, C * D, H, W)
        # 生成查询、键、值
        query = self.query_conv(x).view(B, -1, H*W)
        key = self.key_conv(x).view(B, -1, H*W)
        value = self.value_conv(x).view(B, -1, H*W)
        # 计算注意力分数
        attn = torch.bmm(query.permute(0, 2, 1), key)
        attn = self.softmax(attn)
        # 加权求和
        out = torch.bmm(value, attn.permute(0, 2, 1))
        out = out.view(B, C, H, W)
        return out

class LocalDetailEnhancement(nn.Module):
    """局部细节增强模块，专注于CT图像的细节重建"""
    def __init__(self, dim, reduction=16):
        super().__init__()
        # 局部特征提取
        self.local_conv1 = nn.Conv2d(dim, dim, kernel_size=3, padding=1, groups=dim)
        self.local_conv2 = nn.Conv2d(dim, dim, kernel_size=5, padding=2, groups=dim)
        
        # 通道注意力
        self.channel_attn = ChannelAttention(dim, reduction)
        
        # 特征重构
        self.reconstruct = nn.Sequential(
            nn.Conv2d(dim * 2, dim, kernel_size=1),
            nn.GELU(),
            nn.Conv2d(dim, dim, kernel_size=3, padding=1)
        )
        
        # 自适应权重
        self.adaptive_weight = nn.Sequential(
            nn.AdaptiveAvgPool2d(1),
            nn.Conv2d(dim, dim, kernel_size=1),
            nn.Sigmoid()
        )
        
    def forward(self, x):
        # 局部特征提取
        local_feat1 = self.local_conv1(x)
        local_feat2 = self.local_conv2(x)
        
        # 通道注意力
        channel_weights = self.channel_attn(x)
        
        # 特征融合
        enhanced_feat = torch.cat([local_feat1, local_feat2], dim=1)
        enhanced_feat = self.reconstruct(enhanced_feat)
        
        # 自适应加权
        weights = self.adaptive_weight(enhanced_feat)
        
        return x + weights * enhanced_feat
class DualAttention(nn.Module):
    def __init__(self, dim, reduction=16):
        super().__init__()
        self.chanel_in = dim
        
        # 定义注意力相关卷积层
        self.query_conv = nn.Conv2d(dim, dim // 8, kernel_size=1)
        self.key_conv = nn.Conv2d(dim, dim // 8, kernel_size=1)
        self.value_conv = nn.Conv2d(dim, dim, kernel_size=1)
        self.softmax = nn.Softmax(dim=-1)

    def forward(self, x):
        # 处理五维输入
        if x.dim() == 5:
            B, C, D, H, W = x.shape
            x = x.view(B, C * D, H, W)
        else:
            B, C, H, W = x.shape
        # 生成查询、键、值
        query = self.query_conv(x).view(B, -1, H*W)
        key = self.key_conv(x).view(B, -1, H*W)
        value = self.value_conv(x).view(B, -1, H*W)
        # 计算注意力分数
        attn = torch.bmm(query.permute(0, 2, 1), key)
        attn = self.softmax(attn)
        # 加权求和
        out = torch.bmm(value, attn.permute(0, 2, 1))
        out = out.view(B, C, H, W)
        return out

    def dwt_transform(self, x):
        # 实现离散小波变换
        # 处理五维输入
        if x.dim() == 5:
            B, C, D, H, W = x.shape
            x = x.view(B, C * D, H, W)
        else:
            B, C, H, W = x.shape
        # 使用Haar小波基函数
        haar_weights = torch.tensor([[1, 1], [1, -1]], dtype=torch.float32) / 2.0
        haar_weights = haar_weights.view(1, 1, 2, 2).to(x.device)
        # 进行卷积操作实现小波变换
        x_transformed = F.conv2d(x.view(-1, 1, H, W), haar_weights, stride=2)
        x_transformed = x_transformed.view(B, C, H//2, W//2)
        return x_transformed

class ChannelAttention(nn.Module):
    """通道注意力机制，用于增强特征提取器的通道维度特征学习"""
    def __init__(self, dim, reduction=16):
        super().__init__()
        self.avg_pool = nn.AdaptiveAvgPool2d(1)
        self.max_pool = nn.AdaptiveMaxPool2d(1)
        
        self.shared_mlp = nn.Sequential(
            nn.Conv2d(dim, dim//reduction, kernel_size=1),
            nn.ReLU(),
            nn.Conv2d(dim//reduction, dim, kernel_size=1)
        )
        
        self.sigmoid = nn.Sigmoid()
        
    def forward(self, x):
        avg_out = self.shared_mlp(self.avg_pool(x))
        max_out = self.shared_mlp(self.max_pool(x))
        out = self.sigmoid(avg_out + max_out)
        return x * out

class CrossScaleAttention(nn.Module):
    """跨尺度注意力模块，用于特征融合"""
    def __init__(self, dim):
        super().__init__()
        self.query_conv = nn.Conv2d(dim, dim//8, kernel_size=1)
        self.key_conv = nn.Conv2d(dim, dim//8, kernel_size=1)
        self.value_conv = nn.Conv2d(dim, dim, kernel_size=1)
        self.softmax = nn.Softmax(dim=-1)
        
    def forward(self, x):
        # 处理五维输入
        if x.dim() == 5:
            B, C, D, H, W = x.shape
            x = x.view(B, C * D, H, W)
        else:
            B, C, H, W = x.shape
        # 生成查询、键、值
        query = self.query_conv(x).view(B, -1, H*W)
        key = self.key_conv(x).view(B, -1, H*W)
        value = self.value_conv(x).view(B, -1, H*W)
        # 计算注意力分数
        attn = torch.bmm(query.permute(0, 2, 1), key)
        attn = self.softmax(attn)
        # 加权求和
        out = torch.bmm(value, attn.permute(0, 2, 1))
        out = out.view(B, C, H, W)
        return out

class LocalDetailEnhancement(nn.Module):
    """局部细节增强模块，专注于CT图像的细节重建"""
    def __init__(self, dim, reduction=16):
        super().__init__()
        # 局部特征提取
        self.local_conv1 = nn.Conv2d(dim, dim, kernel_size=3, padding=1, groups=dim)
        self.local_conv2 = nn.Conv2d(dim, dim, kernel_size=5, padding=2, groups=dim)
        
        # 通道注意力
        self.channel_attn = ChannelAttention(dim, reduction)
        
        # 特征重构
        self.reconstruct = nn.Sequential(
            nn.Conv2d(dim * 2, dim, kernel_size=1),
            nn.GELU(),
            nn.Conv2d(dim, dim, kernel_size=3, padding=1)
        )
        
        # 自适应权重
        self.adaptive_weight = nn.Sequential(
            nn.AdaptiveAvgPool2d(1),
            nn.Conv2d(dim, dim, kernel_size=1),
            nn.Sigmoid()
        )
        
    def forward(self, x):
        # 局部特征提取
        local_feat1 = self.local_conv1(x)
        local_feat2 = self.local_conv2(x)
        
        # 通道注意力
        channel_weights = self.channel_attn(x)
        
        # 特征融合
        enhanced_feat = torch.cat([local_feat1, local_feat2], dim=1)
        enhanced_feat = self.reconstruct(enhanced_feat)
        
        # 自适应加权
        weights = self.adaptive_weight(enhanced_feat)
        
        return x + weights * enhanced_feat
