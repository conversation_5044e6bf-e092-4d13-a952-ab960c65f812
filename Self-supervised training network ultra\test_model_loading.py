#!/usr/bin/env python3
"""
Test script for verifying enhanced model saving and loading functionality
Tests both best model and final model loading
"""

import os
import sys
import torch
import yaml
from pathlib import Path

# Add project root to path
PROJECT_ROOT = os.path.abspath(os.path.join(os.path.dirname(__file__), '..'))
if PROJECT_ROOT not in sys.path:
    sys.path.insert(0, PROJECT_ROOT)

from models.diffusion_sr_model_ultra import DiffusionSRModel

def test_model_loading(checkpoint_dir, config_path):
    """Test loading different types of saved models"""
    
    print("="*60)
    print("TESTING ENHANCED MODEL SAVING AND LOADING")
    print("="*60)
    
    # Load config
    with open(config_path, 'r', encoding='utf-8') as f:
        config = yaml.safe_load(f)
    
    device = torch.device('cuda' if torch.cuda.is_available() else 'cpu')
    print(f"Using device: {device}")
    
    # Test files to check
    test_files = [
        'best_model.pth',
        'best_model_generator.pth', 
        'final_model.pth',
        'final_model_generator.pth'
    ]
    
    # Find available checkpoint files
    available_files = []
    for test_file in test_files:
        file_path = os.path.join(checkpoint_dir, test_file)
        if os.path.exists(file_path):
            available_files.append((test_file, file_path))
        else:
            print(f"❌ {test_file} not found")
    
    if not available_files:
        print("❌ No model files found to test!")
        return False
    
    print(f"\n✅ Found {len(available_files)} model files to test:")
    for filename, _ in available_files:
        print(f"   - {filename}")
    
    # Test each available file
    success_count = 0
    for filename, file_path in available_files:
        print(f"\n{'='*40}")
        print(f"Testing: {filename}")
        print(f"{'='*40}")
        
        try:
            # Load checkpoint
            checkpoint = torch.load(file_path, map_location=device)
            print(f"✅ Successfully loaded checkpoint")
            
            # Check required keys
            required_keys = ['model_state_dict', 'epoch', 'config']
            missing_keys = [key for key in required_keys if key not in checkpoint]
            if missing_keys:
                print(f"❌ Missing required keys: {missing_keys}")
                continue
            
            print(f"✅ All required keys present")
            
            # Print checkpoint info
            print(f"📊 Checkpoint Information:")
            print(f"   - Epoch: {checkpoint.get('epoch', 'N/A')}")
            print(f"   - Global Step: {checkpoint.get('global_step', 'N/A')}")
            
            if 'total_loss' in checkpoint:
                print(f"   - Total Loss: {checkpoint['total_loss']:.6f}")
            if 'best_total_loss' in checkpoint:
                print(f"   - Best Total Loss: {checkpoint['best_total_loss']:.6f}")
            if 'best_epoch' in checkpoint:
                print(f"   - Best Epoch: {checkpoint['best_epoch']}")
            
            # Test model loading
            print(f"🔄 Testing model instantiation...")
            model = DiffusionSRModel(config).to(device)
            print(f"✅ Model instantiated successfully")
            
            # Load model state
            print(f"🔄 Loading model state dict...")
            model.load_state_dict(checkpoint['model_state_dict'])
            print(f"✅ Model state loaded successfully")
            
            # Test model forward pass (dummy input)
            print(f"🔄 Testing model forward pass...")
            model.eval()
            with torch.no_grad():
                # Create dummy inputs
                batch_size = 2
                channels = config['model']['in_channels']
                height = width = 64  # Small test size
                
                x_t = torch.randn(batch_size, channels, height, width).to(device)
                t = torch.randint(0, 100, (batch_size,)).to(device)
                condition = torch.randn(batch_size, channels, height//4, width//4).to(device)
                
                # Forward pass
                output = model(x_t, t, condition=condition)
                
                expected_shape = (batch_size, channels, height, width)
                if output.shape == expected_shape:
                    print(f"✅ Forward pass successful, output shape: {output.shape}")
                else:
                    print(f"❌ Forward pass failed, expected {expected_shape}, got {output.shape}")
                    continue
            
            # Check additional states for complete models
            if 'optimizer_state_dict' in checkpoint:
                print(f"✅ Optimizer state dict present")
            if 'scheduler_state_dict' in checkpoint:
                print(f"✅ Scheduler state dict present")
            if 'scaler_state_dict' in checkpoint:
                print(f"✅ AMP scaler state dict present")
            if 'discriminator_state_dict' in checkpoint:
                print(f"✅ Discriminator state dict present")
            
            print(f"✅ {filename} passed all tests!")
            success_count += 1
            
        except Exception as e:
            print(f"❌ Error testing {filename}: {e}")
            continue
    
    print(f"\n{'='*60}")
    print(f"TESTING SUMMARY")
    print(f"{'='*60}")
    print(f"Total files tested: {len(available_files)}")
    print(f"Successful tests: {success_count}")
    print(f"Failed tests: {len(available_files) - success_count}")
    
    if success_count == len(available_files):
        print(f"🎉 All tests passed! Enhanced model saving is working correctly.")
        return True
    else:
        print(f"⚠️  Some tests failed. Please check the error messages above.")
        return False

def find_latest_checkpoint_dir():
    """Find the most recent checkpoint directory"""
    base_dir = "./checkpoints/sr_diffusion"
    if not os.path.exists(base_dir):
        return None
    
    # Look for experiment directories
    experiment_dirs = [d for d in os.listdir(base_dir) 
                      if os.path.isdir(os.path.join(base_dir, d))]
    
    if not experiment_dirs:
        return None
    
    # Return the most recent one (by name)
    latest_dir = sorted(experiment_dirs)[-1]
    return os.path.join(base_dir, latest_dir)

def main():
    import argparse
    
    parser = argparse.ArgumentParser(description='Test Enhanced Model Loading')
    parser.add_argument('--checkpoint_dir', type=str, default=None, 
                       help='Path to checkpoint directory')
    parser.add_argument('--config', type=str, 
                       default='configs/config_ultra_no_gan.yaml',
                       help='Path to config file')
    
    args = parser.parse_args()
    
    # Find checkpoint directory
    if args.checkpoint_dir:
        checkpoint_dir = args.checkpoint_dir
    else:
        checkpoint_dir = find_latest_checkpoint_dir()
        if checkpoint_dir:
            print(f"Auto-detected checkpoint directory: {checkpoint_dir}")
        else:
            print("❌ No checkpoint directory found. Please specify --checkpoint_dir")
            return
    
    if not os.path.exists(checkpoint_dir):
        print(f"❌ Checkpoint directory not found: {checkpoint_dir}")
        return
    
    if not os.path.exists(args.config):
        print(f"❌ Config file not found: {args.config}")
        return
    
    # Run tests
    success = test_model_loading(checkpoint_dir, args.config)
    
    if success:
        print(f"\n🎯 RECOMMENDATION:")
        print(f"   Use 'best_model_generator.pth' for inference")
        print(f"   Use 'best_model.pth' for resuming training")
        print(f"   Location: {checkpoint_dir}")
    
    return success

if __name__ == '__main__':
    main()
