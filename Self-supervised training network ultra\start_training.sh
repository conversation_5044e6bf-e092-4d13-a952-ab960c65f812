#!/bin/bash

echo "============================================================"
echo "CT图像超分辨率训练启动脚本"
echo "============================================================"
echo

echo "🔧 激活Python环境: pytorchEnv"
source activate pytorchEnv
if [ $? -ne 0 ]; then
    echo "❌ 无法激活pytorchEnv环境"
    echo "请确保已安装conda并且pytorchEnv环境存在"
    exit 1
fi

echo "✅ 环境激活成功"
echo

echo "📁 切换到训练目录"
cd "$(dirname "$0")"
echo "当前目录: $(pwd)"
echo

echo "🚀 开始训练..."
echo "============================================================"
python run_training.py

echo
echo "============================================================"
echo "训练脚本执行完成"
echo "============================================================"
