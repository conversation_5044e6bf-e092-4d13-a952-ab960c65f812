import torch
import torch.nn as nn
import torch.nn.functional as F
import torch.optim as optim
from torch.optim.lr_scheduler import CosineAnnealingLR
from data.mae_loader import get_mae_loader # Import loader
import timm # Import timm
assert timm.__version__ >= "0.3.2" # MAE requires timm support, assert version
# Use timm's Block for ViT-style decoder
from timm.models.vision_transformer import Block # Changed to ViT Block
# from timm.models.swin_transformer import SwinTransformerBlock # Removed Swin Block import
from tqdm import tqdm
import os
import numpy as np
import matplotlib.pyplot as plt
from torch.utils.tensorboard import SummaryWriter
import math
import random
from util.pos_embed import get_2d_sincos_pos_embed # Assuming pos embed helper is in util
# import util.lr_sched as lr_sched # Keep import for now, even if not directly used
import torchvision.models as models
import torchvision.transforms as transforms
import argparse
from pytorch_msssim import ssim, ms_ssim # Import SSIM and MS-SSIM
from timm.models.layers import DropPath, Mlp

# --- Random seed control function ---
def set_seed(seed=42):
    """设置所有随机种子以确保可重复性"""
    random.seed(seed)
    np.random.seed(seed)
    torch.manual_seed(seed)
    if torch.cuda.is_available():
        torch.cuda.manual_seed(seed)
        torch.cuda.manual_seed_all(seed)
    torch.backends.cudnn.deterministic = True
    torch.backends.cudnn.benchmark = False
    print(f"随机种子已设置为: {seed}")

def clear_cuda_cache():
    """清理CUDA缓存，释放GPU内存"""
    import gc
    gc.collect()
    if torch.cuda.is_available():
        torch.cuda.empty_cache()
        try:
            torch.cuda.synchronize()
        except RuntimeError:
            print("警告: CUDA同步失败，可能是由于CUDA上下文未初始化")
    print("CUDA缓存已清理")


# Helper Modules for Hierarchical Decoder with Cross-Attention

# Basic Cross Attention Block
class CrossAttention(nn.Module):
    def __init__(self, dim, num_heads=8, qkv_bias=False, attn_drop=0., proj_drop=0.):
        super().__init__()
        assert dim % num_heads == 0, 'dim should be divisible by num_heads'
        self.num_heads = num_heads
        head_dim = dim // num_heads
        self.scale = head_dim ** -0.5

        self.q = nn.Linear(dim, dim, bias=qkv_bias)
        # Project K and V together from encoder features (context)
        self.kv = nn.Linear(dim, dim * 2, bias=qkv_bias)
        self.attn_drop = nn.Dropout(attn_drop)
        self.proj = nn.Linear(dim, dim)
        self.proj_drop = nn.Dropout(proj_drop)

    def forward(self, x_query, x_kv):
        # x_query: [B, N_q, C] (Decoder features - Query)
        # x_kv: [B, N_kv, C] (Encoder features - Key/Value context)
        B, N_q, C = x_query.shape
        B_kv, N_kv, C_kv = x_kv.shape # Get shape of context
        assert C == C_kv, "Query and Key/Value dimensions must match"

        q = self.q(x_query).reshape(B, N_q, self.num_heads, C // self.num_heads).permute(0, 2, 1, 3) # B, h, N_q, C/h
        kv = self.kv(x_kv).reshape(B_kv, N_kv, 2, self.num_heads, C // self.num_heads).permute(2, 0, 3, 1, 4) # 2, B, h, N_kv, C/h
        k, v = kv.unbind(0) # B, h, N_kv, C/h

        attn = (q @ k.transpose(-2, -1)) * self.scale # B, h, N_q, N_kv
        attn = attn.softmax(dim=-1)
        attn = self.attn_drop(attn)

        x = (attn @ v).transpose(1, 2).reshape(B, N_q, C) # B, N_q, C
        x = self.proj(x)
        x = self.proj_drop(x)
        return x

# Combined Cross-Attention and Self-Attention Block (inspired by Transformer decoders)
class DecoderCrossAttentionBlock(nn.Module):
    def __init__(self, dim, num_heads, mlp_ratio=4., qkv_bias=False, drop=0., attn_drop=0.,
                 drop_path=0., act_layer=nn.GELU, norm_layer=nn.LayerNorm):
        super().__init__()
        self.norm1_q = norm_layer(dim)
        self.norm1_kv = norm_layer(dim) # Norm for encoder features (Key/Value)
        self.cross_attn = CrossAttention(
            dim, num_heads=num_heads, qkv_bias=qkv_bias, attn_drop=attn_drop, proj_drop=drop)
        self.drop_path1 = DropPath(drop_path) if drop_path > 0. else nn.Identity()

        # Standard Self-Attention Block (using timm's Block)
        # Note: timm's Block applies norm *before* attention/mlp and includes residual connection
        self.self_attn_block = Block(
            dim=dim, num_heads=num_heads, mlp_ratio=mlp_ratio, qkv_bias=qkv_bias,
            proj_drop=drop, attn_drop=attn_drop, drop_path=drop_path, norm_layer=norm_layer, act_layer=act_layer
        )

    def forward(self, x_query, x_kv):
        # x_query: [B, N_q, C] (Decoder features)
        # x_kv: [B, N_kv, C] (Encoder features)

        # Cross-Attention part (Query attends to Key/Value from Encoder) + Residual
        x_query = x_query + self.drop_path1(self.cross_attn(self.norm1_q(x_query), self.norm1_kv(x_kv)))

        # Self-Attention + MLP part (using timm's Block, which includes residuals internally)
        x_query = self.self_attn_block(x_query)

        return x_query


class UpSampleBlock(nn.Module):
    """Upsampling block using ConvTranspose2d"""
    def __init__(self, in_channels, out_channels, scale_factor=2):
        super().__init__()
        # Ensure kernel_size and stride match scale_factor for simple upsampling
        self.conv_transpose = nn.ConvTranspose2d(in_channels, out_channels, kernel_size=scale_factor, stride=scale_factor)
        # Using GroupNorm as an alternative, potentially more stable than LayerNorm on spatial features
        self.norm = nn.GroupNorm(num_groups=max(1, out_channels // 32), num_channels=out_channels) # Example GroupNorm
        self.act = nn.GELU()

    def forward(self, x):
        # Input x expected shape: [N, C, H, W]
        x = self.conv_transpose(x)
        x = self.norm(x)
        x = self.act(x)
        return x

# Hierarchical Decoder (Inspired by U-Net and Hi-End-MAE concepts - Using Cross Attention)
class HierarchicalDecoder(nn.Module):
    def __init__(self, encoder_dims, decoder_embed_dim, decoder_depths, decoder_num_heads, mlp_ratio=4., qkv_bias=True, norm_layer=nn.LayerNorm, patch_size=4, in_chans=1, drop_path_rate=0.):
        super().__init__()
        # 首先保存encoder_dims，确保它在整个类中可用
        self.encoder_dims = list(encoder_dims)  # 创建一个副本，避免引用问题
        print(f"HierarchicalDecoder.__init__: encoder_dims = {self.encoder_dims}")

        self.patch_size = patch_size
        self.in_chans = in_chans
        self.num_stages = len(self.encoder_dims) # Number of encoder stages providing features

        # Upsampling layers
        self.upsample_blocks = nn.ModuleList()
        # Decoder blocks (CrossAttention + SelfAttention + MLP) for each stage
        self.decoder_stages = nn.ModuleList()
        # Linear projections for encoder features (Key/Value context) to match decoder dimension at each stage
        self.encoder_projections = nn.ModuleList()

        current_dim = decoder_embed_dim # Start with the dimension after initial projection from latent_full

        # Stochastic depth decay rule
        dpr = [x.item() for x in torch.linspace(0, drop_path_rate, sum(decoder_depths))]
        block_idx = 0

        # Build decoder stages from deep to shallow
        for i in range(self.num_stages - 1, -1, -1): # Iterate encoder stages in reverse
            encoder_dim = encoder_dims[i]
            stage_depth = decoder_depths[i] # Depth for this decoder stage
            stage_heads = decoder_num_heads[i] # Heads for this decoder stage

            # 1. Upsampling Block (except for the deepest stage)
            if i < self.num_stages - 1:
                # Upsample from previous decoder stage dim to current stage dim
                # Keep dimension consistent during upsampling
                upsample = UpSampleBlock(in_channels=current_dim, out_channels=current_dim, scale_factor=2)
                self.upsample_blocks.insert(0, upsample) # Prepend to list
            else:
                 self.upsample_blocks.insert(0, nn.Identity()) # No upsampling for the first stage

            # 2. Encoder Feature Projection (for Key/Value context)
            # Project encoder feature dim to match the current decoder dimension
            encoder_proj = nn.Linear(encoder_dim, current_dim, bias=True) if encoder_dim != current_dim else nn.Identity()
            self.encoder_projections.insert(0, encoder_proj) # Prepend

            # 3. Decoder Stage Blocks (CrossAttention + SelfAttention)
            stage_blocks = nn.ModuleList()
            for j in range(stage_depth):
                 stage_blocks.append(
                     DecoderCrossAttentionBlock(
                         dim=current_dim, num_heads=stage_heads, mlp_ratio=mlp_ratio, qkv_bias=qkv_bias,
                         drop=0., attn_drop=0., drop_path=dpr[block_idx + j], norm_layer=norm_layer
                     )
                 )
            self.decoder_stages.insert(0, stage_blocks) # Prepend
            block_idx += stage_depth

            # Note: current_dim remains the same across stages in this design

        # Final prediction head
        self.decoder_norm = norm_layer(current_dim) # Norm after last stage
        self.decoder_pred = nn.Linear(current_dim, patch_size**2 * in_chans, bias=True)

        print(f"Initialized HierarchicalDecoder (Cross-Attention) with {self.num_stages} stages.")
        print(f"Decoder stage depths: {decoder_depths}")
        print(f"Decoder stage heads: {decoder_num_heads}")


    def forward(self, x_latent, encoder_features, initial_grid_size, final_grid_size):
        # x_latent: Projected final encoder output [N, L_final, D_decoder]
        # encoder_features: List of intermediate features [N, C, H, W] from encoder stages (shallow to deep)
        # initial_grid_size: (H_initial, W_initial)
        # final_grid_size: (H_final, W_final)

        N = x_latent.shape[0]
        D_decoder = x_latent.shape[-1]

        # Reshape latent to sequence: [N, L_final, D_decoder]
        x = x_latent # Start with sequence format

        # Decoder path (from deep to shallow)
        for i in range(self.num_stages):
            stage_blks = self.decoder_stages[i]
            upsample_blk = self.upsample_blocks[i]

            # 确定编码器特征和投影层的索引
            # 这个索引应该随着i从0到num_stages-1，从深层(num_stages-1)到浅层(0)
            current_processing_depth_idx = self.num_stages - 1 - i

            # 使用相同的索引获取投影层和编码器特征
            encoder_proj_blk = self.encoder_projections[current_processing_depth_idx]

            # 添加调试信息
            print(f"Stage {i}: Processing encoder feature at depth {current_processing_depth_idx}")
            # 检查self.encoder_dims是否存在
            if not hasattr(self, 'encoder_dims'):
                print("ERROR: self.encoder_dims attribute is missing!")
                # 创建一个占位符，避免后续代码崩溃
                print("Creating placeholder encoder_dims based on encoder_features")
                # 尝试从encoder_features推断维度
                try:
                    inferred_dims = [feat.shape[1] for feat in encoder_features]
                    self.encoder_dims = inferred_dims
                    print(f"Inferred encoder_dims: {self.encoder_dims}")
                except Exception as e:
                    print(f"Failed to infer dimensions: {e}")
                    # 使用num_stages创建占位符
                    self.encoder_dims = [512] * self.num_stages
                    print(f"Using default placeholder: {self.encoder_dims}")

            print(f"      Encoder feature dim: {self.encoder_dims[current_processing_depth_idx]}")
            print(f"      Projection layer: input_dim={encoder_proj_blk.in_features if isinstance(encoder_proj_blk, nn.Linear) else 'Identity'}, output_dim={encoder_proj_blk.out_features if isinstance(encoder_proj_blk, nn.Linear) else 'Identity'}")

            # 1. Upsample (if not the first stage) - Operates on spatial, so reshape needed
            if not isinstance(upsample_blk, nn.Identity):
                # Calculate H, W of the feature map *before* upsampling
                # The spatial size doubles at each stage going from deep to shallow
                if i == 0:
                    # For the first upsampling stage, use final_grid_size directly
                    H_prev, W_prev = final_grid_size
                else:
                    # For subsequent stages, calculate based on previous upsampling
                    scale_factor = 2**i  # Each stage doubles the size
                    H_prev = final_grid_size[0] * scale_factor
                    W_prev = final_grid_size[1] * scale_factor

                # Get current dimensions
                C_prev = x.shape[-1]
                L_curr = x.shape[1]

                # Debug print to check dimensions
                print(f"Stage {i}: Reshaping to H_prev={H_prev}, W_prev={W_prev}, L_curr={L_curr}")

                # Check if dimensions match
                if H_prev * W_prev != L_curr:
                    # If dimensions don't match, adjust spatial dimensions
                    H_prev = int(math.sqrt(L_curr))
                    W_prev = H_prev
                    print(f"  Dimension mismatch! Adjusted to H_prev=W_prev={H_prev}")
                # Reshape sequence to spatial [N, C, H, W] before upsampling
                x = x.transpose(1, 2).reshape(N, C_prev, H_prev, W_prev)
                x = upsample_blk(x) # Upsamples spatially, dim remains current_dim
                # Reshape back to sequence [N, L_new, C_curr]
                N, C_curr, H_curr, W_curr = x.shape
                x = x.permute(0, 2, 3, 1).reshape(N, H_curr * W_curr, C_curr)

            # 2. Prepare Encoder Skip Feature (Project and Reshape to Sequence)
            # 使用之前计算的索引获取编码器特征
            skip_feature = encoder_features[current_processing_depth_idx] # Get feature [N, C_enc, H_enc, W_enc]
            N_skip, C_enc, H_enc, W_enc = skip_feature.shape
            skip_feature_seq = skip_feature.permute(0, 2, 3, 1).reshape(N_skip, H_enc * W_enc, C_enc) # -> [N, L_enc, C_enc]

            # 添加调试信息
            print(f"      Skip feature shape: {skip_feature.shape}")
            print(f"      Skip feature sequence shape: {skip_feature_seq.shape}")
            if isinstance(encoder_proj_blk, nn.Linear):
                 skip_feature_seq = encoder_proj_blk(skip_feature_seq) # -> [N, L_enc, C_curr]

            # 3. Apply Decoder Stage Blocks (CrossAttention + SelfAttention)
            # x is the query (decoder state), skip_feature_seq provides key/value context
            for blk in stage_blks:
                x = blk(x, skip_feature_seq) # Pass both query and kv context

        # Final processing after last stage (output should be sequence [N, L_initial, C_final])
        # x should already be in sequence format [N, L_initial, current_dim] after the last stage

        # Apply final norm and prediction head
        x = self.decoder_norm(x)
        pred = self.decoder_pred(x) # [N, L_initial, patch_size^2 * C]

        return pred


# Window Masking (Based on Swin MAE paper / Zian-Xu implementation idea)
# This version replaces masked patches with a mask token.
# --------------------------------------------------------
def window_masking(x: torch.Tensor, mask_token: nn.Parameter, window_size: int, mask_ratio: float):
    """
    Performs window-level masking and replaces masked patches with mask_token.
    Assumes square images and window partitioning.
    x: [N, L, D] input sequence after patch embedding + pos embedding
    mask_token: [1, 1, D] the learnable mask token
    window_size: int, size of the masking window (e.g., 4 or 7)
    mask_ratio: float, e.g., 0.75
    """
    N, L, D = x.shape
    H_patch = W_patch = int(L**0.5) # Grid size HxW
    assert H_patch * W_patch == L, "Input sequence length L must be a perfect square."
    # Ensure window_size is valid before assertion
    if not isinstance(window_size, int) or window_size <= 0:
        raise ValueError(f"window_size must be a positive integer, got {window_size}")
    if H_patch % window_size != 0 or W_patch % window_size != 0:
         # Option 1: Raise error if not divisible
         # raise ValueError(f"Patch grid size ({H_patch}x{W_patch}) must be divisible by window size ({window_size})")
         # Option 2: Adjust window size or masking strategy (more complex)
         # For now, let's print a warning and proceed, potentially with uneven masking at edges
         print(f"Warning: Patch grid size ({H_patch}x{W_patch}) not perfectly divisible by window size ({window_size}). Masking might be uneven.")
         # Adjust num_windows calculation to handle non-divisible cases (e.g., using ceil)
         num_windows_h = math.ceil(H_patch / window_size)
         num_windows_w = math.ceil(W_patch / window_size)
    else:
         num_windows_h = H_patch // window_size
         num_windows_w = W_patch // window_size

    num_windows = num_windows_h * num_windows_w

    # Generate noise and decide which windows to keep (or mask)
    len_keep_windows = int(num_windows * (1 - mask_ratio))
    noise = torch.rand(N, num_windows, device=x.device)
    ids_shuffle_windows = torch.argsort(noise, dim=1) # Indices of windows, shuffled
    ids_restore_windows = torch.argsort(ids_shuffle_windows, dim=1)

    # Generate the window-level mask: 0 is keep, 1 is remove/mask
    mask_windows = torch.ones(N, num_windows, device=x.device)
    mask_windows[:, :len_keep_windows] = 0
    mask_windows = torch.gather(mask_windows, dim=1, index=ids_restore_windows) # Unshuffle [N, num_windows]

    # Expand window mask to patch mask
    mask_windows_reshaped = mask_windows.reshape(N, num_windows_h, num_windows_w)
    # Use repeat_interleave, but handle potential size mismatch if not divisible
    mask_patches = mask_windows_reshaped.repeat_interleave(window_size, dim=1).repeat_interleave(window_size, dim=2)
    # Crop the expanded mask to the actual patch grid size if needed
    mask_patches = mask_patches[:, :H_patch, :W_patch] # Ensure correct size
    mask_patches = mask_patches.reshape(N, L) # [N, L] (0 is keep, 1 is mask)


    # Replace masked patches with mask_token
    x_masked = x.clone()
    mask_bool = mask_patches.bool().unsqueeze(-1).expand_as(x) # Expand mask to match x's dimensions
    x_masked = torch.where(mask_bool, mask_token.type_as(x), x_masked) # Replace where mask is True

    return x_masked, mask_patches # Return sequence with mask tokens and the patch mask

# --------------------------------------------------------
# Enhanced Perceptual Loss using ResNet18 for CT images (Modified)
# --------------------------------------------------------
class ResNetPerceptualLoss(nn.Module):
    def __init__(self, feature_layer_names=['layer2', 'layer3'], use_ct_norm=True, requires_grad=False): # Default to shallower layers, add norm flag
        super().__init__()
        # 加载预训练的ResNet18
        resnet = models.resnet18(weights=models.ResNet18_Weights.IMAGENET1K_V1)
        self.use_ct_norm = use_ct_norm # Flag for custom CT normalization

        # 修改第一层卷积以接受单通道输入
        original_conv = resnet.conv1
        resnet.conv1 = nn.Conv2d(1, 64, kernel_size=7, stride=2, padding=3, bias=False)

        # 初始化新的卷积层权重 - 使用原始权重的平均值
        with torch.no_grad():
            resnet.conv1.weight.data = original_conv.weight.data.mean(dim=1, keepdim=True)

        # 定义要提取的特征层 (using names now)
        self.feature_layer_names = feature_layer_names
        # Define weights based on the chosen layers (example weights, might need tuning)
        default_weights = {'layer1': 1.0, 'layer2': 0.8, 'layer3': 0.6, 'layer4': 0.4}
        self.layer_weights = [default_weights.get(name, 0.5) for name in self.feature_layer_names] # Get weights for specified layers

        # 提取特征层
        self.features = nn.ModuleDict()
        current_model = nn.Sequential()

        # 添加初始层
        current_model.add_module('conv1', resnet.conv1)
        current_model.add_module('bn1', resnet.bn1)
        current_model.add_module('relu', resnet.relu)
        current_model.add_module('maxpool', resnet.maxpool)

        # 添加ResNet的各个层 based on feature_layer_names
        if 'layer1' in self.feature_layer_names:
            self.features['layer1'] = nn.Sequential(current_model, resnet.layer1)
        # Always advance current_model regardless of whether the layer is used for loss
        current_model = nn.Sequential(current_model, resnet.layer1)

        if 'layer2' in self.feature_layer_names:
            self.features['layer2'] = nn.Sequential(current_model, resnet.layer2)
        current_model = nn.Sequential(current_model, resnet.layer2)

        if 'layer3' in self.feature_layer_names:
            self.features['layer3'] = nn.Sequential(current_model, resnet.layer3)
        current_model = nn.Sequential(current_model, resnet.layer3)

        if 'layer4' in self.feature_layer_names:
            self.features['layer4'] = nn.Sequential(current_model, resnet.layer4)
        # current_model = nn.Sequential(current_model, resnet.layer4) # Not needed if layer4 is the last

        if not self.features:
             raise ValueError(f"No valid ResNet feature layers specified in {self.feature_layer_names}")

        # 冻结参数
        if not requires_grad:
            for param in self.parameters():
                param.requires_grad = False

        self.eval()  # 设置为评估模式
        self.criterion = nn.L1Loss(reduction='mean')
        print(f"Initialized ResNetPerceptualLoss using layers: {self.feature_layer_names} with weights {self.layer_weights}. CT norm: {self.use_ct_norm}")

    def _normalize_ct(self, x):
        """归一化CT图像以适应ResNet (Instance-wise MinMax after [0,1] scaling)
        Input: Tensor in range [-1, 1]
        Output: Tensor in range [0, 1] (instance normalized)
        """
        # 从[-1, 1]转换到[0, 1]
        x = (x + 1.0) / 2.0

        # 应用CT图像特定的对比度增强 (Instance-wise Min-Max scaling)
        x_min = x.min(dim=2, keepdim=True)[0].min(dim=3, keepdim=True)[0]
        x_max = x.max(dim=2, keepdim=True)[0].max(dim=3, keepdim=True)[0]
        denominator = x_max - x_min
        # Avoid division by zero for blank images/patches
        x_norm = torch.where(denominator > 1e-8, (x - x_min) / denominator, torch.zeros_like(x))

        return x_norm

    def forward(self, x, y, mask=None):
        """计算感知损失
        Args:
            x, y: 输入图像 [N, 1, H, W] in range [-1, 1]
            mask: 可选掩码 [N, 1, H, W]，1表示掩码区域
        """
        # Apply normalization if enabled
        if self.use_ct_norm:
            x = self._normalize_ct(x)
            y = self._normalize_ct(y)
        else:
            # If not using CT norm, just scale to [0, 1] as ResNet expects non-negative input
            x = (x + 1.0) / 2.0
            y = (y + 1.0) / 2.0

        total_loss = 0.0

        # 计算每个特征层的损失
        for i, layer_name in enumerate(self.feature_layer_names): # Use names here
            layer = self.features[layer_name]
            x_feat = layer(x)
            y_feat = layer(y)

            # 应用掩码（如果提供）
            if mask is not None:
                # 调整掩码大小以匹配特征图
                mask_resized = F.interpolate(mask, size=x_feat.shape[2:], mode='nearest')
                # 计算掩码区域的损失
                layer_loss = self.criterion(x_feat * mask_resized, y_feat * mask_resized)
            else:
                layer_loss = self.criterion(x_feat, y_feat)

            # 应用层权重
            weight = self.layer_weights[i]
            total_loss += weight * layer_loss

        return total_loss


# --------------------------------------------------------
# Based on MAE implementation by Meta Platforms, Inc. and Swin Transformer
# --------------------------------------------------------
class MaskedAutoencoderSwin(nn.Module):
    """ Masked Autoencoder with Swin Transformer backbone (Low-efficiency version based on Swin MAE paper) """
    def __init__(self, img_size=256, patch_size=4, in_chans=1, # Swin uses patch_size=4 typically
                 embed_dim=96, depths=[2, 2, 6, 2], num_heads=[3, 6, 12, 24], # Swin-T defaults
                 window_size=7, mlp_ratio=4., qkv_bias=True, qk_scale=None,
                 drop_rate=0., attn_drop_rate=0., drop_path_rate=0.1,
                 norm_layer=nn.LayerNorm, ape=False, patch_norm=True, # Swin specific params
                 decoder_embed_dim=512, # Base dimension for decoder stages
                 decoder_depths=[2, 2, 6, 2], # Depth per decoder stage (mirrors encoder default)
                 decoder_num_heads=[24, 12, 6, 3], # Heads per decoder stage (mirrors encoder default, reversed)
                 masking_window_size=4, # Size of window for masking (e.g., 4x4 patches)
                 decoder_mlp_ratio=4., decoder_norm_layer=nn.LayerNorm, # Decoder specific norm/mlp
                 norm_pix_loss=False,
                 perceptual_loss_weight=0.01, # Default weight lowered
                 perc_layers_resnet=['layer2', 'layer3'], # Configurable layers
                 perc_norm_ct_resnet=True, # Configurable normalization
                 ssim_loss_weight=0.05): # Add SSIM loss weight
        super().__init__()

        self.patch_size = patch_size # Store patch size
        self.in_chans = in_chans # Store in_chans
        self.masking_window_size = masking_window_size # Store masking window size
        self.perceptual_loss_weight = perceptual_loss_weight # Store perceptual loss weight
        self.ssim_loss_weight = ssim_loss_weight # Store SSIM loss weight


        # --------------------------------------------------------------------------
        # Swin MAE encoder specifics
        self.encoder = timm.create_model(
            'swin_tiny_patch4_window7_224', # Reverted to standard tiny model name
            pretrained=False,
            in_chans=in_chans,
            img_size=img_size, # Timm handles img_size mismatch from 224
            patch_size=patch_size, # Should be 4 for standard Swin
            embed_dim=embed_dim,
            depths=depths,
            num_heads=num_heads,
            window_size=window_size, # Attention window size
            mlp_ratio=mlp_ratio,
            qkv_bias=qkv_bias,
            qk_scale=qk_scale,
            drop_rate=drop_rate,
            attn_drop_rate=attn_drop_rate,
            drop_path_rate=drop_path_rate,
            norm_layer=norm_layer,
            ape=ape, # Absolute Position Embedding
            patch_norm=patch_norm,
            num_classes=0, # No head
            global_pool='' # No pooling
        )

        # --- MAE specific additions/modifications ---
        # Store encoder stage dimensions (needed for decoder skip connections)
        self.encoder_dims = [int(embed_dim * 2**i) for i in range(len(depths))]
        self.actual_encoder_output_dim = self.encoder_dims[-1]
        print(f"Encoder stage dims: {self.encoder_dims}")
        print(f"Final encoder output dim: {self.actual_encoder_output_dim}")

        # Debug print to help diagnose dimension issues
        print(f"Command line swin_embed_dim: {embed_dim}")
        print(f"Command line decoder_embed_dim: {decoder_embed_dim}")


        self.patch_embed = self.encoder.patch_embed
        self.embed_dim = embed_dim # Store embed_dim

        # --- Use grid_size from patch_embed ---
        self.actual_grid_size = self.patch_embed.grid_size
        self.num_patches = self.actual_grid_size[0] * self.actual_grid_size[1]
        print(f"Using grid_size from patch_embed: {self.actual_grid_size}, num_patches: {self.num_patches}")

        # --- Calculate final grid size and patch count after encoder downsampling ---
        # This IS needed because the decoder receives features from the encoder's final resolution
        downsample_factor = 2**(len(depths) - 1) # Number of patch merging stages = len(depths) - 1
        self.grid_final = (self.actual_grid_size[0] // downsample_factor, self.actual_grid_size[1] // downsample_factor)
        self.num_patches_final = self.grid_final[0] * self.grid_final[1] # Calculate num_patches_final
        print(f"Calculated final grid size after encoder: {self.grid_final}, num_patches_final: {self.num_patches_final}") # Print both


        # --- Absolute Position Embedding (APE) for Encoder ---
        if ape:
             self.pos_embed = getattr(self.encoder, 'absolute_pos_embed', None)
             if self.pos_embed is not None:
                 print("Using absolute positional embedding from Swin config (handled internally by timm model).")
             else:
                 print("Warning: ape=True but Swin model doesn't seem to have 'absolute_pos_embed'. Creating custom APE.")
                 self.pos_embed = nn.Parameter(torch.zeros(1, self.num_patches, embed_dim))
        else:
            self.pos_embed = nn.Parameter(torch.zeros(1, self.num_patches, embed_dim)) # Use self.num_patches
            print("Created absolute positional embedding for Swin MAE (ape=False).")


        # --------------------------------------------------------------------------
        # MAE Decoder Selection
        # Initial projection from final encoder output dim to decoder base dim
        # Print dimensions for debugging
        print(f"Debug - Encoder dimensions: {self.encoder_dims}")
        print(f"Debug - Actual encoder output dim: {self.actual_encoder_output_dim}")
        print(f"Debug - Requested decoder_embed_dim: {decoder_embed_dim}")

        # Store the original decoder_embed_dim for reference
        self.decoder_embed_dim = decoder_embed_dim

        # Create the projection layer from encoder output to decoder input
        # The input dimension must be the actual encoder output dimension
        # For Linear layer, weight shape is (out_features, in_features)
        # So for input (N, L, D_in) -> output (N, L, D_out), we need weight shape (D_out, D_in)
        self.decoder_embed = nn.Linear(self.actual_encoder_output_dim, decoder_embed_dim, bias=True)
        print(f"Debug - Created decoder_embed with input dim {self.actual_encoder_output_dim} and output dim {decoder_embed_dim}")
        print(f"Debug - decoder_embed weight shape: {self.decoder_embed.weight.shape}")

        # Instantiate the Hierarchical Decoder
        # TODO: Add argument to switch between simple and hierarchical decoder?
        print("Using Hierarchical Decoder")

        # Adjust decoder_num_heads to be compatible with decoder_embed_dim
        adjusted_decoder_num_heads = []
        for head_count in decoder_num_heads:
            if self.decoder_embed_dim % head_count != 0:
                # Find the closest divisor of decoder_embed_dim
                divisors = [i for i in range(1, self.decoder_embed_dim + 1) if self.decoder_embed_dim % i == 0]
                closest_divisor = min(divisors, key=lambda x: abs(x - head_count))
                print(f"Warning: decoder_num_heads value {head_count} is not compatible with decoder_embed_dim {self.decoder_embed_dim}.")
                print(f"Adjusting to {closest_divisor}.")
                adjusted_decoder_num_heads.append(closest_divisor)
            else:
                adjusted_decoder_num_heads.append(head_count)

        if adjusted_decoder_num_heads != decoder_num_heads:
            print(f"Adjusted decoder_num_heads: {adjusted_decoder_num_heads}")
            decoder_num_heads = adjusted_decoder_num_heads

        self.decoder = HierarchicalDecoder(
            encoder_dims=self.encoder_dims, # Pass encoder stage dims
            decoder_embed_dim=self.decoder_embed_dim,  # Use adjusted value
            decoder_depths=decoder_depths, # Pass per-stage depths
            decoder_num_heads=decoder_num_heads, # Pass adjusted heads
            mlp_ratio=decoder_mlp_ratio,
            qkv_bias=qkv_bias,
            norm_layer=decoder_norm_layer,
            patch_size=patch_size,
            in_chans=in_chans,
            drop_path_rate=drop_path_rate # Pass drop path rate to decoder
        )

        # --- Mask Tokens ---
        # Mask token for the ENCODER (dimension: embed_dim) - used in window_masking
        self.encoder_mask_token = nn.Parameter(torch.zeros(1, 1, embed_dim))
        print(f"Initialized encoder mask_token with size: {self.encoder_mask_token.shape}")
        # Note: HierarchicalDecoder does not explicitly use a decoder mask token

        # --- Positional Embedding (for Decoder) ---
        # Positional embedding is often added *before* the final prediction layer.
        # Since HierarchicalDecoder includes the final prediction layer,
        # we might not need a separate decoder_pos_embed here, or it should be handled inside the decoder.
        # Let's remove the separate decoder_pos_embed for now, assuming the hierarchical structure captures position implicitly or handles it internally.
        # self.decoder_pos_embed = nn.Parameter(torch.zeros(1, self.num_patches, decoder_embed_dim), requires_grad=False)
        # print(f"Initialized decoder_pos_embed with size: {self.decoder_pos_embed.shape}")
        # decoder_pos_embed_data = get_2d_sincos_pos_embed(self.decoder_pos_embed.shape[-1], self.actual_grid_size[0], cls_token=False)
        # self.decoder_pos_embed.data.copy_(torch.from_numpy(decoder_pos_embed_data).float().unsqueeze(0))
        # print(f"Initialized decoder positional embedding using actual_grid_size: {self.actual_grid_size}")
        print("Decoder positional embedding is handled within the Hierarchical Decoder or omitted.")

        # --------------------------------------------------------------------------

        self.norm_pix_loss = norm_pix_loss

        # Instantiate Enhanced Perceptual Loss using ResNet18 (ensure it's on the correct device later)
        if self.perceptual_loss_weight > 0:
             # Use ResNet18 for better CT image feature extraction
             self.perceptual_loss = ResNetPerceptualLoss(
                 feature_layer_names=perc_layers_resnet,
                 use_ct_norm=perc_norm_ct_resnet,
                 requires_grad=False
             )
             # Print statement moved inside ResNetPerceptualLoss __init__
        else:
             self.perceptual_loss = None

        self.ssim_loss_weight = ssim_loss_weight # Store SSIM weight
        if self.ssim_loss_weight > 0:
            print(f"Initialized SSIM Loss with weight {self.ssim_loss_weight}")


        self.initialize_weights() # Call init after defining all layers

    def initialize_weights(self):
        # Initialize custom APE if we created it
        if isinstance(self.pos_embed, nn.Parameter): # Check if it's the one we created
             pos_embed_data = get_2d_sincos_pos_embed(self.pos_embed.shape[-1], self.actual_grid_size[0], cls_token=False)
             self.pos_embed.data.copy_(torch.from_numpy(pos_embed_data).float().unsqueeze(0))
             print("Initialized custom absolute positional embedding for encoder.")

        # Initialize encoder mask_token
        torch.nn.init.normal_(self.encoder_mask_token, std=.02)
        print("Initialized encoder mask token.")

        # Initialize linear layers and layer norms (excluding already initialized embeddings)
        self.apply(self._init_weights)

    def _init_weights(self, m):
        if isinstance(m, nn.Linear):
            torch.nn.init.xavier_uniform_(m.weight)
            if isinstance(m, nn.Linear) and m.bias is not None:
                nn.init.constant_(m.bias, 0)
        elif isinstance(m, nn.LayerNorm):
            nn.init.constant_(m.bias, 0)
            nn.init.constant_(m.weight, 1.0)

    # --- Encoder following Swin MAE paper (modified to return intermediate features) ---
    def forward_encoder(self, x, mask_ratio):
        # x: [N, C, H, W]
        intermediate_features = [] # List to store features from each stage

        # 1. Patch Embedding
        x = self.patch_embed(x) # Output is likely [N, H', W', D]
        # Ensure output is [N, L, D] before unpacking shape
        if x.dim() == 4:
            x = x.flatten(1, 2) # -> [N, H'*W', D] = [N, L, D]
        N, L, D = x.shape # Now unpack the 3D shape

        # 2. Add Position Embedding (if applicable)
        if self.pos_embed is not None:
            if self.pos_embed.shape[1] != L:
                 print(f"Warning: Position embedding size mismatch (Expected {L}, Got {self.pos_embed.shape[1]}). Attempting interpolation.")
                 pos_embed_resized = F.interpolate(
                     self.pos_embed.reshape(1, int(self.pos_embed.shape[1]**0.5), int(self.pos_embed.shape[1]**0.5), D).permute(0, 3, 1, 2),
                     size=self.actual_grid_size, mode='bicubic', align_corners=False,
                 ).permute(0, 2, 3, 1).reshape(1, L, D)
                 x = x + pos_embed_resized.type_as(x)
            else:
                 x = x + self.pos_embed.type_as(x)

        # 3. Window Masking (Replaces masked patches with mask_token)
        # Use the encoder_mask_token (which has dimension D = embed_dim)
        mask_token_to_use = self.encoder_mask_token.type_as(x)

        # Use the masking_window_size defined in init
        x_masked, mask = window_masking(x, mask_token_to_use, self.masking_window_size, mask_ratio)
        # x_masked: [N, L, D] (sequence with encoder_mask_tokens)
        # mask: [N, L] (0 is keep, 1 is mask)

        # 4. Pass the *full sequence* (with mask tokens) through Swin stages
        # This is the low-efficiency approach from the paper.
        # We need to pass the sequence with mask tokens through the Swin layers manually.
        # First, reshape back to spatial if the layers expect it.
        # Check if timm Swin layers expect [N, H, W, C] or [N, L, C]
        # Assuming they expect spatial input based on the original code structure
        H_grid, W_grid = self.actual_grid_size
        x_spatial_masked = x_masked.reshape(N, H_grid, W_grid, D)

        # Pass through Swin layers and store intermediate outputs
        for i, layer in enumerate(self.encoder.layers):
            x_spatial_masked = layer(x_spatial_masked) # Output: [N, H_out, W_out, C_out]
            # Store the feature map *before* the final norm of the encoder
            # Reshape to [N, C, H, W] for easier handling in decoder
            intermediate_features.append(x_spatial_masked.permute(0, 3, 1, 2).contiguous())
            # print(f"Encoder stage {i} output shape: {x_spatial_masked.shape}") # Reduce print frequency


        # Flatten back to sequence for final normalization
        N_out, H_out, W_out, C_out = x_spatial_masked.shape # Shape after last layer
        x_seq_masked = x_spatial_masked.view(N_out, H_out * W_out, C_out) # Flatten to [N, L_final, C_out]

        # Apply final encoder normalization
        latent_full = self.encoder.norm(x_seq_masked) # [N, L_final, D_encoder_output]

        # Debug print to check latent_full shape
        print(f"Debug - latent_full shape: {latent_full.shape}")
        print(f"Debug - Expected shape: [N, {self.num_patches_final}, {self.actual_encoder_output_dim}]")

        # Return final latent, initial patch mask, and intermediate features
        return latent_full, mask, intermediate_features

    # --- Decoder ---
    def forward_decoder(self, latent_full, intermediate_features, mask): # Now accepts intermediate features
        # Debug print to check latent_full shape again
        print(f"Debug - In forward_decoder - latent_full shape: {latent_full.shape}")
        print(f"Debug - In forward_decoder - self.decoder_embed weight shape: {self.decoder_embed.weight.shape}")

        # Check if latent_full shape matches expected input shape for decoder_embed
        if latent_full.shape[1] * latent_full.shape[2] != self.decoder_embed.weight.shape[0] * self.decoder_embed.weight.shape[1]:
            print(f"Warning: Shape mismatch between latent_full and decoder_embed weight!")
            print(f"Attempting to reshape latent_full...")

            # Try to reshape latent_full to match expected input shape
            N, L, D = latent_full.shape
            if L * D == self.num_patches_final * self.actual_encoder_output_dim:
                latent_full = latent_full.reshape(N, self.num_patches_final, self.actual_encoder_output_dim)
                print(f"Reshaped latent_full to: {latent_full.shape}")

        # 1. Project final encoder features to decoder dimension
        try:
            x = self.decoder_embed(latent_full) # [N, L_final, D_decoder]
            print(f"Debug - After projection - x shape: {x.shape}")
        except Exception as e:
            print(f"Error in decoder_embed: {e}")
            # Try a different approach if the original fails
            print("Trying alternative approach...")
            N, L, D = latent_full.shape
            x = latent_full.reshape(N * L, D)
            x = self.decoder_embed(x)
            x = x.reshape(N, L, -1)
            print(f"Alternative approach - x shape: {x.shape}")

        # 2. Pass projected latent and intermediate features through Hierarchical Decoder
        # The decoder handles upsampling, fusion (via cross-attention), and final prediction internally.
        pred_seq = self.decoder(x, intermediate_features, self.actual_grid_size, self.grid_final) # Output: [N, L_initial, p*p*C]

        return pred_seq # Return predictions for all initial patches [N, L_initial, p*p*C]

    def patchify(self, imgs):
        """ imgs: (N, C, H, W) -> (N, L, patch_size**2 * C) """
        p = self.patch_size
        assert imgs.shape[2] == imgs.shape[3] and imgs.shape[2] % p == 0
        h = w = imgs.shape[2] // p
        c = self.in_chans # Use self.in_chans
        x = imgs.reshape(shape=(imgs.shape[0], c, h, p, w, p))
        x = torch.einsum('nchpwq->nhwpqc', x)
        x = x.reshape(shape=(imgs.shape[0], h * w, p**2 * c))
        return x

    def unpatchify(self, x):
        """ x: (N, L, patch_size**2 * C) -> (N, C, H, W) """
        p = self.patch_size
        h, w = self.actual_grid_size # Use initial grid size for unpatchify
        assert h * w == x.shape[1], f"h*w ({h*w}) from grid_size does not match L ({x.shape[1]})"
        c = self.in_chans # Use self.in_chans
        assert x.shape[2] == p**2 * c, f"Decoder prediction dim {x.shape[2]} != p*p*C ({p**2 * c})"

        x = x.reshape(shape=(x.shape[0], h, w, p, p, c))
        x = torch.einsum('nhwpqc->nchpwq', x)
        imgs = x.reshape(shape=(x.shape[0], c, h * p, w * p))
        return imgs

    def forward_loss(self, imgs, pred, mask):
        """
        imgs: [N, C, H, W] - Original images
        pred: [N, L_initial, p*p*C] - Predicted patch values
        mask: [N, L_initial], 1 is remove/masked
        """
        target = self.patchify(imgs) # Target shape: [N, L_initial, p*p*C]
        if self.norm_pix_loss:
            mean = target.mean(dim=-1, keepdim=True)
            var = target.var(dim=-1, keepdim=True)
            target = (target - mean) / (var + 1.e-6)**.5

        loss = (pred - target) ** 2
        loss = loss.mean(dim=-1)  # [N, L_initial], mean loss per patch

        mask_sum = mask.sum()
        if mask_sum == 0:
             # Avoid division by zero if the mask is empty (should not happen with mask_ratio > 0)
             print("Warning: mask_sum is zero in forward_loss.")
             # Return zero loss for reconstruction if no patches are masked
             reconstruction_loss = torch.tensor(0.0, device=pred.device, requires_grad=True)
        else:
             reconstruction_loss = (loss * mask).sum() / mask_sum # Mean loss on removed patches

        # Calculate Enhanced Perceptual Loss (only if weight > 0)
        perc_loss = torch.tensor(0.0, device=pred.device)
        if self.perceptual_loss is not None and self.perceptual_loss_weight > 0:
            # Ensure perceptual loss module is on the correct device
            self.perceptual_loss = self.perceptual_loss.to(pred.device)

            # Unpatchify predictions and targets for perceptual loss input
            pred_img = self.unpatchify(pred)  # [N, C, H, W]

            # Create a mask image for focusing perceptual loss on masked regions
            # This is more effective than computing on the full image
            # In MAE, mask=1 means the patch is masked (removed)
            mask_img = mask.reshape(-1, *self.actual_grid_size).unsqueeze(1).float()  # [N, 1, H_grid, W_grid]
            # Upsample mask to match image resolution
            mask_img = F.interpolate(mask_img, size=(pred_img.shape[2], pred_img.shape[3]), mode='nearest')

            # Calculate perceptual loss focused on masked regions using ResNet18
            perc_loss = self.perceptual_loss(pred_img, imgs, mask=mask_img)

        # Calculate SSIM Loss (only if weight > 0)
        struct_loss = torch.tensor(0.0, device=pred.device)
        if self.ssim_loss_weight > 0:
            # SSIM expects input in range [0, 1] or [0, 255] etc.
            # Our loader outputs [-1, 1]. Scale to [0, 1] for SSIM.
            # Unpatchify if not already done for perceptual loss
            if perc_loss == 0.0: # Avoid unpatchifying twice
                 pred_img = self.unpatchify(pred)
            pred_img_scaled = (pred_img + 1.0) / 2.0
            imgs_scaled = (imgs + 1.0) / 2.0
            # Ensure channel dim is correct (N, C, H, W) - should be [N, 1, H, W]
            # Calculate SSIM (value is 0 to 1, higher is better)
            # Use data_range=1.0 because input is scaled to [0, 1]
            ssim_val = ssim(pred_img_scaled, imgs_scaled, data_range=1.0, size_average=True)
            # Convert SSIM value to loss (0 to 1, lower is better)
            struct_loss = 1.0 - ssim_val
            # Optional: Use MS-SSIM for multi-scale structural similarity
            # struct_loss = 1.0 - ms_ssim(pred_img_scaled, imgs_scaled, data_range=1.0, size_average=True)


        # Combine losses
        total_loss = reconstruction_loss \
                     + self.perceptual_loss_weight * perc_loss \
                     + self.ssim_loss_weight * struct_loss

        # Return individual losses for logging and debugging
        return total_loss, reconstruction_loss, perc_loss, struct_loss


    def forward(self, imgs, mask_ratio=0.75):
        # Forward pass with Hierarchical Decoder
        # Debug print to check input image shape
        print(f"Debug - Input image shape: {imgs.shape}")

        # 1. Patchify and encode
        latent_full, mask, intermediate_features = self.forward_encoder(imgs, mask_ratio)

        # Debug print to check latent_full and mask shapes
        print(f"Debug - After encoding - latent_full shape: {latent_full.shape}")
        print(f"Debug - After encoding - mask shape: {mask.shape if mask is not None else 'None'}")

        # 2. Decode and predict
        pred = self.forward_decoder(latent_full, intermediate_features, mask)

        # Debug print to check pred shape
        print(f"Debug - After decoding - pred shape: {pred.shape}")

        # 3. Calculate loss
        loss, recon_loss, perc_loss, ssim_loss = self.forward_loss(imgs, pred, mask) # Loss calculated on masked patches (now includes perceptual and SSIM)

        # Debug print to check loss values
        print(f"Debug - Loss values: total={loss:.4f}, recon={recon_loss:.4f}, perc={perc_loss:.4f}, ssim={ssim_loss:.4f}")

        return loss, pred, mask


# --- Training Function ---
# (setup_mae_training and train_epoch_mae remain largely the same)

def setup_mae_training(model_params, optimizer_params, device):
    """设置 MAE 训练的优化器和调度器"""
    lr = optimizer_params.get('lr', 1.5e-4) # Base LR
    weight_decay = optimizer_params.get('weight_decay', 0.05)
    optimizer = optim.AdamW(model_params, lr=lr, betas=(0.9, 0.95), weight_decay=weight_decay)
    # Cosine scheduler T_max depends on total epochs *after* warmup
    scheduler = CosineAnnealingLR(optimizer, T_max=optimizer_params['epochs_after_warmup'], eta_min=lr/100)
    scaler = torch.cuda.amp.GradScaler(enabled=optimizer_params.get('use_amp', True))
    return optimizer, scheduler, scaler

def train_epoch_mae(model, train_loader, optimizer, scaler, device, writer, epoch, total_epochs, base_lr, warmup_epochs, gradient_accumulation_steps=1, log_interval=50, use_amp=True): # Added gradient_accumulation_steps
    """MAE 训练循环 with warmup and gradient accumulation"""
    model.train()
    total_loss = 0
    accumulated_loss = 0.0 # Track loss over accumulation steps
    num_batches = len(train_loader)
    processed_batches = 0

    with tqdm(train_loader, unit="batch") as pbar:
        for batch_idx, imgs in enumerate(pbar):
            if imgs is None or imgs.nelement() == 0:
                print(f"Skipping empty batch at index {batch_idx}")
                continue

            global_step = epoch * num_batches + batch_idx
            # Adjust learning rate using linear warmup
            if epoch < warmup_epochs:
                num_warmup_steps = warmup_epochs * num_batches
                if num_warmup_steps > 0:
                    current_step = global_step + 1
                    lr_scale = min(1.0, float(current_step) / num_warmup_steps)
                    new_lr = base_lr * lr_scale
                    for param_group in optimizer.param_groups:
                        param_group['lr'] = new_lr
            # After warmup, the scheduler (stepped per epoch) handles decay.

            imgs = imgs.to(device, non_blocking=True)
            # optimizer.zero_grad() # Moved to after accumulation step

            with torch.cuda.amp.autocast(enabled=use_amp):
                loss, _, _ = model(imgs, mask_ratio=0.75) # Default mask ratio
                # Normalize loss for accumulation
                loss = loss / gradient_accumulation_steps

            # Accumulate scaled loss
            scaler.scale(loss).backward()

            current_loss_value = loss.item() * gradient_accumulation_steps # Log the non-normalized loss

            if math.isnan(current_loss_value):
                print(f"Warning: NaN loss detected at epoch {epoch+1}, batch {batch_idx}. Skipping gradient step.")
                # Skip optimizer step if loss is NaN, but zero grad for next accumulation cycle
                if (batch_idx + 1) % gradient_accumulation_steps == 0 or (batch_idx + 1) == num_batches:
                    optimizer.zero_grad(set_to_none=True) # Use set_to_none=True for potential efficiency
                continue

            accumulated_loss += current_loss_value # Accumulate for average calculation

            # Perform optimizer step after accumulating gradients
            if (batch_idx + 1) % gradient_accumulation_steps == 0 or (batch_idx + 1) == num_batches:
                scaler.step(optimizer)
                scaler.update()
                optimizer.zero_grad(set_to_none=True) # Zero gradients after step

            processed_batches += 1
            # Display the loss for the current batch (non-accumulated)
            pbar.set_postfix(loss=f"{current_loss_value:.4f}", lr=f"{optimizer.param_groups[0]['lr']:.1e}")

            # Log less frequently if accumulating gradients
            if global_step % (log_interval * gradient_accumulation_steps) == 0:
                 writer.add_scalar('Loss/batch', current_loss_value, global_step)
                 writer.add_scalar('LR', optimizer.param_groups[0]['lr'], global_step)

    # Calculate average loss based on accumulated loss and number of batches processed
    avg_loss = accumulated_loss / processed_batches if processed_batches > 0 else 0
    return avg_loss


def pretrain_mae(args): # Pass args directly
    """Swin MAE 自监督预训练函数"""
    # 设置随机种子以确保可重复性
    set_seed(args.seed)

    # 清理CUDA缓存，确保有足够的GPU内存
    clear_cuda_cache()

    # 打印CUDA信息
    if torch.cuda.is_available():
        print(f"CUDA设备数量: {torch.cuda.device_count()}")
        print(f"当前CUDA设备: {torch.cuda.current_device()}")
        print(f"CUDA设备名称: {torch.cuda.get_device_name(0)}")
        print(f"CUDA可用内存: {torch.cuda.get_device_properties(0).total_memory / 1024 / 1024 / 1024:.2f} GB")
    else:
        print("CUDA不可用，将使用CPU训练（速度会很慢）")

    # Calculate effective batch size
    effective_batch_size = args.batch_size * args.gradient_accumulation_steps
    print(f"Effective batch size: {args.batch_size} * {args.gradient_accumulation_steps} = {effective_batch_size}")

    device = torch.device('cuda' if torch.cuda.is_available() else 'cpu')
    print(f"Using device: {device}")
    print(f"Swin MAE Pretraining Parameters: {args}")

    os.makedirs(args.checkpoint_dir, exist_ok=True)
    os.makedirs(args.log_dir, exist_ok=True)
    writer = SummaryWriter(args.log_dir)

    # Instantiate Swin MAE model
    model = MaskedAutoencoderSwin(
        img_size=args.img_size,
        patch_size=args.patch_size,
        in_chans=args.in_chans,
        embed_dim=args.swin_embed_dim, # Use Swin specific embed_dim
        depths=args.swin_depths,
        num_heads=args.swin_num_heads,
        window_size=args.swin_window_size, # Attention window size
        masking_window_size=args.masking_window_size, # Pass masking window size
        mlp_ratio=4.0, # Standard MLP ratio
        norm_layer=nn.LayerNorm,
        ape=args.swin_ape, # Absolute Position Embedding flag
        patch_norm=True, # Usually True for Swin
        decoder_embed_dim=args.decoder_embed_dim,
        decoder_depths=args.decoder_depths, # Pass per-stage depths
        decoder_num_heads=args.decoder_num_heads, # Pass per-stage heads
        norm_pix_loss=args.norm_pix_loss,
        perceptual_loss_weight=args.perceptual_loss_weight, # Pass perceptual loss weight
        perc_layers_resnet=args.perc_layers_resnet,
        perc_norm_ct_resnet=args.perc_norm_ct_resnet,
        ssim_loss_weight=args.ssim_loss_weight,
        drop_path_rate=args.drop_path_rate # Pass drop path rate
    ).to(device)

    # 打印模型参数数量
    param_count = sum(p.numel() for p in model.parameters() if p.requires_grad)
    print(f"Swin MAE Model Parameters: {param_count:,}")

    # 模型创建后清理CUDA缓存
    clear_cuda_cache()
    print(f"模型已加载到{device}，参数量: {param_count/1000000:.2f}M")

    # Get MAE data loader
    train_loader = get_mae_loader(
        data_dir=args.data_dir,
        batch_size=args.batch_size,
        num_workers=args.num_workers,
        crop_size=args.img_size, # Crop size must match model img_size
        augment=True
        # Add clip_min/max here if needed by loader
    )

    # Setup optimizer and scheduler
    optimizer_params = {
        'lr': args.lr,
        'epochs_after_warmup': max(0, args.epochs - args.warmup_epochs), # Ensure non-negative T_max
        'weight_decay': args.weight_decay,
        'use_amp': args.use_amp
    }
    optimizer, scheduler, scaler = setup_mae_training(model.parameters(), optimizer_params, device)

    start_epoch = 0
    best_loss = float('inf')

    # Resume logic
    if args.resume and os.path.isfile(args.resume):
        print(f"Resuming from checkpoint: {args.resume}")
        checkpoint = torch.load(args.resume, map_location='cpu')
        # Use strict=False to handle potential mismatches if model structure changed slightly
        msg = model.load_state_dict(checkpoint['model_state_dict'], strict=False)
        print(f"Model load_state_dict message: {msg}")
        if 'optimizer_state_dict' in checkpoint:
             optimizer.load_state_dict(checkpoint['optimizer_state_dict'])
        if 'scheduler_state_dict' in checkpoint and hasattr(scheduler, 'load_state_dict'):
             # Check if T_max matches before loading scheduler state
             expected_T_max = max(0, args.epochs - args.warmup_epochs)
             if hasattr(scheduler, 'T_max') and scheduler.T_max == expected_T_max:
                 scheduler.load_state_dict(checkpoint['scheduler_state_dict'])
             else:
                 print(f"Warning: Scheduler T_max mismatch (expected {expected_T_max}, got {getattr(scheduler, 'T_max', 'N/A')}), not loading scheduler state.")
        start_epoch = checkpoint.get('epoch', 0)
        best_loss = checkpoint.get('loss', float('inf'))
        if args.use_amp and 'scaler_state_dict' in checkpoint and checkpoint['scaler_state_dict'] is not None:
            scaler.load_state_dict(checkpoint['scaler_state_dict'])
        print(f"Resumed from epoch {start_epoch}")
        # Adjust scheduler's last_epoch correctly after resuming
        if 'scheduler_state_dict' in checkpoint and hasattr(scheduler, 'load_state_dict') and hasattr(scheduler, 'T_max') and scheduler.T_max == expected_T_max:
             # Set last_epoch based on the resumed epoch, considering warmup
             scheduler.last_epoch = max(0, start_epoch - args.warmup_epochs) - 1


    # Training loop
    for epoch in range(start_epoch, args.epochs):
        print(f"\nEpoch {epoch+1}/{args.epochs}:")

        # 每5个epoch清理一次CUDA缓存，防止内存泄漏
        if epoch % 5 == 0:
            clear_cuda_cache()

        avg_loss = train_epoch_mae(
            model, train_loader, optimizer, scaler, device, writer, epoch,
            total_epochs=args.epochs, base_lr=args.lr, warmup_epochs=args.warmup_epochs,
            gradient_accumulation_steps=args.gradient_accumulation_steps, # Pass grad accum steps
            use_amp=args.use_amp
        )

        # Step the scheduler only *after* the warmup phase
        if epoch >= args.warmup_epochs:
             if hasattr(scheduler, 'step'):
                 scheduler.step()
             else:
                 print(f"Warning: Scheduler {type(scheduler)} does not have step method.")


        current_lr = optimizer.param_groups[0]['lr']
        print(f"Epoch {epoch+1}/{args.epochs} completed. Average Loss: {avg_loss:.6f}, Current LR: {current_lr:.6f}")
        writer.add_scalar('Loss/train_epoch', avg_loss, epoch)
        writer.add_scalar('LR_epoch', current_lr, epoch)

        is_best = avg_loss < best_loss
        if is_best:
            best_loss = avg_loss
            print(f"New best loss: {best_loss:.6f}")

        # Save checkpoint logic (consider saving args used for this run)
        if (epoch + 1) % args.save_interval == 0 or is_best:
             # Save args relevant to this specific model configuration
             saved_args = {k: v for k, v in vars(args).items()} # Already includes seed
             checkpoint_data = {
                 'epoch': epoch + 1,
                 'model_state_dict': model.state_dict(),
                 'optimizer_state_dict': optimizer.state_dict(),
                 'scheduler_state_dict': scheduler.state_dict() if hasattr(scheduler, 'state_dict') else None,
                 'scaler_state_dict': scaler.state_dict() if args.use_amp else None,
                 'loss': avg_loss,
                 'args': saved_args # Save all args used for this run
             }
             save_path = f"{args.checkpoint_dir}/checkpoint_epoch{epoch+1}.pth"
             torch.save(checkpoint_data, save_path)
             print(f"Saved checkpoint: {save_path}")
             if is_best:
                 best_path = f"{args.checkpoint_dir}/best_model.pth"
                 torch.save(checkpoint_data, best_path)
                 print(f"Saved best model to: {best_path}")


    writer.close()
    print("Swin MAE Pretraining completed!")


# --- Main execution block ---
if __name__ == '__main__':
    import argparse
    parser = argparse.ArgumentParser('Swin MAE pretraining script', add_help=False)

    # Random Seed
    parser.add_argument('--seed', default=42, type=int, help='Random seed for reproducibility')

    # Model Parameters (Encoder - Swin Specific)
    parser.add_argument('--img_size', default=256, type=int, help='images input size')
    parser.add_argument('--patch_size', default=4, type=int, help='Swin patch size (usually 4)')
    parser.add_argument('--in_chans', default=1, type=int, help='Input channels')
    parser.add_argument('--swin_embed_dim', default=96, type=int, help='Swin encoder embedding dimension (e.g., 96 for Tiny/Small, 128 for Base)')
    parser.add_argument('--swin_depths', type=int, nargs='+', default=[2, 2, 6, 2], help='Swin encoder depth of each stage')
    parser.add_argument('--swin_num_heads', type=int, nargs='+', default=[3, 6, 12, 24], help='Swin encoder number of attention heads in different stages')
    parser.add_argument('--swin_window_size', type=int, default=7, help='Swin attention window size')
    parser.add_argument('--masking_window_size', type=int, default=4, help='Window size for masking (e.g., 4x4 patches)') # Added masking window size arg
    parser.add_argument('--swin_ape', action='store_false', default=True, help='Disable absolute position embedding in Swin encoder (default uses APE)')
    parser.add_argument('--drop_path_rate', type=float, default=0.1, help='Stochastic depth rate for encoder and decoder') # Added drop path rate

    # Model Parameters (Decoder)
    parser.add_argument('--decoder_embed_dim', default=512, type=int, help='Decoder embedding dimension')
    # parser.add_argument('--decoder_depth', default=8, type=int, help='Overall decoder depth (REMOVED - use depths)')
    # parser.add_argument('--decoder_num_heads', default=16, type=int, help='Decoder heads (REMOVED - use heads)')
    parser.add_argument('--decoder_depths', type=int, nargs='+', default=[2, 2, 6, 2], help='Decoder depth per stage (shallow to deep)')
    parser.add_argument('--decoder_num_heads', type=int, nargs='+', default=[3, 6, 12, 24], help='Decoder heads per stage (shallow to deep)')
    parser.add_argument('--norm_pix_loss', action='store_true', default=False, help='Enable normalized pixel loss (default uses standard pixel loss)')
    parser.add_argument('--perceptual_loss_weight', type=float, default=0.01, help='Weight for ResNet18 perceptual loss (0 to disable)') # Default lowered
    parser.add_argument('--perc_layers_resnet', type=str, nargs='+', default=['layer2', 'layer3'], help='ResNet layers for perceptual loss (e.g., layer2 layer3)')
    parser.add_argument('--perc_norm_ct_resnet', action='store_true', default=True, help='Use custom CT normalization for ResNet perceptual loss')
    parser.add_argument('--no_perc_norm_ct_resnet', action='store_false', dest='perc_norm_ct_resnet', help='Disable custom CT normalization for ResNet perceptual loss') # Allows disabling via flag
    parser.add_argument('--ssim_loss_weight', type=float, default=0.05, help='Weight for SSIM loss (0 to disable)') # Add SSIM weight arg

    # Training Parameters
    parser.add_argument('--batch_size', default=64, type=int, help='Batch size per GPU')
    parser.add_argument('--epochs', default=400, type=int) # Increased epochs
    parser.add_argument('--lr', type=float, default=1.5e-4, help='Base learning rate')
    parser.add_argument('--warmup_epochs', type=int, default=80, help='Epochs to warmup LR') # Increased warmup epochs
    parser.add_argument('--weight_decay', type=float, default=0.05)
    parser.add_argument('--use_amp', action='store_true', help='Enable mixed precision training')
    parser.set_defaults(use_amp=False) # Disable AMP by default
    parser.add_argument('--gradient_accumulation_steps', type=int, default=1, help='Number of steps to accumulate gradients before updating weights') # Add grad accum arg

    # Dataset Parameters
    parser.add_argument('--data_dir', default='E:/vscode/2号CT数据', type=str, help='dataset path') # Updated path
    parser.add_argument('--num_workers', default=8, type=int)

    # Checkpoint/Log Parameters
    parser.add_argument('--checkpoint_dir', default='checkpoints/swin_mae_ultra', help='path where to save checkpoints') # Updated default
    parser.add_argument('--log_dir', default='logs/swin_mae_ultra', help='path where to tensorboard log') # Updated default
    parser.add_argument('--save_interval', default=20, type=int, help='Save checkpoint every x epochs')
    parser.add_argument('--resume', default='', help='resume from checkpoint')

    args = parser.parse_args()

    pretrain_mae(args)
