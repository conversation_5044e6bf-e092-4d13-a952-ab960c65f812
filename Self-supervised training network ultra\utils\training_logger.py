#!/usr/bin/env python3
"""
Training logger utilities for clean and organized logging
Provides structured logging with reduced verbosity
"""

import logging
import sys
import os
import time
from datetime import datetime
from typing import Dict, Any, Optional
import torch

class TrainingLogger:
    """训练过程的结构化日志记录器"""

    def __init__(self, log_dir: str, experiment_name: str,
                 console_level: str = "INFO", file_level: str = "DEBUG"):
        """
        初始化训练日志记录器

        Args:
            log_dir: 日志目录
            experiment_name: 实验名称
            console_level: 控制台日志级别
            file_level: 文件日志级别
        """
        self.log_dir = log_dir
        self.experiment_name = experiment_name

        # 创建日志目录
        os.makedirs(log_dir, exist_ok=True)

        # 设置日志文件路径
        timestamp = datetime.now().strftime("%Y%m%d_%H%M%S")
        self.log_file = os.path.join(log_dir, f"{experiment_name}_{timestamp}.log")

        # 配置日志记录器
        self.logger = logging.getLogger(f"training_{experiment_name}")
        self.logger.setLevel(logging.DEBUG)

        # 清除现有的处理器
        self.logger.handlers.clear()

        # 文件处理器
        file_handler = logging.FileHandler(self.log_file, encoding='utf-8')
        file_handler.setLevel(getattr(logging, file_level.upper()))
        file_formatter = logging.Formatter(
            '%(asctime)s - %(name)s - %(levelname)s - %(message)s'
        )
        file_handler.setFormatter(file_formatter)
        self.logger.addHandler(file_handler)

        # 控制台处理器（简化格式）
        console_handler = logging.StreamHandler(sys.stdout)
        console_handler.setLevel(getattr(logging, console_level.upper()))
        console_formatter = logging.Formatter('%(message)s')
        console_handler.setFormatter(console_formatter)
        self.logger.addHandler(console_handler)

        # 训练统计
        self.epoch_start_time = None
        self.batch_times = []
        self.error_counts = {}

    def log_training_start(self, config: Dict[str, Any]):
        """记录训练开始信息"""
        self.logger.info("="*60)
        self.logger.info(f"开始训练实验: {self.experiment_name}")
        self.logger.info("="*60)

        # 记录关键配置信息
        self.logger.info(f"模型配置:")
        model_config = config.get('model', {})
        for key, value in model_config.items():
            if key in ['in_channels', 'out_channels', 'base_channels', 'use_pretrained_encoder']:
                self.logger.info(f"  {key}: {value}")

        self.logger.info(f"训练配置:")
        training_config = config.get('training', {})
        for key, value in training_config.items():
            if key in ['learning_rate', 'batch_size', 'epochs', 'use_gan']:
                self.logger.info(f"  {key}: {value}")

        self.logger.info(f"日志文件: {self.log_file}")
        self.logger.info("-"*60)

    def log_epoch_start(self, epoch: int, total_epochs: int):
        """记录轮次开始"""
        self.epoch_start_time = datetime.now()
        self.batch_times = []
        self.logger.info(f"\n开始第 {epoch+1}/{total_epochs} 轮训练")

    def log_batch_progress(self, batch_idx: int, total_batches: int,
                          losses: Dict[str, float], lr: float,
                          show_every: int = 50):
        """记录批次进度（减少输出频率）"""
        if batch_idx % show_every == 0 or batch_idx == total_batches - 1:
            progress = (batch_idx + 1) / total_batches * 100

            # 格式化损失信息
            loss_str = " | ".join([f"{k}: {v:.4f}" for k, v in losses.items()])

            self.logger.info(
                f"  批次 {batch_idx+1}/{total_batches} ({progress:.1f}%) | "
                f"LR: {lr:.2e} | {loss_str}"
            )

    def log_epoch_summary(self, epoch: int, avg_losses: Dict[str, float],
                         best_loss: Optional[float] = None,
                         is_best: bool = False):
        """记录轮次总结"""
        if self.epoch_start_time:
            epoch_time = (datetime.now() - self.epoch_start_time).total_seconds()
            self.logger.info(f"第 {epoch+1} 轮完成 (用时: {epoch_time:.1f}秒)")

        # 格式化平均损失
        loss_str = " | ".join([f"{k}: {v:.4f}" for k, v in avg_losses.items()])
        self.logger.info(f"平均损失 -> {loss_str}")

        if best_loss is not None:
            self.logger.info(f"当前最佳损失: {best_loss:.6f}")

        if is_best:
            self.logger.info("🎉 发现新的最佳模型！")

        self.logger.info("-"*40)

    def log_error(self, error_type: str, message: str, details: Optional[str] = None):
        """记录错误信息"""
        # 统计错误类型
        self.error_counts[error_type] = self.error_counts.get(error_type, 0) + 1

        # 如果同类错误太多，减少日志输出
        if self.error_counts[error_type] <= 5:
            self.logger.warning(f"错误 [{error_type}]: {message}")
            if details:
                self.logger.debug(f"错误详情: {details}")
        elif self.error_counts[error_type] == 6:
            self.logger.warning(f"错误 [{error_type}] 已出现多次，后续将减少日志输出")
        elif self.error_counts[error_type] % 20 == 0:
            self.logger.warning(f"错误 [{error_type}] 累计出现 {self.error_counts[error_type]} 次")

    def log_model_save(self, save_path: str, save_type: str = "checkpoint"):
        """记录模型保存"""
        self.logger.info(f"✅ {save_type}已保存: {save_path}")

    def log_training_complete(self, total_time: float, best_loss: float, best_epoch: int):
        """记录训练完成"""
        self.logger.info("\n" + "="*60)
        self.logger.info("训练完成！")
        self.logger.info("="*60)
        self.logger.info(f"总训练时间: {total_time/3600:.2f} 小时")
        self.logger.info(f"最佳损失: {best_loss:.6f} (第 {best_epoch} 轮)")

        if self.error_counts:
            self.logger.info("\n错误统计:")
            for error_type, count in self.error_counts.items():
                self.logger.info(f"  {error_type}: {count} 次")

        self.logger.info(f"详细日志已保存至: {self.log_file}")

    def log_system_info(self):
        """记录系统信息"""
        self.logger.info("系统信息:")
        self.logger.info(f"  PyTorch版本: {torch.__version__}")
        self.logger.info(f"  CUDA可用: {torch.cuda.is_available()}")
        if torch.cuda.is_available():
            self.logger.info(f"  CUDA版本: {torch.version.cuda}")
            self.logger.info(f"  GPU数量: {torch.cuda.device_count()}")
            for i in range(torch.cuda.device_count()):
                gpu_name = torch.cuda.get_device_name(i)
                gpu_memory = torch.cuda.get_device_properties(i).total_memory / 1024**3
                self.logger.info(f"  GPU {i}: {gpu_name} ({gpu_memory:.1f}GB)")

class QuietProgressBar:
    """安静的进度条，显示关键训练信息"""

    def __init__(self, total: int, desc: str = "", update_every: int = 10):
        """
        初始化进度条

        Args:
            total: 总数
            desc: 描述
            update_every: 每多少步更新一次
        """
        self.total = total
        self.desc = desc
        self.update_every = update_every
        self.current = 0
        self.last_update = 0
        self.start_time = time.time()
        self.last_losses = {}

    def update(self, n: int = 1, losses: dict = None):
        """更新进度"""
        self.current += n

        if losses:
            self.last_losses = losses

        # 只在特定间隔或完成时显示
        if (self.current - self.last_update >= self.update_every or
            self.current >= self.total):

            progress = self.current / self.total * 100
            elapsed_time = time.time() - self.start_time

            # 基础进度信息
            progress_str = f"{self.desc} {self.current}/{self.total} ({progress:.1f}%)"

            # 添加损失信息（如果有）
            if self.last_losses:
                loss_str = " | ".join([f"{k}: {v:.4f}" for k, v in self.last_losses.items()])
                progress_str += f" | {loss_str}"

            # 添加时间信息
            if elapsed_time > 60:
                progress_str += f" | 用时: {elapsed_time/60:.1f}分钟"

            print(f"\r{progress_str}", end="", flush=True)
            self.last_update = self.current

            if self.current >= self.total:
                print()  # 换行

    def close(self):
        """关闭进度条"""
        if self.current < self.total:
            print()

def setup_quiet_logging():
    """设置安静的日志模式"""
    # 减少第三方库的日志输出
    logging.getLogger('PIL').setLevel(logging.WARNING)
    logging.getLogger('matplotlib').setLevel(logging.WARNING)
    logging.getLogger('kornia').setLevel(logging.WARNING)

    # 设置PyTorch警告过滤
    import warnings
    warnings.filterwarnings("ignore", category=UserWarning, module="torch")
    warnings.filterwarnings("ignore", category=FutureWarning, module="torch")

def create_training_logger(log_dir: str, experiment_name: str) -> TrainingLogger:
    """
    创建训练日志记录器的便捷函数

    Args:
        log_dir: 日志目录
        experiment_name: 实验名称

    Returns:
        TrainingLogger实例
    """
    setup_quiet_logging()
    return TrainingLogger(log_dir, experiment_name)
