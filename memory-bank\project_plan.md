# CT图像超分辨率项目计划（更新版）

本文档概述了基于最新自监督预训练成果的CT图像超分辨率项目完整计划。

## 📊 **项目当前状态**

### 🎯 **自监督预训练阶段成果**
- **最佳模型**：`train_swin_mae_resnet_random_mask_hierarchical.py`
- **最佳检查点**：`checkpoints/swin_mae_hierarchical_random_ssim_nce_w002/best_model.pth`
- **性能指标**：
  - **PSNR**: 21.51 dB
  - **SSIM**: 0.626
- **关键技术**：层次化解码器 + 随机掩码 + SSIM损失 + PatchNCE对比损失

### 🚀 **项目目标**
1. **短期目标**：将自监督预训练PSNR提升到22.0+
2. **核心目标**：实现高质量CT图像超分辨率，PSNR提升2-3dB
3. **最终目标**：为岩心CT图像分析提供高分辨率重建能力

## 🔄 **三阶段实施计划**

### 🚀 **阶段一：自监督预训练最终优化（1-2周）**

#### **目标**：PSNR从21.51提升到22.0+，SSIM从0.626提升到0.65+

#### **具体任务**：
1. **超参数精细调优**
   ```bash
   # 测试更优的损失权重组合
   python train_swin_mae_resnet_random_mask_hierarchical.py \
     --seed 42 --epochs 300 --batch_size 8 --gradient_accumulation_steps 16 \
     --patchnce_loss_weight 0.003 --ssim_loss_weight 0.06 \
     --perceptual_loss_weight 0.005 --nce_proj_dim 256 --nce_T 0.07
   ```

2. **模型架构微调**
   - 测试更深解码器：`--decoder_depths 2 2 2 2`
   - 测试更大嵌入维度：`--decoder_embed_dim 256`
   - 优化注意力头配置：`--decoder_num_heads 12 8 6 4`

3. **训练策略优化**
   - 扩展训练到300 epochs
   - 实现余弦退火学习率调度
   - 添加梯度裁剪（max_norm=1.0）

#### **成功标准**：
- ✅ PSNR > 22.0 dB
- ✅ SSIM > 0.65
- ✅ 视觉质量显著改善

### 🎯 **阶段二：监督训练环境准备与启动（1周）**

#### **目标**：成功启动超分辨率监督训练并建立基线

#### **具体任务**：
1. **配置文件修正**
   ```yaml
   # 修正config_ultra.yaml中的关键配置
   data:
     hr_dir: "./data/3号CT数据"
     lr_dir: "./data/2号CT数据"
   model:
     encoder_checkpoint: ./checkpoints/swin_mae_hierarchical_random_ssim_nce_w002/best_model.pth
     freeze_encoder: true
   ```

2. **环境验证**
   - 验证数据路径存在性
   - 检查预训练模型加载
   - 测试GPU内存充足性

3. **启动监督训练**
   ```bash
   cd "Self-supervised training network ultra"
   python train_sr_ultra.py --config configs/config_ultra.yaml \
     --tag SwinMAE_Hierarchical_DiffusionSR_v1
   ```

#### **成功标准**：
- ✅ 训练成功启动无错误
- ✅ 损失曲线正常下降
- ✅ 建立超分辨率基线PSNR

### 🔧 **阶段三：监督训练优化与最终评估（2-3周）**

#### **目标**：实现高质量CT图像超分辨率，达到项目最终目标

#### **具体任务**：
1. **损失函数权重优化**
   ```yaml
   # 基于基线结果调整损失权重
   training:
     diffusion_loss_type: l1
     perceptual_loss_weight: 0.02
     ssim_loss_weight: 0.08
     gradient_loss_weight: 0.015
     gan_loss_weight: 0.1
   ```

2. **编码器微调策略**
   ```yaml
   # 第二阶段：解冻编码器进行微调
   model:
     freeze_encoder: false
     encoder_injection_levels: [0, 1, 2, 3]
   training:
     learning_rate: 1e-4
     encoder_lr: 1e-5  # 编码器使用更小学习率
   ```

3. **自适应门控优化**
   - 监控门控权重分布
   - 调整门控网络隐藏维度
   - 测试不同门控策略

4. **CT图像特性优化**
   ```python
   # 针对岩心CT图像的特殊处理
   data_config = {
       'hu_window': [-1000, 3000],
       'normalize_method': 'z_score',
       'edge_enhancement': True,
   }
   ```

#### **成功标准**：
- 🎯 超分辨率PSNR提升2-3dB（相对于双三次插值）
- 🎯 SSIM提升5-10%
- 🎯 边缘和纹理细节显著改善
- 🎯 HU值分布保持准确性

## 📋 **详细实施时间线**

### 📅 **第1-2周：自监督预训练最终冲刺**
- **Day 1-3**：超参数调优实验
  - 测试PatchNCE权重：0.002, 0.003, 0.004
  - 测试SSIM权重：0.05, 0.06, 0.07
  - 测试解码器配置：更深层次和更大嵌入维度

- **Day 4-7**：最佳配置长期训练
  - 使用最优超参数训练300 epochs
  - 实现梯度裁剪和学习率调度
  - 密切监控PSNR和SSIM指标

- **Day 8-14**：结果验证和模型选择
  - 全面评估所有训练模型
  - 选择最佳检查点作为监督训练初始化
  - 准备监督训练环境

### 📅 **第3周：监督训练启动**
- **Day 15-17**：环境配置
  - 修正config_ultra.yaml配置
  - 验证数据路径和预训练模型
  - 测试训练脚本完整性

- **Day 18-21**：基线训练
  - 启动监督训练建立基线
  - 监控训练稳定性和收敛性
  - 初步评估超分辨率效果

### 📅 **第4-6周：优化和最终评估**
- **Week 4**：损失函数和超参数优化
- **Week 5**：编码器微调和门控优化
- **Week 6**：最终评估和结果分析

## 🎯 **关键性能指标追踪**

### 📊 **自监督预训练指标**
| 模型版本 | PSNR | SSIM | 检查点路径 | 备注 |
|---------|------|------|------------|------|
| 当前最佳 | 21.51 | 0.626 | swin_mae_hierarchical_random_ssim_nce_w002 | 基线 |
| 目标v1 | >22.0 | >0.65 | 待训练 | 优化目标 |
| 目标v2 | >22.5 | >0.67 | 待训练 | 理想目标 |

### 📊 **监督训练指标**
| 阶段 | 超分辨率PSNR | 超分辨率SSIM | 相对提升 | 备注 |
|------|-------------|-------------|----------|------|
| 基线 | 待建立 | 待建立 | - | 初始基线 |
| 优化后 | 目标+2-3dB | 目标+5-10% | 显著提升 | 最终目标 |

## 🚀 **立即可执行的行动计划**

### 🎯 **今天就可以开始的任务**

1. **启动预训练最终优化**
   ```bash
   # 使用改进的超参数配置
   python train_swin_mae_resnet_random_mask_hierarchical.py \
     --seed 42 --epochs 300 --batch_size 8 --gradient_accumulation_steps 16 \
     --patch_size 4 --decoder_embed_dim 256 --decoder_depths 2 2 2 2 \
     --decoder_num_heads 12 8 6 4 --perceptual_loss_weight 0.005 \
     --ssim_loss_weight 0.06 --patchnce_loss_weight 0.003 \
     --nce_proj_dim 256 --nce_T 0.07 --lr 1.5e-4 --warmup_epochs 40 \
     --weight_decay 0.05 --use_amp
   ```

2. **准备监督训练环境**
   - 检查数据路径：`./data/2号CT数据` 和 `./data/3号CT数据`
   - 验证预训练模型：`checkpoints/swin_mae_hierarchical_random_ssim_nce_w002/best_model.pth`
   - 测试GPU内存和依赖环境

3. **配置文件更新**
   - 更新`config_ultra.yaml`中的预训练模型路径
   - 调整初始损失权重配置
   - 设置合适的批次大小和学习率

### ⚠️ **风险控制措施**

1. **内存管理**
   - 使用梯度检查点：`--use_checkpoint`
   - 混合精度训练：`--use_amp`
   - 适当的批次大小：8-16

2. **训练稳定性**
   - 梯度裁剪：`max_norm=1.0`
   - 学习率预热：`warmup_epochs=40`
   - 定期保存检查点：每10个epoch

3. **质量监控**
   - 实时PSNR/SSIM监控
   - TensorBoard可视化
   - 定期生成重建样本

## 🎯 **预期成果与里程碑**

### 📈 **短期里程碑（2周内）**
- ✅ 自监督预训练PSNR突破22.0
- ✅ SSIM达到0.65+
- ✅ 监督训练成功启动

### 📈 **中期里程碑（4周内）**
- 🎯 超分辨率基线建立
- 🎯 损失函数优化完成
- 🎯 编码器微调策略确定

### 📈 **最终目标（6周内）**
- 🏆 超分辨率PSNR提升2-3dB
- 🏆 视觉质量显著改善
- 🏆 完整的评估报告和模型交付

### 阶段四：针对性优化与迭代实验

*   **目标：** 通过解决已识别的瓶颈和探索优化方案，系统地提高模型性能。
*   **任务（迭代循环）：**
    *   **自适应门控机制：**
        *   验证其实现方式及其对特征融合的影响。
        *   如有必要，进行消融研究（例如，比较有无门控或不同门控功能的情况）。
    *   **预训练编码器集成：**
        *   试验Swin-MAE编码器的微调策略（例如，冻结、解冻特定层、完全微调）。
        *   分析其对特征质量和整体SR性能的影响。
    *   **损失函数调整：**
        *   根据基线中观察到的弱点，调整不同损失分量（L1/MSE、感知损失、SSIM、梯度损失、GAN损失）的权重。
        *   如果需要，试验不同的感知损失主干网络或层。
        *   如果使用GAN，仔细调整判别器架构和训练稳定性。
    *   **扩散过程参数：**
        *   研究扩散时间步长 (`T`) 的数量和不同的噪声调度。
    *   **数据增强与预处理：**
        *   审查并优化CT特定的数据增强技术。
        *   确保HU窗口和归一化对于任务是最优的。
    *   **正则化与训练稳定性：**
        *   如果观察到过拟合，探索正则化技术。
        *   监控训练稳定性，尤其是在涉及GAN时。
    *   对于每个实验，重复进行训练、评估（定量和定性）和分析。

### 阶段五：模型比较（可选，如果基线 `Self-supervised training network` 也被训练）

*   **目标：** 如果 `ultra` 和基线模型都已开发，则比较它们的性能。
*   **任务：**
    *   如果尚未完成，则在相似条件下训练基线 `Self-supervised training network`。
    *   使用相同的验证集和指标，对最佳 `ultra` 模型和基线模型进行直接比较。
    *   分析每个模型在视觉质量和特定优缺点上的差异。

### 阶段六：最终模型选择与精炼

*   **目标：** 选择性能最佳的模型配置并进行最终精炼。
*   **任务：**
    *   根据所有实验结果，选择产生最佳整体性能的模型架构、超参数和训练策略。
    *   可能会使用最优配置运行更长时间的训练。
    *   如果能带来显著改进，可以考虑集成方法或后处理技术。

### 阶段七：全面评估与结果总结

*   **目标：** 对最终模型进行彻底评估并记录研究结果。
*   **任务：**
    *   在保留测试集（如果可用）或综合验证集上评估最终模型。
    *   报告所有相关的定量指标（PSNR、SSIM、LPIPS等）。
    *   提供丰富的定性结果，包括与双三次插值、LR图像和HR真实值（如果对子集可用）的视觉比较。
    *   分析模型重建CT影像特有细节和结构的能力。
    *   总结项目、主要发现、挑战和潜在的未来工作。
    *   准备最终报告或演示文稿。

## 时间表与里程碑

*   （根据可用资源和优先级待定）

## 资源

*   **数据集：** （指定路径和描述）
*   **预训练模型：** （指定Swin-MAE编码器权重的路径）
*   **计算资源：** （GPU资源等）