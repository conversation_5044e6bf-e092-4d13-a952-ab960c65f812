# CT图像超分辨率项目运行环境

## 基本环境信息

- **Python版本**: 3.8.0
- **环境名称**: pytorchEnv
- **Python路径**: D:\anaconda\anaconda\envs\pytorchEnv\python.exe
- **操作系统**: Windows

## 运行规则

1. **所有程序必须在指定环境中运行**:
   ```
   Python 3.8.0 ('pytorchEnv') D:\anaconda\anaconda\envs\pytorchEnv\python.exe
   ```

2. **激活环境的方法**:
   ```bash
   # 在命令行中激活环境
   conda activate pytorchEnv
   ```

3. **运行Python脚本的标准命令**:
   ```bash
   # 使用正确的Python解释器运行脚本
   D:\anaconda\anaconda\envs\pytorchEnv\python.exe script_name.py [arguments]
   
   # 或在激活环境后
   python script_name.py [arguments]
   ```

## 常用运行命令

### 自监督预训练

1. **运行Swin-MAE预训练**:
   ```bash
   python train_swin_mae.py [arguments]
   ```

2. **运行Vision Transformer MAE预训练**:
   ```bash
   python train_mae.py [arguments]
   ```

3. **运行多尺度对比学习预训练**:
   ```bash
   python train_enhanced.py [arguments]
   ```

### 可视化与分析

1. **可视化Swin-MAE重建结果**:
   ```bash
   python visualize_swin_mae_resnet.py [arguments]
   ```

### 监督训练

1. **运行超分辨率网络训练**:
   ```bash
   # 待实现
   python train_sr_network.py [arguments]
   ```

## 环境依赖

主要依赖库及其版本要求：
```
torch>=1.7.0
torchvision>=0.8.0
numpy
opencv-python
scikit-image
matplotlib
tensorboard
timm
```

## 注意事项

1. 处理2700×2700大尺寸CT图像需要大量GPU内存，确保有足够的GPU资源
2. 使用梯度累积和混合精度训练可以缓解内存压力
3. 长时间训练时，建议使用检查点保存训练状态
4. 使用TensorBoard监控训练进度和结果

## 故障排除

1. **内存不足错误**:
   - 减小批量大小
   - 启用梯度累积
   - 使用混合精度训练

2. **CUDA错误**:
   - 检查CUDA版本与PyTorch兼容性
   - 更新GPU驱动

3. **训练不稳定**:
   - 降低学习率
   - 使用学习率预热
   - 检查数据归一化
