# -*- coding: utf-8 -*-
"""
Defines the PatchGAN discriminator model used for adversarial training.
Inspired by architectures used in Pix2Pix, CycleGAN, and SRGAN.
"""

import torch
import torch.nn as nn
import functools

class NLayerDiscriminator(nn.Module):
    """Defines a PatchGAN discriminator as described in Pix2Pix
        --> Uses a series of Conv2d => BatchNorm2d => LeakyReLU blocks
        --> Outputs a 2D map of predictions for overlapping patches.
    """
    def __init__(self, input_nc, ndf=64, n_layers=3, norm_layer=nn.BatchNorm2d, use_sigmoid=False):
        """Construct a PatchGAN discriminator
        Parameters:
            input_nc (int)  -- the number of channels in input images
            ndf (int)       -- the number of filters in the last conv layer
            n_layers (int)  -- the number of conv layers in the discriminator
            norm_layer      -- normalization layer
            use_sigmoid (bool) -- if True, use a sigmoid layer at the end (usually False for LSGAN/WGAN)
        """
        super(NLayerDiscriminator, self).__init__()
        if type(norm_layer) == functools.partial:  # no need to use bias as BatchNorm2d has affine parameters
            use_bias = norm_layer.func != nn.BatchNorm2d
        else:
            use_bias = norm_layer != nn.BatchNorm2d

        kw = 4 # Kernel width
        padw = 1 # Padding width

        # First layer: Conv2d -> LeakyReLU
        sequence = [nn.Conv2d(input_nc, ndf, kernel_size=kw, stride=2, padding=padw), nn.LeakyReLU(0.2, True)]

        # Middle layers: Conv2d -> Norm -> LeakyReLU
        nf_mult = 1
        nf_mult_prev = 1
        for n in range(1, n_layers):  # gradually increase the number of filters
            nf_mult_prev = nf_mult
            nf_mult = min(2 ** n, 8) # Cap multiplier at 8 (ndf * 8)
            sequence += [
                nn.Conv2d(ndf * nf_mult_prev, ndf * nf_mult, kernel_size=kw, stride=2, padding=padw, bias=use_bias),
                norm_layer(ndf * nf_mult),
                nn.LeakyReLU(0.2, True)
            ]

        # Final layer before output: Conv2d -> Norm -> LeakyReLU
        nf_mult_prev = nf_mult
        nf_mult = min(2 ** n_layers, 8)
        sequence += [
            nn.Conv2d(ndf * nf_mult_prev, ndf * nf_mult, kernel_size=kw, stride=1, padding=padw, bias=use_bias),
            norm_layer(ndf * nf_mult),
            nn.LeakyReLU(0.2, True)
        ]

        # Output layer: 1x1 Conv to produce the prediction map
        sequence += [nn.Conv2d(ndf * nf_mult, 1, kernel_size=kw, stride=1, padding=padw)]

        # Optional sigmoid activation (usually not used with BCEWithLogitsLoss or LSGAN loss)
        if use_sigmoid:
            sequence += [nn.Sigmoid()]

        self.model = nn.Sequential(*sequence)

    def forward(self, input):
        """Standard forward."""
        return self.model(input)

# Example usage (for testing):
if __name__ == '__main__':
    # Example: Discriminate 128x128 image (3 channels)
    netD = NLayerDiscriminator(input_nc=1, ndf=64, n_layers=3, norm_layer=nn.BatchNorm2d)
    print(netD)
    # Input image (batch size 4, 1 channel, 128x128)
    dummy_input = torch.randn(4, 1, 128, 128)
    output = netD(dummy_input)
    # Output shape depends on n_layers and input size, e.g., (4, 1, 14, 14) for n_layers=3, input=128
    print("Output shape:", output.shape)

    # Example with different number of layers
    netD_4 = NLayerDiscriminator(input_nc=1, ndf=64, n_layers=4, norm_layer=nn.BatchNorm2d)
    output_4 = netD_4(dummy_input)
    # Output shape e.g., (4, 1, 6, 6) for n_layers=4, input=128
    print("Output shape (n_layers=4):", output_4.shape)
