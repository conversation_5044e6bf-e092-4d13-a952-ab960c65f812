# -*- coding: utf-8 -*-
"""
Main training script for the Conditional Diffusion Super-Resolution model.
"""

import torch
import torch.optim as optim
import torch.nn as nn
from torch.utils.tensorboard import SummaryWriter
import yaml
import argparse
import os
import time
from tqdm import tqdm
import math
import numpy as np # Added for random seed setting
import random # Added for random seed setting

# --- Local Imports (Adjusted for 'ultra' structure) ---
try:
    from models.diffusion_sr_model_ultra import DiffusionSRModel # Import the ultra model
except ImportError as e:
    print(f"Warning: Could not import DiffusionSRModel: {e}")

try:
    from models.discriminator import NLayerDiscriminator # Import Discriminator from ultra/models
except ImportError as e:
    print(f"Warning: Could not import NLayerDiscriminator: {e}")

from data.sr_dataloader import get_sr_dataloader # Import dataloader from ultra/data
from utils.losses import PerceptualLoss, SSIMLoss, GradientLoss # Import losses from ultra/utils

try:
    from utils.diffusion_utils import get_diffusion_schedule, q_sample, predict_xstart_from_noise # Import utils from ultra/utils
except ImportError as e:
    print(f"Warning: Could not import diffusion utils: {e}")

try:
    from utils.training_logger import create_training_logger # Import enhanced logging
except ImportError as e:
    print(f"Warning: Could not import training_logger: {e}")

try:
    from utils.error_handler import print_error_summary # Import error handling
except ImportError as e:
    print(f"Warning: Could not import error_handler: {e}")

# --- Argument Parsing & Config Loading ---
def parse_args():
    parser = argparse.ArgumentParser(description='Train Conditional Diffusion SR Model (Ultra Version)') # Updated description
    parser.add_argument('--config', type=str, required=True, help='Path to the configuration YAML file (e.g., ultra/configs/config_ultra.yaml)')
    parser.add_argument('--resume', type=str, default=None, help='Path to checkpoint to resume training from')
    parser.add_argument('--tag', type=str, default='sr_diffusion_ultra_run', help='Experiment tag for logging and checkpoints') # Updated default tag
    # Add other command-line overrides if needed (e.g., batch_size, epochs)
    args = parser.parse_args()
    return args

def load_config(config_path):
    with open(config_path, 'r', encoding='utf-8') as f:
        config = yaml.safe_load(f)
    return config

def save_checkpoint(model, optimizer, scheduler, scaler, discriminator, optimizer_D,
                   epoch, global_step, total_loss, best_total_loss, best_epoch,
                   config, args, checkpoint_dir, is_best=False, use_gan=False, use_amp=False):
    """Save model checkpoint following pretraining pattern"""
    os.makedirs(checkpoint_dir, exist_ok=True)

    checkpoint_data = {
        'epoch': epoch,
        'global_step': global_step,
        'model_state_dict': model.state_dict(),
        'optimizer_state_dict': optimizer.state_dict(),
        'scheduler_state_dict': scheduler.state_dict() if scheduler else None,
        'scaler_state_dict': scaler.state_dict() if use_amp else None,
        'total_loss': total_loss,
        'best_total_loss': best_total_loss,
        'best_epoch': best_epoch,
        'config': config,
        'args': args,
    }

    # Add discriminator states if using GAN
    if use_gan and discriminator and optimizer_D:
        checkpoint_data['discriminator_state_dict'] = discriminator.state_dict()
        checkpoint_data['optimizer_D_state_dict'] = optimizer_D.state_dict()

    # Save regular checkpoint
    checkpoint_path = os.path.join(checkpoint_dir, f'checkpoint_epoch_{epoch}.pth')
    torch.save(checkpoint_data, checkpoint_path)
    print(f"Saved checkpoint: {checkpoint_path}")

    # Save best model
    if is_best:
        best_path = os.path.join(checkpoint_dir, 'best_model.pth')
        torch.save(checkpoint_data, best_path)
        print(f"New best model saved with total loss: {total_loss:.6f}")

        # Also save best generator only (for inference)
        best_generator_path = os.path.join(checkpoint_dir, 'best_model_generator.pth')
        generator_data = {
            'epoch': epoch,
            'global_step': global_step,
            'model_state_dict': model.state_dict(),
            'total_loss': total_loss,
            'best_total_loss': best_total_loss,
            'best_epoch': best_epoch,
            'config': config,
            'args': args,
        }
        torch.save(generator_data, best_generator_path)
        print(f"Best generator model saved to: {best_generator_path}")

# --- Main Training Function ---
def train(config, args):
    # --- Setup ---
    device = torch.device("cuda" if torch.cuda.is_available() else "cpu")
    print(f"Using device: {device}")

    # Create directories for logging and checkpoints
    log_dir = os.path.join(config['training']['log_root'], args.tag)
    checkpoint_dir = os.path.join(config['training']['checkpoint_root'], args.tag)
    os.makedirs(log_dir, exist_ok=True)
    os.makedirs(checkpoint_dir, exist_ok=True)

    # Setup logging (console and file)
    # logger = setup_logging(log_dir) # Placeholder for logging setup
    writer = SummaryWriter(log_dir)
    print(f"Logging to: {log_dir}")
    print(f"Saving checkpoints to: {checkpoint_dir}")

    # Save config for reproducibility
    with open(os.path.join(log_dir, 'config.yaml'), 'w') as f:
        yaml.dump(config, f)

    # Set random seed
    seed = config['training'].get('seed', 42)
    torch.manual_seed(seed)
    torch.cuda.manual_seed_all(seed)
    random.seed(seed)
    np.random.seed(seed) # If using numpy random

    # --- Data Loading ---
    print("Loading data...")
    dataloader = get_sr_dataloader(
        config=config, # Pass full config
        batch_size=config['training']['batch_size'],
        num_workers=config['training'].get('num_workers', 4)
    )
    if dataloader is None:
        print("Failed to create dataloader. Exiting.")
        return

    # --- Model Initialization ---
    print("Initializing ultra model...") # Updated print statement
    model = DiffusionSRModel(config).to(device) # Instantiates the ultra model
    print(f"Ultra Model Parameters: {sum(p.numel() for p in model.parameters() if p.requires_grad):,}")

    # --- Diffusion Schedule ---
    print("Setting up diffusion schedule...")
    diffusion_config = config['diffusion']
    schedule_dict = get_diffusion_schedule(
        diffusion_config['schedule_name'],
        diffusion_config['timesteps']
    )
    # Move schedule tensors to device
    for key in schedule_dict:
        if isinstance(schedule_dict[key], torch.Tensor):
            schedule_dict[key] = schedule_dict[key].to(device)

    # --- Loss Functions ---
    print("Initializing loss functions...")
    diffusion_loss_type = config['training'].get('diffusion_loss_type', 'l1')
    if diffusion_loss_type == 'l1':
        diffusion_criterion = torch.nn.L1Loss()
    elif diffusion_loss_type == 'mse':
        diffusion_criterion = torch.nn.MSELoss()
    else:
        raise ValueError(f"Unsupported diffusion loss type: {diffusion_loss_type}")

    perceptual_loss_weight = config['training'].get('perceptual_loss_weight', 0.0)
    perceptual_criterion = None
    if perceptual_loss_weight > 0:
        perceptual_criterion = PerceptualLoss(
            loss_type=config['training'].get('perceptual_loss_type', 'l1'),
            device=device
        ).to(device)
        print(f"Using Perceptual Loss with weight: {perceptual_loss_weight}")

    ssim_loss_weight = config['training'].get('ssim_loss_weight', 0.0)
    ssim_criterion = None
    if ssim_loss_weight > 0:
        try:
            # Initialize SSIM loss (can configure use_ms_ssim via config if needed)
            ssim_criterion = SSIMLoss(
                data_range=1.0, # Since we work with [-1, 1] and rescale to [0, 1]
                channel=config['model']['out_channels'],
                use_ms_ssim=config['training'].get('use_ms_ssim', False) # Default to SSIM
            ).to(device)
            print(f"Using {'MS-SSIM' if config['training'].get('use_ms_ssim', False) else 'SSIM'} Loss with weight: {ssim_loss_weight}")
        except ImportError as e:
            print(f"ImportError for SSIMLoss: {e}. SSIM loss disabled.")
            ssim_loss_weight = 0.0 # Disable if import failed

    gradient_loss_weight = config['training'].get('gradient_loss_weight', 0.0)
    gradient_criterion = None
    if gradient_loss_weight > 0:
        try:
            gradient_criterion = GradientLoss(loss_weight=1.0).to(device) # Weight applied later
            print(f"Using Gradient Loss with weight: {gradient_loss_weight}")
        except ImportError as e:
            print(f"ImportError for GradientLoss: {e}. Gradient loss disabled.")
            gradient_loss_weight = 0.0 # Disable if import failed

    # --- GAN Setup (Optional) ---
    use_gan = config['training'].get('use_gan', False)
    discriminator = None
    optimizer_D = None
    gan_criterion = None
    gan_loss_weight = 0.0
    if use_gan:
        print("Initializing GAN Discriminator...")
        # Use parameters from config or defaults for discriminator
        disc_config = config.get('discriminator', {})
        discriminator = NLayerDiscriminator(
            input_nc=config['model']['out_channels'], # Discriminator sees generated/real HR images
            ndf=disc_config.get('ndf', 64),
            n_layers=disc_config.get('n_layers', 3),
            norm_layer=nn.BatchNorm2d # Or other norm layer from config
        ).to(device)
        print(f"Discriminator Parameters: {sum(p.numel() for p in discriminator.parameters() if p.requires_grad):,}")

        # Use BCEWithLogitsLoss for standard GAN loss (more stable than Sigmoid + BCELoss)
        gan_criterion = torch.nn.BCEWithLogitsLoss()
        gan_loss_weight = config['training'].get('gan_loss_weight', 0.1)
        print(f"Using GAN Loss with weight: {gan_loss_weight}")

        # Optimizer for Discriminator
        disc_lr = disc_config.get('lr', config['training']['learning_rate']) # Use separate LR or default
        optimizer_D = optim.AdamW(
            discriminator.parameters(),
            lr=disc_lr,
            betas=(0.5, 0.999), # Common betas for GANs
            weight_decay=config['training'].get('weight_decay', 0.0)
        )
        print(f"Setting up Discriminator optimizer with LR: {disc_lr}")


    # --- Optimizer and Scheduler (for Generator/Diffusion Model) ---
    print("Setting up Generator optimizer and scheduler...")
    lr = config['training']['learning_rate']
    wd = config['training'].get('weight_decay', 0.0)
    freeze_encoder = config['model'].get('freeze_encoder', True)

    if freeze_encoder or not model.use_pretrained_encoder:
        print(f"Optimizing all parameters with LR: {lr}")
        optimizer = optim.AdamW(model.parameters(), lr=lr, weight_decay=wd)
    else:
        # Setup differential learning rate for fine-tuning
        encoder_lr = config['training'].get('encoder_lr', lr) # Default to main LR if not specified
        print(f"Fine-tuning encoder with LR: {encoder_lr}, U-Net LR: {lr}")

        encoder_params = model.pretrained_encoder.parameters()
        unet_params = [p for n, p in model.named_parameters() if not n.startswith('pretrained_encoder.') and p.requires_grad]

        param_groups = [
            {'params': unet_params, 'lr': lr},
            {'params': encoder_params, 'lr': encoder_lr} # Encoder might have requires_grad=False if frozen, but AdamW handles empty groups
        ]
        optimizer = optim.AdamW(param_groups, lr=lr, weight_decay=wd) # Default lr is main lr

    # TODO: Implement learning rate scheduler (e.g., CosineAnnealingLR, ReduceLROnPlateau)
    scheduler = None # Placeholder

    # --- Automatic Mixed Precision (AMP) ---
    use_amp = config['training'].get('use_amp', False)
    scaler = torch.cuda.amp.GradScaler(enabled=use_amp)
    print(f"Using Automatic Mixed Precision: {use_amp}")

    # --- Resume Training ---
    start_epoch = 0
    global_step = 0
    best_val_loss = float('inf') # Or other metric

    # Initialize best loss tracking (following pretraining pattern)
    best_total_loss = float('inf')
    best_epoch = 0

    if args.resume and os.path.isfile(args.resume):
        print(f"Resuming from checkpoint: {args.resume}")
        checkpoint = torch.load(args.resume, map_location=device)
        try:
            model.load_state_dict(checkpoint['model_state_dict'])
            optimizer.load_state_dict(checkpoint['optimizer_state_dict'])
            if scheduler and 'scheduler_state_dict' in checkpoint:
                scheduler.load_state_dict(checkpoint['scheduler_state_dict'])
            if use_amp and 'scaler_state_dict' in checkpoint and checkpoint['scaler_state_dict'] is not None:
                 scaler.load_state_dict(checkpoint['scaler_state_dict'])
            start_epoch = checkpoint.get('epoch', 0)
            global_step = checkpoint.get('global_step', start_epoch * len(dataloader))
            best_val_loss = checkpoint.get('best_val_loss', float('inf'))
            # Load best loss tracking
            best_total_loss = checkpoint.get('best_total_loss', float('inf'))
            best_epoch = checkpoint.get('best_epoch', 0)
            # Load discriminator state if resuming and GAN was used
            if use_gan and discriminator and 'discriminator_state_dict' in checkpoint:
                discriminator.load_state_dict(checkpoint['discriminator_state_dict'])
                print("Loaded discriminator state dict.")
            if use_gan and optimizer_D and 'optimizer_D_state_dict' in checkpoint:
                optimizer_D.load_state_dict(checkpoint['optimizer_D_state_dict'])
                print("Loaded discriminator optimizer state dict.")
            print(f"Resumed from epoch {start_epoch}, global step {global_step}")
        except Exception as e:
            print(f"Error loading checkpoint state: {e}. Starting from scratch.")
            start_epoch = 0
            global_step = 0
            best_val_loss = float('inf')

    # --- Training Loop ---
    print("Starting training...")
    epochs = config['training']['epochs']
    timesteps = diffusion_config['timesteps']

    for epoch in range(start_epoch, epochs):
        print(f"\nEpoch {epoch+1}/{epochs}:")
        model.train()
        epoch_loss_G = 0.0 # Generator total loss
        epoch_loss_D = 0.0 # Discriminator total loss
        epoch_diffusion_loss = 0.0
        epoch_perceptual_loss = 0.0
        epoch_ssim_loss = 0.0
        epoch_gradient_loss = 0.0 # Add Gradient loss tracking
        epoch_gan_G_loss = 0.0 # Generator's adversarial loss

        # 使用tqdm进度条，与预训练模型保持一致
        with tqdm(dataloader, unit="batch") as pbar:
            for step, (lr_batch, hr_batch) in enumerate(pbar):
                # Ensure optimizers are zeroed correctly depending on update step
                # optimizer.zero_grad() # Moved later

                lr_batch = lr_batch.to(device) # Condition
                hr_batch = hr_batch.to(device) # Target x_0 (Real HR)
                batch_size = hr_batch.shape[0]

                # 1. Sample timesteps t for the batch
                t = torch.randint(0, timesteps, (batch_size,), device=device).long()

                # 2. Sample noise and compute x_t using q_sample (forward process)
                noise = torch.randn_like(hr_batch)
                x_t = q_sample(x_start=hr_batch, t=t, schedule_dict=schedule_dict, noise=noise)

                # 3. Predict noise using the Generator (Diffusion Model)
                with torch.cuda.amp.autocast(enabled=use_amp):
                    predicted_noise = model(x_t, t, condition=lr_batch) # Generator forward pass

                    # --- Calculate Generator Losses ---
                    loss_G = 0.0 # Total Generator loss for this step

                    # Diffusion Loss (predicting noise)
                    diffusion_loss = diffusion_criterion(predicted_noise, noise)
                    loss_G += diffusion_loss
                    epoch_diffusion_loss += diffusion_loss.item()

                    # Reconstruct x_0 for other losses
                    reconstructed_x0 = predict_xstart_from_noise(x_t, t, predicted_noise, schedule_dict)
                    reconstructed_x0 = torch.clamp(reconstructed_x0, -1.0, 1.0) # Clamp to valid range

                # Perceptual Loss (optional)
                perceptual_loss = torch.tensor(0.0, device=device) # Initialize
                if perceptual_criterion is not None and perceptual_loss_weight > 0:
                    perceptual_loss = perceptual_criterion(reconstructed_x0, hr_batch)
                    loss_G += perceptual_loss * perceptual_loss_weight
                    epoch_perceptual_loss += perceptual_loss.item()

                # SSIM Loss (optional)
                ssim_loss = torch.tensor(0.0, device=device) # Initialize
                if ssim_criterion is not None and ssim_loss_weight > 0:
                    ssim_loss = ssim_criterion(reconstructed_x0, hr_batch)
                    loss_G += ssim_loss * ssim_loss_weight
                    epoch_ssim_loss += ssim_loss.item()

                # Gradient Loss (optional)
                gradient_loss = torch.tensor(0.0, device=device) # Initialize
                if gradient_criterion is not None and gradient_loss_weight > 0:
                    gradient_loss = gradient_criterion(reconstructed_x0, hr_batch)
                    loss_G += gradient_loss * gradient_loss_weight
                    epoch_gradient_loss += gradient_loss.item()

                # Adversarial Loss for Generator (optional)
                loss_G_adv = torch.tensor(0.0, device=device) # Initialize
                if use_gan and discriminator:
                    # We want the discriminator to think the fake images are real
                    pred_fake = discriminator(reconstructed_x0) # Pass generated SR through D
                    # Calculate loss against "real" labels (e.g., ones)
                    target_real = torch.ones_like(pred_fake, device=device)
                    loss_G_adv = gan_criterion(pred_fake, target_real)
                    loss_G += loss_G_adv * gan_loss_weight
                    epoch_gan_G_loss += loss_G_adv.item()

            # --- Generator Update ---
            optimizer.zero_grad() # Zero G optimizer grads
            scaler.scale(loss_G).backward() # Backprop G loss
            # Optional: Gradient clipping for G
            # if config['training'].get('grad_clip_norm', None):
            #     scaler.unscale_(optimizer)
            #     torch.nn.utils.clip_grad_norm_(model.parameters(), config['training']['grad_clip_norm'])
            scaler.step(optimizer) # Update G weights
            # scaler.update() # Update scaler later, after D step

            # --- Discriminator Update (Optional) ---
            loss_D_total = torch.tensor(0.0, device=device) # Initialize
            if use_gan and discriminator and optimizer_D:
                optimizer_D.zero_grad() # Zero D optimizer grads

                with torch.cuda.amp.autocast(enabled=use_amp):
                    # Loss for real images
                    pred_real = discriminator(hr_batch)
                    target_real = torch.ones_like(pred_real, device=device)
                    loss_D_real = gan_criterion(pred_real, target_real)

                    # Loss for fake images (use detached reconstructed_x0)
                    pred_fake = discriminator(reconstructed_x0.detach())
                    target_fake = torch.zeros_like(pred_fake, device=device)
                    loss_D_fake = gan_criterion(pred_fake, target_fake)

                    # Combine discriminator losses
                    loss_D_total = (loss_D_real + loss_D_fake) * 0.5 # Average real and fake loss

                scaler.scale(loss_D_total).backward() # Backprop D loss
                # Optional: Gradient clipping for D
                scaler.step(optimizer_D) # Update D weights
                epoch_loss_D += loss_D_total.item()

            # Update scaler after both potential steps (移出GAN条件块)
            scaler.update()

            global_step += 1
            epoch_loss_G += loss_G.item()

            # Update progress bar with loss information (与预训练模型格式一致)
            pbar.set_postfix(
                loss=f"{loss_G.item():.4f}",
                lr=f"{optimizer.param_groups[0]['lr']:.1e}"
            )

            # Log batch losses to TensorBoard (减少日志频率)
            log_interval = config['training'].get('log_interval', 50)  # 默认每50步记录一次
            if global_step % log_interval == 0:
                 writer.add_scalar('Loss/batch_G_total', loss_G.item(), global_step)
                 writer.add_scalar('Loss/batch_diffusion', diffusion_loss.item(), global_step)
                 writer.add_scalar('Loss/batch_perceptual', perceptual_loss.item(), global_step)
                 writer.add_scalar('Loss/batch_ssim', ssim_loss.item(), global_step)
                 writer.add_scalar('Loss/batch_gradient', gradient_loss.item(), global_step) # Log Gradient
                 writer.add_scalar('Loss/batch_G_adv', loss_G_adv.item(), global_step)
                 writer.add_scalar('Loss/batch_D_total', loss_D_total.item(), global_step)
                 writer.add_scalar('Meta/LR_G', optimizer.param_groups[0]['lr'], global_step)
                 if len(optimizer.param_groups) > 1: # Log encoder LR if fine-tuning
                      writer.add_scalar('Meta/LR_Encoder', optimizer.param_groups[1]['lr'], global_step)
                 if optimizer_D:
                      writer.add_scalar('Meta/LR_D', optimizer_D.param_groups[0]['lr'], global_step)


        # --- End of Epoch ---
        avg_epoch_loss_G = epoch_loss_G / len(dataloader)
        avg_epoch_diffusion_loss = epoch_diffusion_loss / len(dataloader)
        avg_epoch_perceptual_loss = epoch_perceptual_loss / len(dataloader)
        avg_epoch_ssim_loss = epoch_ssim_loss / len(dataloader)
        avg_epoch_gradient_loss = epoch_gradient_loss / len(dataloader) # Calculate avg Gradient
        avg_epoch_gan_G_loss = epoch_gan_G_loss / len(dataloader)
        avg_epoch_loss_D = epoch_loss_D / len(dataloader) if use_gan else 0

        # 与预训练模型保持一致的日志格式
        current_lr = optimizer.param_groups[0]['lr']
        print(f"Epoch {epoch+1}/{epochs} completed. Average Loss: {avg_epoch_loss_G:.6f}, Current LR: {current_lr:.6f}")
        print(f"  Diffusion: {avg_epoch_diffusion_loss:.6f} | Perceptual: {avg_epoch_perceptual_loss:.6f} | SSIM: {avg_epoch_ssim_loss:.6f} | Gradient: {avg_epoch_gradient_loss:.6f}")
        writer.add_scalar('Loss/epoch_G_total', avg_epoch_loss_G, epoch)
        writer.add_scalar('Loss/epoch_diffusion', avg_epoch_diffusion_loss, epoch)
        writer.add_scalar('Loss/epoch_perceptual', avg_epoch_perceptual_loss, epoch)
        writer.add_scalar('Loss/epoch_ssim', avg_epoch_ssim_loss, epoch)
        writer.add_scalar('Loss/epoch_gradient', avg_epoch_gradient_loss, epoch) # Log epoch Gradient
        writer.add_scalar('Loss/epoch_G_adv', avg_epoch_gan_G_loss, epoch)
        writer.add_scalar('Loss/epoch_D_total', avg_epoch_loss_D, epoch)

        # Step the scheduler (if applicable)
        if scheduler:
            # scheduler.step() # Or scheduler.step(avg_epoch_loss) for ReduceLROnPlateau
            pass # Placeholder for scheduler step logic

        # Check if this is the best model so far (following pretraining pattern)
        is_best = avg_epoch_loss_G < best_total_loss
        if is_best:
            best_total_loss = avg_epoch_loss_G
            best_epoch = epoch + 1
            print(f"New best loss: {best_total_loss:.6f}")

        # --- Validation (Placeholder) ---
        # if (epoch + 1) % config['training'].get('val_interval', 1) == 0:
        #     print("Running validation...")
        #     model.eval()
        #     val_loss = 0.0
        #     # with torch.no_grad():
        #     #     for val_lr, val_hr in val_dataloader:
        #     #         # Calculate validation loss (similar to training but without backprop)
        #     #         pass
        #     # avg_val_loss = val_loss / len(val_dataloader)
        #     # print(f"Epoch {epoch+1} Validation Loss: {avg_val_loss:.4f}")
        #     # writer.add_scalar('Loss/validation', avg_val_loss, epoch)
        #     # is_best = avg_val_loss < best_val_loss
        #     # if is_best:
        #     #     best_val_loss = avg_val_loss
        #     #     print("New best validation loss!")
        #     #     # Save best model checkpoint
        #     #     save_path = os.path.join(checkpoint_dir, 'best_model.pth')
        #     #     torch.save({ ... }, save_path)
        #     #     print(f"Saved best model to {save_path}")
        #     model.train() # Switch back to train mode

        # --- Save Checkpoint ---
        if (epoch + 1) % config['training'].get('save_interval', 10) == 0 or is_best:
            save_checkpoint(
                model=model,
                optimizer=optimizer,
                scheduler=scheduler,
                scaler=scaler,
                discriminator=discriminator,
                optimizer_D=optimizer_D,
                epoch=epoch + 1,
                global_step=global_step,
                total_loss=avg_epoch_loss_G,
                best_total_loss=best_total_loss,
                best_epoch=best_epoch,
                config=config,
                args=vars(args),
                checkpoint_dir=checkpoint_dir,
                is_best=is_best,
                use_gan=use_gan,
                use_amp=use_amp
            )

    # --- End of Training ---
    writer.close()
    print("Training finished!")

    # Save final model (following pretraining pattern)
    final_checkpoint_data = {
        'epoch': epochs,
        'global_step': global_step,
        'model_state_dict': model.state_dict(),
        'optimizer_state_dict': optimizer.state_dict(),
        'scheduler_state_dict': scheduler.state_dict() if scheduler else None,
        'scaler_state_dict': scaler.state_dict() if use_amp else None,
        'total_loss': avg_epoch_loss_G,
        'best_total_loss': best_total_loss,
        'best_epoch': best_epoch,
        'config': config,
        'args': vars(args),
    }

    if use_gan and discriminator and optimizer_D:
        final_checkpoint_data['discriminator_state_dict'] = discriminator.state_dict()
        final_checkpoint_data['optimizer_D_state_dict'] = optimizer_D.state_dict()

    # Save complete final model
    final_path = os.path.join(checkpoint_dir, 'final_model.pth')
    torch.save(final_checkpoint_data, final_path)
    print(f"Saved final complete model to: {final_path}")

    # Save final generator only (for inference)
    final_generator_path = os.path.join(checkpoint_dir, 'final_model_generator.pth')
    final_generator_data = {
        'epoch': epochs,
        'global_step': global_step,
        'model_state_dict': model.state_dict(),
        'total_loss': avg_epoch_loss_G,
        'best_total_loss': best_total_loss,
        'best_epoch': best_epoch,
        'config': config,
        'args': vars(args),
    }
    torch.save(final_generator_data, final_generator_path)
    print(f"Saved final generator model to: {final_generator_path}")

    print(f"Training completed! Best total loss: {best_total_loss:.6f} at epoch {best_epoch}")
    print(f"Models saved to: {checkpoint_dir}")
    print(f"Best model available at: {os.path.join(checkpoint_dir, 'best_model.pth')}")
    print(f"Best generator model available at: {os.path.join(checkpoint_dir, 'best_model_generator.pth')}")

    # 打印错误统计总结
    print("\n" + "="*50)
    print("训练过程错误统计:")
    print_error_summary()
    print("="*50)


if __name__ == '__main__':
    args = parse_args()
    config = load_config(args.config)
    # Optionally override config with command line args here
    train(config, args)
