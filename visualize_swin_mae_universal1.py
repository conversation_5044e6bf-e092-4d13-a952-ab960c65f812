import numpy as np
import torch
import matplotlib.pyplot as plt
from PIL import Image
import os
import argparse
import random
import cv2 # Added for image processing
import math # Added for calculations
from skimage.metrics import peak_signal_noise_ratio, structural_similarity # Added for metrics
import importlib.util
import sys
import types
import logging # Import logging

# Configure logging
logging.basicConfig(level=logging.INFO, format='%(asctime)s - %(levelname)s - %(message)s')

# --- Dynamic Model Loading Functions ---
def import_model_from_file(file_path, model_name="MaskedAutoencoderSwin"):
    """
    Dynamically import a model class from a Python file.

    Args:
        file_path: Path to the Python file containing the model class
        model_name: Name of the model class to import

    Returns:
        The imported model class
    """
    try:
        # Get the module name from the file path
        module_name = os.path.basename(file_path).replace('.py', '')

        # Load the module specification
        spec = importlib.util.spec_from_file_location(module_name, file_path)
        if spec is None:
            raise ImportError(f"Could not find module specification for {file_path}")

        # Create the module
        module = importlib.util.module_from_spec(spec)
        sys.modules[module_name] = module

        # Execute the module
        spec.loader.exec_module(module)

        # Get the model class
        model_class = getattr(module, model_name)

        # Also get the random_masking function if available
        random_masking_func = getattr(module, "random_masking", None)

        return model_class, random_masking_func
    except Exception as e:
        logging.error(f"Error importing model from {file_path}: {e}")
        raise

def determine_model_type(model_path):
    """
    Determine the type of model based on the checkpoint path.

    Args:
        model_path: Path to the model checkpoint

    Returns:
        A string indicating the model type: 'hierarchical', 'structured', or 'unknown'
    """
    model_type = 'unknown'

    # 强制检测为结构化模型，如果路径中包含w007
    if 'w007' in model_path.lower():
        model_type = 'structured'
        logging.info(f"Forced detection as structured model based on w007 in path")
        return model_type

    # Check if the checkpoint path contains keywords
    if 'hierarchical' in model_path.lower():
        model_type = 'hierarchical'
    elif 'structured' in model_path.lower() or 'nce' in model_path.lower():
        model_type = 'structured'

    # If we couldn't determine from the path, try to load the checkpoint and check its structure
    if model_type == 'unknown':
        try:
            checkpoint = torch.load(model_path, map_location='cpu')

            # First check if the model state dict contains structured-specific keys
            if 'model_state_dict' in checkpoint:
                state_dict = checkpoint['model_state_dict']
            else:
                state_dict = checkpoint  # Assume the checkpoint is the state dict itself

            # Check for structured-specific keys in the state dict
            structured_keys = ['decoder_mask_token', 'decoder_pos_embed', 'decoder.multi_scale_pos_embeds']
            has_structured_keys = False

            # 更详细地检查结构化特定键
            for k in state_dict.keys():
                for sk in structured_keys:
                    if sk in k:
                        has_structured_keys = True
                        logging.info(f"Found structured key: {k}")
                        break
                if has_structured_keys:
                    break

            if has_structured_keys:
                model_type = 'structured'
                logging.info(f"Detected structured model based on state dict keys")
            else:
                # Check args if we still don't know
                if 'args' in checkpoint and checkpoint['args'] is not None:
                    ckpt_args = checkpoint['args']
                    # Convert to dict if it's a Namespace
                    if isinstance(ckpt_args, argparse.Namespace):
                        ckpt_args = vars(ckpt_args)

                    # Check for specific parameters that might indicate the model type
                    if 'patchnce_loss_weight' in ckpt_args and float(ckpt_args['patchnce_loss_weight']) > 0:
                        model_type = 'structured'
                        logging.info(f"Detected structured model based on patchnce_loss_weight > 0")
                    elif 'ssim_loss_weight' in ckpt_args and float(ckpt_args['ssim_loss_weight']) > 0:
                        if model_type == 'unknown':  # Don't override structured detection
                            model_type = 'hierarchical'
                            logging.info(f"Detected hierarchical model based on ssim_loss_weight > 0")
        except Exception as e:
            logging.warning(f"Could not determine model type from checkpoint: {e}")

    # 检查unexpected_keys是否包含结构化模型的特定键
    try:
        checkpoint = torch.load(model_path, map_location='cpu')

        # 直接检查checkpoint中的键
        if 'model_state_dict' in checkpoint:
            state_dict = checkpoint['model_state_dict']
        else:
            state_dict = checkpoint

        # 直接检查state_dict中的键
        structured_indicators = ['decoder_mask_token', 'decoder_pos_embed', 'multi_scale_pos_embeds']
        for key in state_dict.keys():
            for indicator in structured_indicators:
                if indicator in key:
                    model_type = 'structured'
                    logging.info(f"Detected structured model based on key: {key}")
                    break
            if model_type == 'structured':
                break
    except Exception as e:
        logging.warning(f"Error during additional model type detection: {e}")

    logging.info(f"Final determined model type: {model_type}")
    return model_type

def load_appropriate_model(model_path, device):
    """
    Load the appropriate model based on the model path.

    Args:
        model_path: Path to the model checkpoint
        device: Device to load the model on

    Returns:
        The loaded model and the random_masking function
    """
    model_type = determine_model_type(model_path)

    # Define paths to the training scripts
    hierarchical_script = "train_swin_mae_resnet_random_mask_hierarchical.py"
    structured_script = "train_swin_mae_resnet_random_mask_structured.py"

    # Choose the appropriate script based on the model type
    script_path = structured_script if model_type == 'structured' else hierarchical_script

    # 检查脚本是否存在
    if not os.path.exists(script_path):
        logging.error(f"Training script not found: {script_path}")
        # 如果找不到结构化脚本，尝试使用层次化脚本
        if model_type == 'structured' and os.path.exists(hierarchical_script):
            logging.warning(f"Falling back to hierarchical script: {hierarchical_script}")
            script_path = hierarchical_script
        else:
            raise FileNotFoundError(f"Training script not found: {script_path}")

    # Import the model class and random_masking function
    model_class, random_masking_func = import_model_from_file(script_path)

    # Load the checkpoint
    checkpoint = torch.load(model_path, map_location='cpu')

    # Extract model parameters from the checkpoint
    if 'args' in checkpoint and checkpoint['args'] is not None:
        ckpt_args = checkpoint['args']
        # Convert to dict if it's a Namespace
        if isinstance(ckpt_args, argparse.Namespace):
            ckpt_args = vars(ckpt_args)
        elif not isinstance(ckpt_args, dict):
            logging.warning(f"Checkpoint 'args' is of unexpected type: {type(ckpt_args)}. Using default parameters.")
            ckpt_args = {}
    else:
        logging.warning("Checkpoint does not contain 'args'. Using default parameters.")
        ckpt_args = {}

    # Extract model parameters with defaults
    img_size = ckpt_args.get('img_size', 256)
    patch_size = ckpt_args.get('patch_size', 4)
    swin_embed_dim = ckpt_args.get('swin_embed_dim', 96)
    swin_depths = ckpt_args.get('swin_depths', [2, 2, 6, 2])
    swin_num_heads = ckpt_args.get('swin_num_heads', [3, 6, 12, 24])
    swin_window_size = ckpt_args.get('swin_window_size', 7)
    swin_ape = ckpt_args.get('swin_ape', True)
    decoder_embed_dim = ckpt_args.get('decoder_embed_dim', 128)
    decoder_depths = ckpt_args.get('decoder_depths', [1, 1, 1, 1])
    decoder_num_heads = ckpt_args.get('decoder_num_heads', [8, 8, 4, 2])
    norm_pix_loss = ckpt_args.get('norm_pix_loss', False)
    perceptual_loss_weight = ckpt_args.get('perceptual_loss_weight', 0.005)
    ssim_loss_weight = ckpt_args.get('ssim_loss_weight', 0.0)
    patchnce_loss_weight = ckpt_args.get('patchnce_loss_weight', 0.0)
    nce_proj_dim = ckpt_args.get('nce_proj_dim', 256)
    nce_T = ckpt_args.get('nce_T', 0.07)

    # 如果是w007模型，强制设置为结构化模型
    if 'w007' in model_path.lower():
        model_type = 'structured'
        logging.info(f"Forced model type to 'structured' based on w007 in path")

    # Log the parameters
    logging.info(f"Model parameters: img_size={img_size}, patch_size={patch_size}, swin_embed_dim={swin_embed_dim}, "
                f"decoder_embed_dim={decoder_embed_dim}, decoder_depths={decoder_depths}, "
                f"decoder_num_heads={decoder_num_heads}, perceptual_loss_weight={perceptual_loss_weight}, "
                f"ssim_loss_weight={ssim_loss_weight}, patchnce_loss_weight={patchnce_loss_weight}")

    # Create model kwargs based on the model type
    model_kwargs = {
        'img_size': img_size,
        'patch_size': patch_size,
        'in_chans': 1,
        'embed_dim': swin_embed_dim,
        'depths': swin_depths,
        'num_heads': swin_num_heads,
        'window_size': swin_window_size,
        'mlp_ratio': 4.0,
        'norm_layer': torch.nn.LayerNorm,
        'ape': swin_ape,
        'patch_norm': True,
        'decoder_embed_dim': decoder_embed_dim,
        'decoder_depths': decoder_depths,
        'decoder_num_heads': decoder_num_heads,
        'norm_pix_loss': norm_pix_loss,
        'perceptual_loss_weight': perceptual_loss_weight,
    }

    # Add model-specific parameters
    if model_type == 'hierarchical':
        model_kwargs['ssim_loss_weight'] = ssim_loss_weight
    elif model_type == 'structured':
        model_kwargs['ssim_loss_weight'] = ssim_loss_weight
        model_kwargs['patchnce_loss_weight'] = patchnce_loss_weight
        model_kwargs['nce_proj_dim'] = nce_proj_dim
        model_kwargs['nce_T'] = nce_T

    # 尝试实例化模型
    try:
        # 首先尝试使用完整参数实例化模型
        model = model_class(**model_kwargs).to(device)
        logging.info(f"Successfully instantiated model with full parameters")
    except Exception as e:
        logging.warning(f"Error instantiating model with full parameters: {e}")
        # 如果失败，尝试移除可能导致问题的参数
        problematic_params = ['nce_proj_dim', 'nce_T']
        for param in problematic_params:
            if param in model_kwargs:
                del model_kwargs[param]

        try:
            model = model_class(**model_kwargs).to(device)
            logging.info(f"Successfully instantiated model with reduced parameters")
        except Exception as e2:
            logging.error(f"Error instantiating model with reduced parameters: {e2}")
            raise

    # Load the state dict
    if 'model_state_dict' in checkpoint:
        state_dict = checkpoint['model_state_dict']
    else:
        state_dict = checkpoint  # Assume the checkpoint is the state dict itself

    new_state_dict = {}
    for k, v in state_dict.items():
        name = k[7:] if k.startswith('module.') else k  # remove module. prefix
        new_state_dict[name] = v

    # 检查是否有结构化特定的键
    structured_keys = ['decoder_mask_token', 'decoder_pos_embed', 'multi_scale_pos_embeds']
    has_structured_keys = False
    for k in new_state_dict.keys():
        for sk in structured_keys:
            if sk in k:
                has_structured_keys = True
                logging.info(f"Found structured key in state dict: {k}")
                break
        if has_structured_keys:
            break

    # 如果检测到结构化键但模型类型不是结构化，更新模型类型
    if has_structured_keys and model_type != 'structured':
        logging.warning(f"Detected structured keys in state dict but model type is {model_type}. Updating to 'structured'.")
        model_type = 'structured'

    # Load the state dict with strict=False to handle missing/unexpected keys
    msg = model.load_state_dict(new_state_dict, strict=False)
    logging.info(f"Load state dict message: {msg}")

    # 检查是否有未使用的键，这可能表明模型类型不匹配
    if msg.unexpected_keys:
        logging.warning(f"Unexpected keys in state dict: {msg.unexpected_keys}")
        # 检查是否有结构化特定的键
        for key in msg.unexpected_keys:
            for sk in structured_keys:
                if sk in key:
                    logging.warning(f"Found structured key in unexpected keys: {key}")
                    if model_type != 'structured':
                        logging.warning(f"Updating model type to 'structured'")
                        model_type = 'structured'
                    break

    # Set the model to evaluation mode
    model.eval()

    # Create a compatible forward_encoder method
    def compatible_forward_encoder(self, x, mask_ratio=0):
        """
        Compatible forward_encoder method that handles both old and new interfaces.

        Args:
            x: Input tensor
            mask_ratio: Mask ratio

        Returns:
            A tuple of (latent_full, intermediate_features) for old interface
            or (latent_full, mask, intermediate_features, ids_restore) for new interface
        """
        # Check the signature of the original forward_encoder method
        import inspect
        sig = inspect.signature(self.original_forward_encoder)
        params = list(sig.parameters.keys())

        # 检查是否是结构化模型
        is_structured_model = (
            hasattr(self, 'decoder_mask_token') and self.decoder_mask_token is not None or
            hasattr(self, 'decoder_pos_embed') and self.decoder_pos_embed is not None or
            hasattr(self.decoder, 'multi_scale_pos_embeds') if hasattr(self, 'decoder') else False
        )

        # 记录模型类型
        model_type = "structured" if is_structured_model else "hierarchical"
        logging.info(f"Compatible forward_encoder detected model type: {model_type}")

        # Call the original forward_encoder method with appropriate arguments
        try:
            if len(params) > 1 and 'mask_ratio' in params:
                # New interface: forward_encoder(x, mask_ratio)
                result = self.original_forward_encoder(x, mask_ratio)
                logging.info(f"Called forward_encoder with mask_ratio parameter")
            else:
                # Old interface: forward_encoder(x)
                result = self.original_forward_encoder(x)
                logging.info(f"Called forward_encoder without mask_ratio parameter")
        except Exception as e:
            logging.error(f"Error calling original forward_encoder: {e}")
            # 尝试不同的调用方式
            if len(params) > 1 and 'mask_ratio' in params:
                # 尝试不带mask_ratio参数调用
                result = self.original_forward_encoder(x)
                logging.info(f"Fallback: Called forward_encoder without mask_ratio parameter")
            else:
                # 尝试带mask_ratio参数调用
                result = self.original_forward_encoder(x, mask_ratio)
                logging.info(f"Fallback: Called forward_encoder with mask_ratio parameter")

        # Check the number of return values
        if isinstance(result, tuple):
            if len(result) == 2:
                # Old interface: (latent_full, intermediate_features)
                logging.info(f"Forward encoder returned 2 values (latent_full, intermediate_features)")
                return result
            elif len(result) == 4:
                # New interface: (latent_full, mask, intermediate_features, ids_restore)
                latent_full, mask, intermediate_features, ids_restore = result
                logging.info(f"Forward encoder returned 4 values (latent_full, mask, intermediate_features, ids_restore)")

                # 根据调用者的需求返回不同的结果
                if is_structured_model:
                    # 结构化模型需要所有4个返回值
                    logging.info(f"Returning all 4 values for structured model")
                    return latent_full, mask, intermediate_features, ids_restore
                else:
                    # 层次化模型只需要2个返回值
                    logging.info(f"Returning 2 values for hierarchical model")
                    return latent_full, intermediate_features
            else:
                logging.warning(f"Unexpected number of return values from forward_encoder: {len(result)}")
                # 尝试提取最基本的返回值
                if len(result) > 0:
                    latent_full = result[0]
                    intermediate_features = result[2] if len(result) > 2 else None
                    return latent_full, intermediate_features
                else:
                    raise ValueError(f"Empty tuple returned from forward_encoder")
        else:
            # 单返回值情况
            logging.info(f"Forward encoder returned single value")
            return result, None

    # Save the original forward_encoder method
    model.original_forward_encoder = model.forward_encoder

    # Replace with the compatible version
    model.forward_encoder = types.MethodType(compatible_forward_encoder, model)

    # If random_masking_func is None, use a default implementation
    if random_masking_func is None:
        def default_random_masking(N, L, mask_ratio, device):
            """Default random masking implementation."""
            len_keep = int(L * (1 - mask_ratio))
            noise = torch.rand(N, L, device=device)
            ids_shuffle = torch.argsort(noise, dim=1)
            ids_restore = torch.argsort(ids_shuffle, dim=1)
            mask = torch.ones([N, L], device=device)
            mask[:, :len_keep] = 0
            mask = torch.gather(mask, dim=1, index=ids_restore)
            return mask

        random_masking_func = default_random_masking

    return model, random_masking_func, model_type

# --- Helper Functions for Visualization ---
def show_image(image, title=''):
    """Helper function to display an image (handles 2D grayscale or 3D HWC), fixing display range."""
    # Ensure image is numpy array for consistent handling
    if isinstance(image, torch.Tensor):
        image = image.numpy()

    # Determine vmin and vmax based on dtype or content if needed, but default to [0, 1] for normalized float data
    vmin, vmax = 0, 1

    if image.ndim == 2:
        # Grayscale image (H, W)
        plt.imshow(image, cmap='gray', vmin=vmin, vmax=vmax)
    elif image.ndim == 3:
        # Assumes (H, W, C)
        assert image.shape[2] == 1 or image.shape[2] == 3, f"Expected 1 or 3 channels, got shape {image.shape}"
        # Squeeze potential single channel dimension for imshow
        img_to_show = image.squeeze()
        # If still 3D after squeeze (e.g., RGB), imshow handles it. If 2D, cmap applies.
        plt.imshow(img_to_show, cmap='gray', vmin=vmin, vmax=vmax)
    else:
        raise ValueError(f"Unsupported image shape: {image.shape}")

    plt.title(title, fontsize=10)
    plt.axis('off')
    return

def run_one_image(img_tensor_normalized_minus1_1, model, random_masking_func, mask_ratio, device, patch_hu_min, patch_hu_max, model_type='unknown'):
    """
    Run Swin-MAE model on a single image tensor normalized to [-1, 1] based on ITS OWN patch range.
    Returns the reconstruction denormalized back to the PATCH HU range [patch_hu_min, patch_hu_max]
    and the binary mask (1 = masked/removed, 0 = visible).

    Args:
        img_tensor_normalized_minus1_1: Input image tensor normalized to [-1, 1]
        model: The Swin-MAE model
        random_masking_func: Function to generate random masks
        mask_ratio: Mask ratio
        device: Device to run the model on
        patch_hu_min: Minimum HU value of the patch
        patch_hu_max: Maximum HU value of the patch
        model_type: Type of the model ('hierarchical', 'structured', or 'unknown')

    Returns:
        recon_hu: Reconstruction in HU scale
        mask_img: Binary mask image
    """
    x = img_tensor_normalized_minus1_1.unsqueeze(0)  # Add batch dimension [1, C, H, W]
    x = x.to(device, non_blocking=True)
    N, C_in, _, _ = x.shape  # We only need batch size and channels
    logging.info(f"run_one_image - Input tensor (x) shape: {x.shape}")
    logging.info(f"run_one_image - Input tensor (x) range (normalized [-1,1] using PATCH range): min={x.min():.4f}, max={x.max():.4f}")

    # Run Swin-MAE forward pass to get prediction and mask
    with torch.no_grad():
        try:
            # 专门处理结构化模型
            if model_type == 'structured':
                logging.info(f"Processing as structured model")

                # 检查模型是否有结构化特定的属性
                has_structured_features = (
                    hasattr(model, 'decoder_mask_token') or
                    hasattr(model, 'decoder_pos_embed') or
                    hasattr(model.decoder, 'multi_scale_pos_embeds')
                )

                if has_structured_features:
                    logging.info(f"Model has structured features")

                    # 初始化控制变量
                    skip_decoder = False

                    # 1. 使用encoder获取特征和掩码
                    try:
                        # 尝试使用forward_encoder，处理结构化模型返回4个值的情况
                        encoder_result = model.forward_encoder(x, mask_ratio)

                        # 检查返回值数量
                        if isinstance(encoder_result, tuple):
                            if len(encoder_result) == 4:  # 结构化模型返回4个值
                                latent_full, mask_binary, intermediate_features, _ = encoder_result
                                logging.info(f"Successfully called structured forward_encoder with 4 return values")
                            elif len(encoder_result) == 2:  # 层次化模型返回2个值
                                latent_full, intermediate_features = encoder_result
                                # 生成掩码
                                mask_binary = random_masking_func(N, model.num_patches, mask_ratio, x.device)
                                logging.info(f"Successfully called hierarchical forward_encoder with 2 return values")
                            else:
                                raise ValueError(f"Unexpected number of return values from forward_encoder: {len(encoder_result)}")
                        else:
                            # 单返回值情况（不太可能）
                            latent_full = encoder_result
                            intermediate_features = None
                            # 生成掩码
                            mask_binary = random_masking_func(N, model.num_patches, mask_ratio, x.device)
                            logging.info(f"Successfully called forward_encoder with single return value")

                        logging.info(f"Successfully processed encoder output")
                    except Exception as e:
                        logging.warning(f"Error in encoder step: {e}")
                        # 如果失败，尝试直接使用forward
                        try:
                            _, pred, mask_binary = model(x, mask_ratio)
                            logging.info(f"Successfully used direct forward call")
                            # 跳过decoder步骤
                            skip_decoder = True
                        except Exception as e2:
                            logging.error(f"Both encoder approaches failed: {e2}")
                            raise

                    # 2. 使用decoder重建图像
                    if not skip_decoder:
                        try:
                            # 尝试使用结构化decoder
                            logging.info(f"Attempting structured decoder call")

                            # 准备decoder位置编码
                            decoder_pos_embed = None
                            if hasattr(model, 'decoder_pos_embed'):
                                decoder_pos_embed = model.decoder_pos_embed
                                if hasattr(model, 'decoder_pos_drop'):
                                    decoder_pos_embed = model.decoder_pos_drop(decoder_pos_embed)

                            # 准备decoder掩码令牌
                            decoder_mask_token = None
                            if hasattr(model, 'decoder_mask_token'):
                                decoder_mask_token = model.decoder_mask_token

                            # 调用decoder
                            if decoder_pos_embed is not None and decoder_mask_token is not None:
                                try:
                                    # 尝试使用完整的结构化参数调用
                                    decoder_output = model.forward_decoder(
                                        latent_full,
                                        intermediate_features,
                                        decoder_pos_embed,
                                        mask_binary,
                                        decoder_mask_token
                                    )
                                    logging.info(f"Called decoder with full structured parameters")
                                except Exception as e:
                                    logging.warning(f"Error calling decoder with full parameters: {e}")
                                    # 尝试使用不同的参数顺序
                                    try:
                                        # 有些模型可能使用不同的参数顺序
                                        decoder_output = model.forward_decoder(
                                            latent_full,
                                            intermediate_features,
                                            mask_binary,
                                            decoder_pos_embed,
                                            decoder_mask_token
                                        )
                                        logging.info(f"Called decoder with alternative parameter order")
                                    except Exception as e2:
                                        logging.warning(f"Error with alternative parameter order: {e2}")
                                        # 回退到基本调用
                                        decoder_output = model.forward_decoder(latent_full, intermediate_features)
                                        logging.info(f"Fallback to basic decoder call")
                            else:
                                # 如果缺少结构化参数，尝试简单调用
                                decoder_output = model.forward_decoder(latent_full, intermediate_features)
                                logging.info(f"Called decoder with basic parameters")

                            # 提取预测结果
                            if isinstance(decoder_output, tuple):
                                if len(decoder_output) == 2:
                                    # 结构化模型通常返回(pred, x_normalized)
                                    pred = decoder_output[0]
                                    logging.info(f"Extracted prediction from 2-tuple decoder output")
                                else:
                                    # 其他情况，取第一个元素
                                    pred = decoder_output[0]
                                    logging.info(f"Extracted prediction from {len(decoder_output)}-tuple decoder output")
                            else:
                                pred = decoder_output
                                logging.info(f"Using direct decoder output as prediction")
                        except Exception as e:
                            logging.warning(f"Structured decoder call failed: {e}. Trying simple decoder.")
                            # 尝试简单decoder调用
                            decoder_output = model.forward_decoder(latent_full, intermediate_features)
                            if isinstance(decoder_output, tuple):
                                pred = decoder_output[0]
                            else:
                                pred = decoder_output
                else:
                    # 没有结构化特性，尝试直接使用forward
                    logging.info(f"No structured features detected, using direct forward call")
                    try:
                        _, pred, mask_binary = model(x, mask_ratio)
                    except Exception as e:
                        logging.warning(f"Direct forward call failed: {e}. Trying encoder-decoder approach.")
                        # 尝试encoder-decoder方法
                        latent_full, intermediate_features = model.forward_encoder(x, mask_ratio)
                        mask_binary = random_masking_func(N, model.num_patches, mask_ratio, x.device)
                        decoder_output = model.forward_decoder(latent_full, intermediate_features)
                        if isinstance(decoder_output, tuple):
                            pred = decoder_output[0]
                        else:
                            pred = decoder_output
            else:
                # 处理层次化模型
                logging.info(f"Processing as hierarchical model")

                # 1. Encoder - Call forward_encoder
                latent_full, intermediate_features = model.forward_encoder(x, mask_ratio)

                # 2. Generate mask for visualization
                mask_binary = random_masking_func(N, model.num_patches, mask_ratio, x.device)

                # 3. Decoder - Project latent features and decode
                decoder_output = model.forward_decoder(latent_full, intermediate_features)
                if isinstance(decoder_output, tuple):
                    pred = decoder_output[0]
                else:
                    pred = decoder_output

            # Verify dimensions
            N, L_initial, C_pred = pred.shape
            p = model.patch_size
            C_pix = model.in_chans

            assert L_initial == model.num_patches, f"Prediction sequence length mismatch: {L_initial} vs {model.num_patches}"
            assert C_pred == p**2 * C_pix, f"Prediction channel mismatch: {C_pred} vs {p**2 * C_pix}"

            # Convert sequence back to image format
            pred_img = model.unpatchify(pred)  # Shape: [N, C, H, W]

            # Post-processing for visualization
            y_pred_norm = pred_img.detach().cpu()

            # Unpatchify the mask for visualization
            mask_img = mask_binary.unsqueeze(-1).repeat(1, 1, model.patch_size**2 * C_in)  # (N, L, p*p*C)
            mask_img = model.unpatchify(mask_img)  # Shape: [N, C, H, W], 1 is removing, 0 is keeping
            mask_img = mask_img[:, 0:1, :, :]  # Shape: [N, 1, H, W]

        except Exception as e:
            logging.error(f"Error during model inference: {e}", exc_info=True)
            raise

    # Log reconstruction info
    logging.info(f"run_one_image - Reconstruction (y_pred_norm) shape: {y_pred_norm.shape}")
    logging.info(f"run_one_image - Reconstruction (y_pred_norm) range (normalized [-1,1]): min={y_pred_norm.min():.4f}, max={y_pred_norm.max():.4f}")

    # Denormalize reconstruction back to PATCH HU scale
    patch_hu_mean = (patch_hu_max + patch_hu_min) / 2.0
    patch_hu_half_range = (patch_hu_max - patch_hu_min) / 2.0
    if patch_hu_half_range == 0:  # Avoid division by zero
        logging.warning("Patch HU min and max are equal, denormalizing to patch_hu_min.")
        recon_hu = torch.full_like(y_pred_norm, patch_hu_min)
    else:
        recon_hu = y_pred_norm * patch_hu_half_range + patch_hu_mean

    logging.info(f"run_one_image - Denormalized Reconstruction (recon_hu) range (Patch HU scale [{patch_hu_min:.2f}, {patch_hu_max:.2f}]): min={recon_hu.min():.2f}, max={recon_hu.max():.2f}")

    # Return reconstruction in HU scale and the binary mask image
    return recon_hu, mask_img  # Shape: [1, C, H, W], [1, 1, H, W]

def visualize_swin_mae_reconstruction(model_path, image_path, output_dir, hu_min, hu_max, mask_ratios, args):
    """
    Universal visualization function for Swin-MAE models.
    Works with both hierarchical and structured models.

    Args:
        model_path: Path to the model checkpoint
        image_path: Path to the input image
        output_dir: Directory to save the visualization
        hu_min: Minimum HU value for global visualization
        hu_max: Maximum HU value for global visualization
        mask_ratios: List of mask ratios to visualize
        args: Command line arguments
    """
    # Initialize variables
    original_seed = None

    device = torch.device('cuda' if torch.cuda.is_available() else 'cpu')
    logging.info(f"Using device: {device}")

    # Load the appropriate model
    try:
        model, random_masking_func, model_type = load_appropriate_model(model_path, device)
        model.eval()
        logging.info(f"Model loaded successfully. Type: {model_type}")
    except Exception as e:
        logging.error(f"Failed to load model: {e}", exc_info=True)
        return

    # Load and preprocess the image
    try:
        # Load the image using cv2.imdecode to handle non-ASCII paths
        try:
            with open(image_path, 'rb') as f:
                file_bytes = np.frombuffer(f.read(), dtype=np.uint8)
            img_cv = cv2.imdecode(file_bytes, cv2.IMREAD_UNCHANGED)
            if img_cv is None:
                raise ValueError("cv2.imdecode returned None")
        except Exception as e_load:
            logging.error(f"Failed to load image: {e_load}", exc_info=True)
            return

        logging.info(f"Loaded image: shape={img_cv.shape}, dtype={img_cv.dtype}")

        # Log original image HU range
        min_val_orig, max_val_orig = np.min(img_cv), np.max(img_cv)
        logging.info(f"Original full image HU range: min={min_val_orig}, max={max_val_orig}")

        # Handle multi-channel TIF
        if img_cv.ndim == 3:
            logging.warning(f"Loaded TIF has multiple channels ({img_cv.shape[2]}), using the first channel.")
            img_cv = img_cv[:, :, 0]

        original_h, original_w = img_cv.shape[:2]
        logging.info(f"Original image dimensions (H, W): {original_h}x{original_w}")

        # Extract center patch
        img_size = model.img_size if hasattr(model, 'img_size') else 256
        center_x = original_w // 2
        center_y = original_h // 2

        # Calculate top-left corner for the center patch
        left = center_x - img_size // 2
        top = center_y - img_size // 2
        right = left + img_size
        bottom = top + img_size

        # Ensure coordinates are within bounds
        left = max(0, left)
        top = max(0, top)
        right = min(original_w, right)
        bottom = min(original_h, bottom)

        # Extract patch
        img_cropped_np = img_cv[top:bottom, left:right].astype(np.float32)
        logging.info(f"Extracted center patch {img_cropped_np.shape} from area: ({left}, {top}, {right}, {bottom})")

        # Log patch HU range
        patch_min_hu, patch_max_hu = np.min(img_cropped_np), np.max(img_cropped_np)
        logging.info(f"Original Patch HU range: min={patch_min_hu:.2f}, max={patch_max_hu:.2f}")

        # Determine visualization range
        if args.vis_hu_mode == 'patch':
            display_vmin = patch_min_hu
            display_vmax = patch_max_hu
            vis_range_str = f"Patch HU: [{display_vmin:.0f}, {display_vmax:.0f}]"
            logging.info(f"Using PATCH HU range for visualization: [{display_vmin:.2f}, {display_vmax:.2f}]")
        elif args.vis_hu_mode == 'global':
            display_vmin = hu_min
            display_vmax = hu_max
            vis_range_str = f"Global HU: [{display_vmin:.0f}, {display_vmax:.0f}]"
            logging.info(f"Using GLOBAL HU range for visualization: [{display_vmin:.2f}, {display_vmax:.2f}]")
        else:
            logging.error(f"Invalid vis_hu_mode: {args.vis_hu_mode}. Defaulting to global.")
            display_vmin = hu_min
            display_vmax = hu_max
            vis_range_str = f"Global HU: [{display_vmin:.0f}, {display_vmax:.0f}]"

        # Normalize patch to [-1, 1] for model input
        patch_hu_mean = (patch_max_hu + patch_min_hu) / 2.0
        patch_hu_half_range = (patch_max_hu - patch_min_hu) / 2.0

        if patch_hu_half_range == 0:
            logging.warning("Patch HU min and max are equal. Normalizing input to 0.")
            img_tensor_minus1_1 = torch.zeros_like(torch.from_numpy(img_cropped_np)).unsqueeze(0)
        else:
            img_normalized_for_model_np = (img_cropped_np - patch_hu_mean) / patch_hu_half_range
            img_normalized_for_model_np = np.clip(img_normalized_for_model_np, -1.0, 1.0)
            img_tensor_minus1_1 = torch.from_numpy(img_normalized_for_model_np).unsqueeze(0)

        logging.info(f"Patch range after normalization: min={img_tensor_minus1_1.min():.4f}, max={img_tensor_minus1_1.max():.4f}")

    except Exception as e:
        logging.error(f"Exception during image processing: {e}", exc_info=True)
        return

    # Run inference and visualization for each mask ratio
    num_ratios = len(mask_ratios)
    num_cols = 5  # Original, Masked, Recon, Paste, Difference
    plt.style.use('seaborn-v0_8-whitegrid')
    plt.rcParams.update({
        'font.family': 'serif', 'font.serif': ['Times New Roman'],
        'figure.figsize': (num_cols * 2.5, num_ratios * 2.7),
        'figure.dpi': 150,
        'font.size': 8, 'axes.titlesize': 10,
    })
    fig = plt.figure()

    logging.info(f"Running MAE inference for mask ratios: {mask_ratios}")

    # For structured models, we need to set a fixed random seed for each mask ratio
    # to ensure consistent masking patterns across different runs
    if model_type == 'structured':
        # Save the original seed
        original_seed = torch.initial_seed()
        logging.info(f"Saved original random seed: {original_seed}")

    for i, mask_ratio in enumerate(mask_ratios):
        logging.info(f"  Processing mask ratio: {mask_ratio}")

        # For structured models, set a fixed seed for each mask ratio
        if model_type == 'structured':
            # Set a fixed seed based on mask ratio to ensure reproducibility
            seed = int(mask_ratio * 1000)
            logging.info(f"  Setting fixed seed for mask_ratio {mask_ratio}: {seed}")
            torch.manual_seed(seed)
            if torch.cuda.is_available():
                torch.cuda.manual_seed(seed)

        try:
            # Get reconstruction and mask
            recon_hu_tensor, mask_binary_tensor = run_one_image(
                img_tensor_minus1_1, model, random_masking_func, mask_ratio, device,
                patch_min_hu, patch_max_hu, model_type
            )

            # Process tensors to numpy arrays - make sure to move to CPU first
            recon_hu_np = recon_hu_tensor.cpu().squeeze(0).permute(1, 2, 0).numpy()
            mask_binary_np = mask_binary_tensor.cpu().squeeze(0).squeeze(0).numpy()

            # Ensure recon_hu_np is [H, W] if input was grayscale
            if recon_hu_np.shape[2] == 1:
                recon_hu_np = recon_hu_np.squeeze(axis=2)

        except Exception as e:
            logging.error(f"Error during model inference for ratio {mask_ratio}: {e}", exc_info=True)
            continue

        # Create derived images for visualization
        masked_display = img_cropped_np * (1 - mask_binary_np) + display_vmin * mask_binary_np
        paste_hu = img_cropped_np * (1 - mask_binary_np) + recon_hu_np * mask_binary_np
        diff_map_hu = np.abs(img_cropped_np - recon_hu_np) * mask_binary_np
        diff_max_val = np.max(diff_map_hu) if np.any(mask_binary_np) else 0
        logging.info(f"  Max absolute difference in masked area (HU): {diff_max_val:.2f}")

        # Calculate PSNR and SSIM
        data_range_metrics = display_vmax - display_vmin
        if data_range_metrics <= 0:
            logging.warning(f"  Data range for metrics is non-positive ({data_range_metrics:.2f}), skipping PSNR/SSIM calculation.")
            psnr_val = float('nan')
            ssim_val = float('nan')
        else:
            try:
                psnr_val = peak_signal_noise_ratio(img_cropped_np.astype(np.float32), recon_hu_np.astype(np.float32), data_range=data_range_metrics)
                ssim_val = structural_similarity(img_cropped_np.astype(np.float32), recon_hu_np.astype(np.float32), data_range=data_range_metrics)
                logging.info(f"  Metrics (data_range={data_range_metrics:.2f}): PSNR = {psnr_val:.4f}, SSIM = {ssim_val:.4f}")
            except Exception as e_metrics:
                logging.error(f"  Error calculating metrics for ratio {mask_ratio}: {e_metrics}", exc_info=True)
                psnr_val = float('nan')
                ssim_val = float('nan')

        # --- Plotting for this ratio ---
        plot_row_start = i * num_cols + 1

        # 1. Original Patch
        plt.subplot(num_ratios, num_cols, plot_row_start)
        plt.imshow(img_cropped_np, cmap='gray', vmin=display_vmin, vmax=display_vmax)
        plt.title(f"Original Patch\n(Vis Range: {vis_range_str})")
        plt.axis('off')

        # 2. Masked Patch
        plt.subplot(num_ratios, num_cols, plot_row_start + 1)
        plt.imshow(masked_display, cmap='gray', vmin=display_vmin, vmax=display_vmax)
        plt.title(f"Masked ({int(mask_ratio*100)}%)\n(Masked = {display_vmin:.0f} HU)")
        plt.axis('off')

        # 3. Reconstruction
        plt.subplot(num_ratios, num_cols, plot_row_start + 2)
        plt.imshow(recon_hu_np, cmap='gray', vmin=display_vmin, vmax=display_vmax)
        plt.title(f"Reconstruction\n(Vis: {vis_range_str})\nPSNR: {psnr_val:.2f}, SSIM: {ssim_val:.3f}")
        plt.axis('off')

        # 4. Reconstruction + Visible (Paste)
        plt.subplot(num_ratios, num_cols, plot_row_start + 3)
        plt.imshow(paste_hu, cmap='gray', vmin=display_vmin, vmax=display_vmax)
        plt.title(f"Recon + Visible\n(Vis Range: {vis_range_str})")
        plt.axis('off')

        # 5. Difference Map
        plt.subplot(num_ratios, num_cols, plot_row_start + 4)
        plt.imshow(diff_map_hu, cmap='viridis', vmin=0, vmax=max(1, diff_max_val))
        plt.title(f"Abs Difference (HU)\n(Max: {diff_max_val:.0f})")
        plt.axis('off')

    plt.tight_layout(pad=0.5, h_pad=1.5)

    # Restore original random seed if we saved it
    if original_seed is not None:
        logging.info(f"Restoring original random seed: {original_seed}")
        torch.manual_seed(original_seed)
        if torch.cuda.is_available():
            torch.cuda.manual_seed(original_seed)

    # Save visualization
    os.makedirs(output_dir, exist_ok=True)
    ratios_str = "_".join(map(str, mask_ratios)).replace('.', 'p')
    vis_type_str = args.vis_hu_mode + "vis"

    # Determine model type for filename
    model_type_str = model_type if model_type != 'unknown' else 'universal'

    output_filename = f"swin_mae_{model_type_str}_reconstruction_{vis_type_str}_{display_vmin:.0f}_{display_vmax:.0f}_ratios_{ratios_str}_{os.path.basename(image_path).split('.')[0]}.png"
    output_path = os.path.join(output_dir, output_filename)

    try:
        plt.savefig(output_path, bbox_inches='tight')
        logging.info(f"Visualization saved to: {output_path}")
    except Exception as e:
        logging.error(f"Failed to save visualization: {e}", exc_info=True)
    finally:
        plt.close(fig)


if __name__ == '__main__':
    parser = argparse.ArgumentParser('Universal Swin-MAE Visualization Script')
    parser.add_argument('--model_path', default='checkpoints/swin_mae_hierarchical_random_ssim_w005/best_model.pth',
                        type=str, help='Path to Swin-MAE pretrained model checkpoint')
    parser.add_argument('--image_path', default='E:/vscode/2号CT数据/19p44um-2700x2700x2000-0750.tif',
                        type=str, help='Path to the input image file')
    parser.add_argument('--output_dir', default='plots/swin_mae_visualization',
                        type=str, help='Directory to save visualization plots')
    parser.add_argument('--hu_min', type=float, default=-1000.0,
                        help='Minimum GLOBAL HU value used ONLY for global visualization range (if --vis_hu_mode global)')
    parser.add_argument('--hu_max', type=float, default=1000.0,
                        help='Maximum GLOBAL HU value used ONLY for global visualization range (if --vis_hu_mode global)')
    parser.add_argument('--vis_hu_mode', type=str, default='patch', choices=['global', 'patch'],
                        help="Which HU range to use for visualization: 'global' (uses --hu_min/--hu_max) or 'patch' (uses the actual range of the extracted patch)")
    parser.add_argument('--mask_ratios', default=[0.25, 0.5, 0.75, 0.9], type=float, nargs='+',
                        help='List of masking ratios for visualization (e.g., 0.5 0.75 0.9)')

    args = parser.parse_args()

    # Determine output directory based on visualization range choice
    vis_type_str = args.vis_hu_mode + "vis"
    output_dir_final = os.path.join(args.output_dir, f"swin_mae_visualization_{vis_type_str}")

    # Run visualization
    visualize_swin_mae_reconstruction(
        model_path=args.model_path,
        image_path=args.image_path,
        output_dir=output_dir_final,
        hu_min=args.hu_min,
        hu_max=args.hu_max,
        mask_ratios=args.mask_ratios,
        args=args
    )

    print("Visualization script finished.")
