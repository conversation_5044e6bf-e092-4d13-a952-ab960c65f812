#!/bin/bash
# 主实验启动脚本 - 从项目根目录执行
# 提供统一的入口点来运行各种实验

echo "🚀 CT图像超分辨率项目 - 实验启动器"
echo "=================================="
echo "当前最佳: PSNR=21.51, SSIM=0.626"
echo "=================================="

# 显示菜单
show_menu() {
    echo ""
    echo "请选择要执行的实验:"
    echo "1) 自监督预训练最终优化 (推荐)"
    echo "2) 自监督预训练微调实验 (5个实验)"
    echo "3) 监督超分辨率训练启动"
    echo "4) 查看项目状态和文档"
    echo "5) 退出"
    echo ""
}

# 检查环境
check_environment() {
    echo "🔍 检查环境..."
    
    # 检查Python环境
    if ! command -v python &> /dev/null; then
        echo "❌ Python未找到，请确保Python环境已激活"
        exit 1
    fi
    
    # 检查GPU
    python -c "import torch; print(f'✅ CUDA可用: {torch.cuda.is_available()}'); print(f'GPU数量: {torch.cuda.device_count()}')" 2>/dev/null || {
        echo "⚠️  无法检查GPU状态，请确保PyTorch已安装"
    }
    
    # 检查关键目录
    if [ ! -d "data/2号CT数据" ]; then
        echo "❌ 数据目录 data/2号CT数据 不存在"
        exit 1
    fi
    
    if [ ! -f "checkpoints/swin_mae_hierarchical_random_ssim_nce_w002/best_model.pth" ]; then
        echo "❌ 最佳预训练模型不存在"
        exit 1
    fi
    
    echo "✅ 环境检查通过"
}

# 执行预训练优化
run_pretraining_optimization() {
    echo "🎯 启动自监督预训练最终优化..."
    echo "目标: PSNR从21.51提升到22.0+"
    
    # 检查脚本存在
    if [ ! -f "scripts/pretraining/start_final_optimization.sh" ]; then
        echo "❌ 预训练优化脚本不存在"
        exit 1
    fi
    
    # 给脚本执行权限
    chmod +x scripts/pretraining/start_final_optimization.sh
    
    # 执行脚本
    bash scripts/pretraining/start_final_optimization.sh
}

# 执行微调实验
run_fine_tuning_experiments() {
    echo "🔬 启动自监督预训练微调实验..."
    echo "将运行5个不同配置的实验"
    
    # 检查脚本存在
    if [ ! -f "scripts/pretraining/optimize_from_best_config.sh" ]; then
        echo "❌ 微调实验脚本不存在"
        exit 1
    fi
    
    # 给脚本执行权限
    chmod +x scripts/pretraining/optimize_from_best_config.sh
    
    # 执行脚本
    bash scripts/pretraining/optimize_from_best_config.sh
}

# 执行监督训练
run_supervised_training() {
    echo "🎯 启动监督超分辨率训练..."
    echo "使用最佳预训练模型作为初始化"
    
    # 检查脚本存在
    if [ ! -f "scripts/supervised/start_supervised_training.sh" ]; then
        echo "❌ 监督训练脚本不存在"
        exit 1
    fi
    
    # 给脚本执行权限
    chmod +x scripts/supervised/start_supervised_training.sh
    
    # 执行脚本
    bash scripts/supervised/start_supervised_training.sh
}

# 显示文档
show_documentation() {
    echo "📚 项目文档和状态"
    echo "=================="
    
    if [ -f "scripts/documentation/PROJECT_STATUS_SUMMARY.md" ]; then
        echo "📄 项目状态总结:"
        head -20 "scripts/documentation/PROJECT_STATUS_SUMMARY.md"
        echo ""
        echo "完整文档位置:"
        echo "- 项目状态: scripts/documentation/PROJECT_STATUS_SUMMARY.md"
        echo "- 项目说明: scripts/documentation/README_UPDATED.md"
        echo "- 脚本说明: scripts/README.md"
    else
        echo "❌ 文档文件不存在"
    fi
}

# 主程序
main() {
    # 检查是否在项目根目录
    if [ ! -d "scripts" ] || [ ! -d "memory-bank" ]; then
        echo "❌ 请从项目根目录执行此脚本"
        echo "当前目录: $(pwd)"
        exit 1
    fi
    
    # 检查环境
    check_environment
    
    # 主循环
    while true; do
        show_menu
        read -p "请输入选择 (1-5): " choice
        
        case $choice in
            1)
                run_pretraining_optimization
                break
                ;;
            2)
                run_fine_tuning_experiments
                break
                ;;
            3)
                run_supervised_training
                break
                ;;
            4)
                show_documentation
                ;;
            5)
                echo "👋 退出程序"
                exit 0
                ;;
            *)
                echo "❌ 无效选择，请输入1-5"
                ;;
        esac
    done
}

# 执行主程序
main "$@"
