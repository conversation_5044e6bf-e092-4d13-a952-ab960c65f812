@echo off
REM 主实验启动脚本 - Windows版本
REM 从项目根目录执行

echo 🚀 CT图像超分辨率项目 - 实验启动器
echo ==================================
echo 当前最佳: PSNR=21.51, SSIM=0.626
echo ==================================

:menu
echo.
echo 请选择要执行的实验:
echo 1) 自监督预训练最终优化 (推荐)
echo 2) 自监督预训练微调实验 (5个实验)
echo 3) 监督超分辨率训练启动
echo 4) 查看项目状态和文档
echo 5) 退出
echo.

set /p choice="请输入选择 (1-5): "

if "%choice%"=="1" goto pretraining_optimization
if "%choice%"=="2" goto fine_tuning_experiments
if "%choice%"=="3" goto supervised_training
if "%choice%"=="4" goto show_documentation
if "%choice%"=="5" goto exit_program
echo ❌ 无效选择，请输入1-5
goto menu

:check_environment
echo 🔍 检查环境...

REM 检查Python环境
python --version >nul 2>&1
if errorlevel 1 (
    echo ❌ Python未找到，请确保Python环境已激活
    pause
    exit /b 1
)

REM 检查GPU
python -c "import torch; print(f'✅ CUDA可用: {torch.cuda.is_available()}'); print(f'GPU数量: {torch.cuda.device_count()}')" 2>nul
if errorlevel 1 (
    echo ⚠️  无法检查GPU状态，请确保PyTorch已安装
)

REM 检查关键目录
if not exist "data\2号CT数据" (
    echo ❌ 数据目录 data\2号CT数据 不存在
    pause
    exit /b 1
)

if not exist "checkpoints\swin_mae_hierarchical_random_ssim_nce_w002\best_model.pth" (
    echo ❌ 最佳预训练模型不存在
    pause
    exit /b 1
)

echo ✅ 环境检查通过
goto :eof

:pretraining_optimization
call :check_environment
echo 🎯 启动自监督预训练最终优化...
echo 目标: PSNR从21.51提升到22.0+

if not exist "scripts\pretraining\start_final_optimization.sh" (
    echo ❌ 预训练优化脚本不存在
    pause
    exit /b 1
)

REM 使用Git Bash执行shell脚本（如果可用）
where bash >nul 2>&1
if not errorlevel 1 (
    bash scripts/pretraining/start_final_optimization.sh
) else (
    echo ⚠️  未找到bash，尝试直接执行Python脚本...
    python scripts/pretraining/train_swin_mae_final_optimization.py ^
        --seed 42 ^
        --epochs 200 ^
        --batch_size 8 ^
        --gradient_accumulation_steps 16 ^
        --patch_size 4 ^
        --swin_embed_dim 96 ^
        --decoder_embed_dim 128 ^
        --decoder_depths 1 1 1 1 ^
        --decoder_num_heads 8 8 4 2 ^
        --perceptual_loss_weight 0.005 ^
        --ssim_loss_weight 0.05 ^
        --patchnce_loss_weight 0.005 ^
        --nce_proj_dim 256 ^
        --nce_T 0.07 ^
        --lr 1.5e-4 ^
        --warmup_epochs 40 ^
        --weight_decay 0.05 ^
        --grad_clip_norm 1.0 ^
        --use_amp ^
        --resume_from checkpoints/swin_mae_hierarchical_random_ssim_nce_w002/best_model.pth ^
        --save_dir checkpoints/swin_mae_final_optimization ^
        --log_dir logs/swin_mae_final_optimization ^
        --data_dir data/2号CT数据
)
goto end

:fine_tuning_experiments
call :check_environment
echo 🔬 启动自监督预训练微调实验...
echo 将运行5个不同配置的实验

if not exist "scripts\pretraining\optimize_from_best_config.sh" (
    echo ❌ 微调实验脚本不存在
    pause
    exit /b 1
)

where bash >nul 2>&1
if not errorlevel 1 (
    bash scripts/pretraining/optimize_from_best_config.sh
) else (
    echo ❌ 需要bash环境来运行微调实验脚本
    echo 请安装Git Bash或WSL
    pause
    exit /b 1
)
goto end

:supervised_training
call :check_environment
echo 🎯 启动监督超分辨率训练...
echo 使用最佳预训练模型作为初始化

if not exist "scripts\supervised\start_supervised_training.sh" (
    echo ❌ 监督训练脚本不存在
    pause
    exit /b 1
)

where bash >nul 2>&1
if not errorlevel 1 (
    bash scripts/supervised/start_supervised_training.sh
) else (
    echo ⚠️  未找到bash，尝试直接执行Python脚本...
    cd "Self-supervised training network ultra"
    python train_sr_ultra.py --config configs/config_ultra.yaml --tag SwinMAE_Hierarchical_DiffusionSR_v1
    cd ..
)
goto end

:show_documentation
echo 📚 项目文档和状态
echo ==================

if exist "scripts\documentation\PROJECT_STATUS_SUMMARY.md" (
    echo 📄 项目状态总结:
    type "scripts\documentation\PROJECT_STATUS_SUMMARY.md" | more
    echo.
    echo 完整文档位置:
    echo - 项目状态: scripts\documentation\PROJECT_STATUS_SUMMARY.md
    echo - 项目说明: scripts\documentation\README_UPDATED.md
    echo - 脚本说明: scripts\README.md
) else (
    echo ❌ 文档文件不存在
)
pause
goto menu

:exit_program
echo 👋 退出程序
exit /b 0

:end
echo.
echo 🎉 实验完成！
pause
