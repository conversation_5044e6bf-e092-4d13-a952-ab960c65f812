#!/usr/bin/env python3
"""
安静模式训练启动脚本
减少日志输出，提供清洁的训练体验
"""

import os
import sys
import argparse
import subprocess
import time
from datetime import datetime

def setup_quiet_environment():
    """设置安静的训练环境"""
    # 设置环境变量减少输出
    os.environ['PYTHONUNBUFFERED'] = '1'  # 确保输出不被缓冲
    os.environ['CUDA_LAUNCH_BLOCKING'] = '0'  # 异步CUDA操作

    # 减少PyTorch警告
    import warnings
    warnings.filterwarnings("ignore", category=UserWarning)
    warnings.filterwarnings("ignore", category=FutureWarning)

    # 设置日志级别
    import logging
    logging.getLogger('PIL').setLevel(logging.WARNING)
    logging.getLogger('matplotlib').setLevel(logging.WARNING)

def print_training_header(config_file, experiment_name):
    """打印训练开始信息"""
    print("="*60)
    print("🚀 CT图像超分辨率训练 - 安静模式")
    print("="*60)
    print(f"配置文件: {config_file}")
    print(f"实验名称: {experiment_name}")
    print(f"开始时间: {datetime.now().strftime('%Y-%m-%d %H:%M:%S')}")
    print(f"日志模式: 安静模式 (减少输出频率)")
    print("-"*60)

def monitor_training_progress(process):
    """监控训练进程并提供实时进度显示"""
    import threading
    import queue

    # 创建队列来收集输出
    output_queue = queue.Queue()

    def read_output():
        """读取进程输出的线程函数"""
        for line in iter(process.stdout.readline, ''):
            if line:
                output_queue.put(line.rstrip())
        process.stdout.close()

    # 启动读取线程
    reader_thread = threading.Thread(target=read_output)
    reader_thread.daemon = True
    reader_thread.start()

    last_update_time = time.time()
    current_epoch = 0
    total_epochs = 200  # 默认值，会从输出中更新

    print("🚀 开始训练监控...")
    print("-" * 60)

    while process.poll() is None:
        try:
            # 非阻塞地获取输出
            line = output_queue.get(timeout=1)
            current_time = time.time()

            # 解析关键信息
            if "Epoch" in line and "Avg Losses" in line:
                # 解析轮次信息
                if "Epoch" in line and "/" in line:
                    try:
                        epoch_part = line.split("Epoch")[1].split("Avg Losses")[0].strip()
                        if "/" in epoch_part:
                            current_epoch = int(epoch_part.split("/")[0])
                            total_epochs = int(epoch_part.split("/")[1].split(":")[0])
                    except:
                        pass

                # 显示损失信息
                print(f"📊 {line}")

                # 显示进度条
                if total_epochs > 0:
                    progress = (current_epoch / total_epochs) * 100
                    bar_length = 30
                    filled_length = int(bar_length * current_epoch // total_epochs)
                    bar = '█' * filled_length + '░' * (bar_length - filled_length)
                    print(f"📈 进度: [{bar}] {progress:.1f}% ({current_epoch}/{total_epochs})")

                last_update_time = current_time
                print("-" * 60)

            elif "best model" in line.lower():
                print(f"🎉 {line}")

            elif "Training completed" in line:
                print(f"✅ {line}")

            elif "Error" in line or "WARNING" in line:
                print(f"⚠️ {line}")

            elif "Saved" in line and ("checkpoint" in line or "model" in line):
                print(f"💾 {line}")

            elif "Using device" in line:
                print(f"🖥️ {line}")

            elif "Loading data" in line or "Initializing" in line:
                print(f"🔧 {line}")

        except queue.Empty:
            # 如果长时间没有输出，显示心跳
            current_time = time.time()
            if current_time - last_update_time > 120:  # 2分钟没有更新
                if current_epoch > 0:
                    print(f"⏳ 训练进行中... 当前轮次: {current_epoch}/{total_epochs}")
                else:
                    print(f"⏳ 训练进行中... (运行时间: {int((current_time - last_update_time)/60)} 分钟)")
                last_update_time = current_time
            continue

    # 等待读取线程结束
    reader_thread.join(timeout=5)

    return process.returncode

def main():
    parser = argparse.ArgumentParser(description='安静模式训练启动器')
    parser.add_argument('--config', type=str,
                       default='configs/config_ultra_no_gan.yaml',
                       help='配置文件路径')
    parser.add_argument('--resume', type=str, default=None,
                       help='恢复训练的检查点路径')
    parser.add_argument('--tag', type=str, default=None,
                       help='实验标签')
    parser.add_argument('--verbose', action='store_true',
                       help='启用详细输出模式')

    args = parser.parse_args()

    # 设置安静环境
    if not args.verbose:
        setup_quiet_environment()

    # 确定实验名称
    if args.tag:
        experiment_name = args.tag
    else:
        # 从配置文件自动生成
        config_name = os.path.splitext(os.path.basename(args.config))[0]
        timestamp = datetime.now().strftime("%m%d_%H%M")
        experiment_name = f"{config_name}_{timestamp}"

    # 打印训练头部信息
    print_training_header(args.config, experiment_name)

    # 构建训练命令
    train_cmd = [
        sys.executable, 'train_sr_ultra.py',
        '--config', args.config,
        '--tag', experiment_name
    ]

    if args.resume:
        train_cmd.extend(['--resume', args.resume])

    print(f"执行命令: {' '.join(train_cmd)}")
    print("-"*60)

    # 启动训练进程
    try:
        if args.verbose:
            # 详细模式：显示所有输出
            process = subprocess.Popen(train_cmd,
                                     stdout=subprocess.PIPE,
                                     stderr=subprocess.STDOUT,
                                     universal_newlines=True,
                                     bufsize=1)

            # 实时显示输出
            for line in process.stdout:
                print(line.rstrip())

            process.wait()
            return_code = process.returncode
        else:
            # 安静模式：只显示关键信息
            process = subprocess.Popen(train_cmd,
                                     stdout=subprocess.PIPE,
                                     stderr=subprocess.PIPE,
                                     universal_newlines=True)

            # 监控进程
            return_code = monitor_training_progress(process)

            # 获取最终输出
            stdout, stderr = process.communicate()

            # 只显示重要的输出行
            important_lines = []
            for line in stdout.split('\n'):
                if any(keyword in line for keyword in [
                    'Epoch', 'Avg Losses', 'best model', 'Training completed',
                    'Error', 'WARNING', 'Saved', 'Models saved'
                ]):
                    important_lines.append(line)

            if important_lines:
                print("\n📊 训练关键信息:")
                print("-"*40)
                for line in important_lines[-20:]:  # 只显示最后20行重要信息
                    if line.strip():
                        print(line)

            if stderr and stderr.strip():
                print("\n⚠️ 错误信息:")
                print("-"*40)
                print(stderr)

        # 训练完成总结
        print("\n" + "="*60)
        if return_code == 0:
            print("✅ 训练成功完成！")
            print(f"📁 检查点目录: ./checkpoints/sr_diffusion/{experiment_name}")
            print(f"📊 日志目录: ./logs/sr_diffusion/{experiment_name}")
            print("🎯 推荐使用 best_model_generator.pth 进行推理")
        else:
            print("❌ 训练过程中出现错误")
            print(f"返回码: {return_code}")

        print(f"结束时间: {datetime.now().strftime('%Y-%m-%d %H:%M:%S')}")
        print("="*60)

        return return_code

    except KeyboardInterrupt:
        print("\n⏹️ 训练被用户中断")
        return 1
    except Exception as e:
        print(f"\n❌ 启动训练时出错: {e}")
        return 1

if __name__ == '__main__':
    exit_code = main()
    sys.exit(exit_code)
