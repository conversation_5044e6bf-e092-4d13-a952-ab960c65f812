import torch
import torch.nn as nn
import torch.nn.functional as F
import torch.optim as optim
from torch.optim.lr_scheduler import CosineAnnealingLR
from data.mae_loader import get_mae_loader # Import loader
import timm # Import timm
assert timm.__version__ >= "0.3.2" # MAE requires timm support, assert version
from timm.models.vision_transformer import Block # For ViT-style decoder
from tqdm import tqdm
import os
import numpy as np
from torch.utils.tensorboard import SummaryWriter
import math
import random
# from util.pos_embed import get_2d_sincos_pos_embed # Assuming pos embed helper is in util
import torchvision.models as models
import argparse
# from pytorch_msssim import ssim # Removed for this version, can be added back

# --- Random seed control function ---
def set_seed(seed=42):
    """设置所有随机种子以确保可重复性"""
    random.seed(seed)
    np.random.seed(seed)
    torch.manual_seed(seed)
    if torch.cuda.is_available():
        torch.cuda.manual_seed(seed)
        torch.cuda.manual_seed_all(seed)
    torch.backends.cudnn.deterministic = True
    torch.backends.cudnn.benchmark = False
    print(f"随机种子已设置为: {seed}")

# --- Helper functions from util (mocked for completeness, same as in previous response) ---
def get_2d_sincos_pos_embed(embed_dim, grid_size, cls_token=False):
    grid_h = np.arange(grid_size, dtype=np.float32)
    grid_w = np.arange(grid_size, dtype=np.float32)
    grid = np.meshgrid(grid_w, grid_h)
    grid = np.stack(grid, axis=0)
    grid = grid.reshape([2, 1, grid_size, grid_size])
    pos_embed = get_2d_sincos_pos_embed_from_grid(embed_dim, grid)
    if cls_token:
        pos_embed = np.concatenate([np.zeros([1, embed_dim]), pos_embed], axis=0)
    return pos_embed

def get_2d_sincos_pos_embed_from_grid(embed_dim, grid):
    assert embed_dim % 2 == 0
    emb_h = get_1d_sincos_pos_embed_from_grid(embed_dim // 2, grid[0])
    emb_w = get_1d_sincos_pos_embed_from_grid(embed_dim // 2, grid[1])
    emb = np.concatenate([emb_h, emb_w], axis=1)
    return emb

def get_1d_sincos_pos_embed_from_grid(embed_dim, pos):
    assert embed_dim % 2 == 0
    omega = np.arange(embed_dim // 2, dtype=np.float64)
    omega /= embed_dim / 2.
    omega = 1. / 10000**omega
    pos = pos.reshape(-1)
    out = np.einsum('m,d->md', pos, omega)
    emb_sin = np.sin(out)
    emb_cos = np.cos(out)
    emb = np.concatenate([emb_sin, emb_cos], axis=1)
    return emb
# --- End of mocked util functions ---


# ResNetPerceptualLoss (Copied from Program 1, as it's a good component)
class ResNetPerceptualLoss(nn.Module):
    def __init__(self, feature_layer_names=['layer2', 'layer3'], use_ct_norm=True, requires_grad=False):
        super().__init__()
        resnet = models.resnet18(weights=models.ResNet18_Weights.IMAGENET1K_V1)
        self.use_ct_norm = use_ct_norm
        original_conv = resnet.conv1
        resnet.conv1 = nn.Conv2d(1, 64, kernel_size=7, stride=2, padding=3, bias=False)
        with torch.no_grad():
            resnet.conv1.weight.data = original_conv.weight.data.mean(dim=1, keepdim=True)

        self.feature_layer_names = feature_layer_names
        default_weights = {'layer1': 1.0, 'layer2': 0.8, 'layer3': 0.6, 'layer4': 0.4}
        self.layer_weights = [default_weights.get(name, 0.5) for name in self.feature_layer_names]

        self.features = nn.ModuleDict()
        current_model_layers = [resnet.conv1, resnet.bn1, resnet.relu, resnet.maxpool]

        if 'layer1' in self.feature_layer_names:
            self.features['layer1'] = nn.Sequential(*current_model_layers, resnet.layer1)
        current_model_layers.append(resnet.layer1)

        if 'layer2' in self.feature_layer_names:
            self.features['layer2'] = nn.Sequential(*current_model_layers, resnet.layer2)
        current_model_layers.append(resnet.layer2)

        if 'layer3' in self.feature_layer_names:
            self.features['layer3'] = nn.Sequential(*current_model_layers, resnet.layer3)
        current_model_layers.append(resnet.layer3)

        if 'layer4' in self.feature_layer_names:
            self.features['layer4'] = nn.Sequential(*current_model_layers, resnet.layer4)

        if not self.features:
             raise ValueError(f"No valid ResNet feature layers specified in {self.feature_layer_names}")

        if not requires_grad:
            for param in self.parameters():
                param.requires_grad = False
        self.eval()
        self.criterion = nn.L1Loss(reduction='mean')
        print(f"Initialized ResNetPerceptualLoss using layers: {self.feature_layer_names} with weights {self.layer_weights}. CT norm: {self.use_ct_norm}")

    def _normalize_ct(self, x):
        x = (x + 1.0) / 2.0
        x_min = x.view(x.shape[0], x.shape[1], -1).min(dim=2, keepdim=True)[0].unsqueeze(-1)
        x_max = x.view(x.shape[0], x.shape[1], -1).max(dim=2, keepdim=True)[0].unsqueeze(-1)
        denominator = x_max - x_min
        x_norm = torch.where(denominator > 1e-8, (x - x_min) / denominator, torch.zeros_like(x))
        return x_norm

    def forward(self, x_pred_img, y_true_img, mask_img_focus=None): # mask_img_focus means 1 for masked region
        if self.use_ct_norm:
            x_pred_img = self._normalize_ct(x_pred_img)
            y_true_img = self._normalize_ct(y_true_img)
        else:
            x_pred_img = (x_pred_img + 1.0) / 2.0
            y_true_img = (y_true_img + 1.0) / 2.0

        total_loss = 0.0
        for i, layer_name in enumerate(self.feature_layer_names):
            layer = self.features[layer_name]
            x_feat = layer(x_pred_img)
            y_feat = layer(y_true_img)

            if mask_img_focus is not None:
                mask_resized = F.interpolate(mask_img_focus, size=x_feat.shape[2:], mode='nearest')
                if mask_resized.shape[1] == 1 and x_feat.shape[1] > 1:
                    mask_resized = mask_resized.expand_as(x_feat)
                diff = torch.abs(x_feat - y_feat) * mask_resized
                layer_loss = diff.sum() / (mask_resized.sum() + 1e-8)
            else:
                layer_loss = self.criterion(x_feat, y_feat)
            total_loss += self.layer_weights[i] * layer_loss
        return total_loss


class MaskedAutoencoderSwinSimpleDecoder(nn.Module):
    def __init__(self, img_size=256, patch_size=4, in_chans=1,
                 swin_embed_dim=96, swin_depths=[2, 2, 6, 2], swin_num_heads=[3, 6, 12, 24],
                 swin_window_size=7, swin_ape=False, swin_patch_norm=True,
                 mlp_ratio=4., qkv_bias=True, drop_rate=0., attn_drop_rate=0., drop_path_rate=0.1,
                 norm_layer=nn.LayerNorm,
                 # Decoder specific (simple ViT-style)
                 decoder_embed_dim=512, decoder_depth=8, decoder_num_heads=16,
                 decoder_mlp_ratio=4., # Can be different from encoder's mlp_ratio
                 # Masking (now only random_patch, but it's applied *before* encoder)
                 # Loss
                 norm_pix_loss=False,
                 perceptual_loss_weight=0.01,
                 perc_layers_resnet=['layer2', 'layer3'],
                 perc_norm_ct_resnet=True):
        super().__init__()

        self.patch_size = patch_size
        self.in_chans = in_chans
        self.perceptual_loss_weight = perceptual_loss_weight
        self.norm_pix_loss = norm_pix_loss

        # Encoder (Swin Transformer)
        self.encoder = timm.create_model(
            'swin_tiny_patch4_window7_224',
            pretrained=False, in_chans=in_chans, img_size=img_size,
            patch_size=patch_size, embed_dim=swin_embed_dim, depths=swin_depths,
            num_heads=swin_num_heads, window_size=swin_window_size, mlp_ratio=mlp_ratio,
            qkv_bias=qkv_bias, drop_rate=drop_rate, attn_drop_rate=attn_drop_rate,
            drop_path_rate=drop_path_rate, norm_layer=norm_layer, ape=swin_ape,
            patch_norm=swin_patch_norm, num_classes=0, global_pool=''
        )
        self.patch_embed = self.encoder.patch_embed
        self.encoder_embed_dim = swin_embed_dim # For mask token & pos_embed
        self.actual_grid_size = self.patch_embed.grid_size # (H_initial/p, W_initial/p)
        self.num_patches_initial = self.actual_grid_size[0] * self.actual_grid_size[1]

        # Absolute Position Embedding for Encoder
        if not swin_ape or not hasattr(self.encoder, 'absolute_pos_embed'):
            self.encoder_pos_embed = nn.Parameter(torch.zeros(1, self.num_patches_initial, self.encoder_embed_dim))
        else:
            self.encoder_pos_embed = None
            print("Using APE from Swin config or it's built into timm model.")

        # Encoder Mask Token
        self.encoder_mask_token = nn.Parameter(torch.zeros(1, 1, self.encoder_embed_dim))

        # Determine final encoder output properties
        # Number of patch merging stages = len(swin_depths) - 1
        downsample_stages = len(swin_depths) -1
        self.grid_final_encoder = (self.actual_grid_size[0] // (2**downsample_stages),
                                   self.actual_grid_size[1] // (2**downsample_stages))
        self.num_patches_final_encoder = self.grid_final_encoder[0] * self.grid_final_encoder[1]
        self.actual_encoder_output_dim = int(swin_embed_dim * (2**downsample_stages))
        print(f"Encoder initial grid: {self.actual_grid_size}, L_initial: {self.num_patches_initial}")
        print(f"Encoder final grid: {self.grid_final_encoder}, L_final_enc: {self.num_patches_final_encoder}, D_final_enc: {self.actual_encoder_output_dim}")


        # MAE Decoder (Simple ViT-style)
        self.decoder_embed_proj = nn.Linear(self.actual_encoder_output_dim, decoder_embed_dim, bias=True)

        # Decoder pos_embed is for the *final encoder output sequence length*
        self.decoder_pos_embed = nn.Parameter(torch.zeros(1, self.num_patches_final_encoder, decoder_embed_dim))

        self.decoder_blocks = nn.ModuleList([
            Block(decoder_embed_dim, decoder_num_heads, decoder_mlp_ratio, qkv_bias=qkv_bias,
                  norm_layer=norm_layer, act_layer=nn.GELU) # Use consistent norm_layer
            for _ in range(decoder_depth)])
        self.decoder_norm = norm_layer(decoder_embed_dim)
        # Decoder predicts patches at the *final encoder resolution*
        self.decoder_pred = nn.Linear(decoder_embed_dim, patch_size**2 * in_chans, bias=True)

        if self.perceptual_loss_weight > 0:
            self.perceptual_loss_fn = ResNetPerceptualLoss(
                 feature_layer_names=perc_layers_resnet, use_ct_norm=perc_norm_ct_resnet, requires_grad=False)
        else:
            self.perceptual_loss_fn = None

        self.initialize_weights()

    def initialize_weights(self):
        if self.encoder_pos_embed is not None:
            pos_embed_data = get_2d_sincos_pos_embed(self.encoder_pos_embed.shape[-1], self.actual_grid_size[0], cls_token=False)
            self.encoder_pos_embed.data.copy_(torch.from_numpy(pos_embed_data).float().unsqueeze(0))
            print("Initialized custom encoder absolute positional embedding.")

        torch.nn.init.normal_(self.encoder_mask_token, std=.02)
        print("Initialized encoder mask token.")

        decoder_pos_embed_data = get_2d_sincos_pos_embed(self.decoder_pos_embed.shape[-1], self.grid_final_encoder[0], cls_token=False)
        self.decoder_pos_embed.data.copy_(torch.from_numpy(decoder_pos_embed_data).float().unsqueeze(0))
        print(f"Initialized decoder positional embedding for grid {self.grid_final_encoder}.")

        self.apply(self._init_weights)

    def _init_weights(self, m):
        if isinstance(m, nn.Linear):
            torch.nn.init.xavier_uniform_(m.weight)
            if m.bias is not None:
                nn.init.constant_(m.bias, 0)
        elif isinstance(m, nn.LayerNorm):
            nn.init.constant_(m.bias, 0)
            nn.init.constant_(m.weight, 1.0)
        elif isinstance(m, nn.Conv2d):
            torch.nn.init.kaiming_normal_(m.weight, mode='fan_out', nonlinearity='relu')
            if m.bias is not None:
                nn.init.constant_(m.bias, 0)

    def _random_patch_masking_with_token(self, x_patchembed, mask_ratio):
        N, L, D = x_patchembed.shape
        len_keep = int(L * (1 - mask_ratio))

        noise = torch.rand(N, L, device=x_patchembed.device)
        ids_shuffle = torch.argsort(noise, dim=1)
        ids_restore = torch.argsort(ids_shuffle, dim=1)

        # 0 is keep, 1 is remove/mask
        patch_mask_map = torch.ones(N, L, device=x_patchembed.device, dtype=torch.bool) # Use bool for efficiency
        patch_mask_map[:, :len_keep] = False # False for keep
        patch_mask_map = torch.gather(patch_mask_map, dim=1, index=ids_restore) # Unshuffle

        x_masked_seq = x_patchembed.clone()
        # Expand mask_token correctly. self.encoder_mask_token is [1, 1, D]
        mask_tokens_expanded = self.encoder_mask_token.expand(N, L, D)
        # Use patch_mask_map.unsqueeze(-1) to select where to put mask_tokens
        x_masked_seq = torch.where(patch_mask_map.unsqueeze(-1), mask_tokens_expanded, x_masked_seq)

        return x_masked_seq, patch_mask_map.float() # Return float mask_map (1 for masked) for loss

    def forward_encoder(self, x_img, mask_ratio):
        x_patchembed = self.patch_embed(x_img)
        if x_patchembed.dim() == 4:
            x_patchembed = x_patchembed.flatten(1, 2)
        N, L_init, D_enc_emb = x_patchembed.shape

        if self.encoder_pos_embed is not None:
            pos_embed_to_add = self.encoder_pos_embed.to(x_patchembed.device, dtype=x_patchembed.dtype)
            # Interpolation logic for pos_embed if L_init changes (e.g. dynamic img_size)
            if pos_embed_to_add.shape[1] != L_init:
                num_patches_sqrt = int(pos_embed_to_add.shape[1]**0.5)
                pos_embed_to_add = F.interpolate(
                    pos_embed_to_add.reshape(1, num_patches_sqrt, num_patches_sqrt, D_enc_emb).permute(0,3,1,2),
                    size=self.actual_grid_size, mode='bicubic', align_corners=False
                ).permute(0,2,3,1).reshape(1, L_init, D_enc_emb)
            x_patchembed = x_patchembed + pos_embed_to_add

        x_masked_seq, patch_mask_map_initial = self._random_patch_masking_with_token(x_patchembed, mask_ratio)

        H_grid_init, W_grid_init = self.actual_grid_size
        x_spatial_masked = x_masked_seq.reshape(N, H_grid_init, W_grid_init, D_enc_emb)

        current_feature_map = x_spatial_masked
        for swin_layer in self.encoder.layers:
            current_feature_map = swin_layer(current_feature_map)

        N_out, H_out, W_out, C_out = current_feature_map.shape
        latent_seq_full = current_feature_map.view(N_out, H_out * W_out, C_out)
        latent_final_normed = self.encoder.norm(latent_seq_full)

        return latent_final_normed, patch_mask_map_initial # Return mask for initial patches

    def forward_decoder(self, latent_final_encoder):
        # latent_final_encoder: [N, L_final_enc, D_final_enc]
        x = self.decoder_embed_proj(latent_final_encoder) # [N, L_final_enc, D_decoder_emb]

        # Add decoder positional embedding
        x = x + self.decoder_pos_embed.to(x.device, dtype=x.dtype)

        for blk in self.decoder_blocks:
            x = blk(x)
        x = self.decoder_norm(x)

        # pred_low_res_patches: [N, L_final_enc, patch_size^2 * C]
        pred_low_res_patches = self.decoder_pred(x)
        return pred_low_res_patches

    def patchify_at_initial_resolution(self, imgs_nchw):
        p = self.patch_size
        N, C, H, W = imgs_nchw.shape
        assert H % p == 0 and W % p == 0
        h_patch, w_patch = H // p, W // p # These are self.actual_grid_size
        x = imgs_nchw.reshape(N, C, h_patch, p, w_patch, p)
        x = torch.einsum('nchpwq->nhwpqc', x)
        target_initial_seq = x.reshape(N, h_patch * w_patch, p**2 * C)
        return target_initial_seq

    def unpatchify_from_initial_resolution(self, pred_initial_seq):
        p = self.patch_size
        h_patch, w_patch = self.actual_grid_size
        N, L_initial, _ = pred_initial_seq.shape
        assert L_initial == h_patch * w_patch
        C_img = self.in_chans
        assert pred_initial_seq.shape[2] == p**2 * C_img

        x = pred_initial_seq.reshape(N, h_patch, w_patch, p, p, C_img)
        x = torch.einsum('nhwpqc->nchpwq', x)
        imgs_nchw = x.reshape(N, C_img, h_patch * p, w_patch * p)
        return imgs_nchw

    def forward_loss(self, imgs_orig_nchw, pred_initial_seq, patch_mask_map_initial):
        # imgs_orig_nchw: [N, C, H_orig, W_orig]
        # pred_initial_seq: [N, L_initial, p*p*C] (predictions already upsampled to initial patch resolution)
        # patch_mask_map_initial: [N, L_initial], 1 is masked

        target_initial_seq = self.patchify_at_initial_resolution(imgs_orig_nchw)

        if self.norm_pix_loss:
            mean = target_initial_seq.mean(dim=-1, keepdim=True)
            var = target_initial_seq.var(dim=-1, keepdim=True)
            target_initial_seq = (target_initial_seq - mean) / (var.add(1.e-6).sqrt())

        loss_mse_per_patch = (pred_initial_seq - target_initial_seq) ** 2
        loss_mse_per_patch = loss_mse_per_patch.mean(dim=-1) # [N, L_initial]

        mse_loss = (loss_mse_per_patch * patch_mask_map_initial).sum() / (patch_mask_map_initial.sum() + 1e-8)
        total_loss = mse_loss
        perc_loss_val = torch.tensor(0.0, device=total_loss.device)

        if self.perceptual_loss_fn is not None and self.perceptual_loss_weight > 0:
            pred_img_reconst = self.unpatchify_from_initial_resolution(pred_initial_seq)

            mask_img_spatial = patch_mask_map_initial.reshape(-1, *self.actual_grid_size).unsqueeze(1).float()
            mask_img_focus = F.interpolate(mask_img_spatial,
                                           size=(imgs_orig_nchw.shape[2], imgs_orig_nchw.shape[3]),
                                           mode='nearest')
            self.perceptual_loss_fn = self.perceptual_loss_fn.to(pred_img_reconst.device)
            perc_loss_val = self.perceptual_loss_fn(pred_img_reconst, imgs_orig_nchw, mask_img_focus=mask_img_focus)
            total_loss = total_loss + self.perceptual_loss_weight * perc_loss_val

        return {
            "total_loss": total_loss,
            "mse_loss": mse_loss,
            "perceptual_loss": perc_loss_val
        } # Add ssim if re-enabled

    def forward(self, imgs_nchw, mask_ratio=0.75):
        N, _, H_orig, W_orig = imgs_nchw.shape

        # 1. Encoder processes masked input
        latent_final_encoder, patch_mask_map_initial = self.forward_encoder(imgs_nchw, mask_ratio)

        # 2. Decoder processes latent representation from encoder
        # pred_low_res_patches: [N, L_final_enc, p*p*C]
        pred_low_res_patches = self.forward_decoder(latent_final_encoder)

        # 3. Upsample pred_low_res_patches to initial patch resolution for loss calculation
        # This is the tricky part from original Program 2's logic
        N, L_final_enc, C_pred_patch = pred_low_res_patches.shape
        H_final_enc, W_final_enc = self.grid_final_encoder
        p = self.patch_size
        # C_img_pred = C_pred_patch // (p**2) # Should be self.in_chans

        assert L_final_enc == H_final_enc * W_final_enc
        assert C_pred_patch == p**2 * self.in_chans

        # Reshape to spatial: [N, H_final_enc, W_final_enc, p, p, C_img]
        pred_spatial_low_res = pred_low_res_patches.reshape(N, H_final_enc, W_final_enc, p, p, self.in_chans)
        # Permute to [N, C_img, H_final_enc, W_final_enc, p, p]
        pred_spatial_low_res = pred_spatial_low_res.permute(0, 5, 1, 2, 3, 4)
        # Combine patch dims with spatial dims: [N, C_img, H_final_enc*p, W_final_enc*p]
        pred_img_low_res = pred_spatial_low_res.reshape(N, self.in_chans, H_final_enc * p, W_final_enc * p)

        # Upsample low-resolution reconstructed image to original image resolution H_orig, W_orig
        pred_img_reconst_full_res = F.interpolate(
            pred_img_low_res,
            size=(H_orig, W_orig),
            mode='bilinear', # or 'nearest'
            align_corners=False
        ) # Shape: [N, C_img, H_orig, W_orig]

        # Patchify the upsampled prediction to get [N, L_initial, p*p*C] for loss calculation
        pred_initial_seq = self.patchify_at_initial_resolution(pred_img_reconst_full_res)

        # 4. Calculate loss
        losses = self.forward_loss(imgs_nchw, pred_initial_seq, patch_mask_map_initial)

        return losses, pred_initial_seq, patch_mask_map_initial

# --- Training Functions ---
def setup_mae_training(model_params, optimizer_params):
    """设置 MAE 训练的优化器和调度器"""
    lr = optimizer_params.get('lr', 1.5e-4) # Base LR
    weight_decay = optimizer_params.get('weight_decay', 0.05)
    optimizer = optim.AdamW(model_params, lr=lr, betas=(0.9, 0.95), weight_decay=weight_decay)
    # Cosine scheduler T_max depends on total epochs *after* warmup
    scheduler = CosineAnnealingLR(optimizer, T_max=optimizer_params['epochs_after_warmup'], eta_min=lr/100)
    scaler = torch.cuda.amp.GradScaler(enabled=optimizer_params.get('use_amp', True))
    return optimizer, scheduler, scaler

def train_epoch_mae(model, train_loader, optimizer, scaler, device, writer, epoch, _, base_lr, warmup_epochs, gradient_accumulation_steps=1, log_interval=50, use_amp=True, mask_ratio=0.75):
    """MAE 训练循环 with warmup and gradient accumulation"""
    model.train()
    total_losses = {'total_loss': 0, 'mse_loss': 0, 'perceptual_loss': 0}
    num_batches = len(train_loader)
    processed_batches = 0

    with tqdm(train_loader, unit="batch") as pbar:
        for batch_idx, imgs in enumerate(pbar):
            if imgs is None or imgs.nelement() == 0:
                print(f"Skipping empty batch at index {batch_idx}")
                continue

            global_step = epoch * num_batches + batch_idx
            # Adjust learning rate using linear warmup
            if epoch < warmup_epochs:
                num_warmup_steps = warmup_epochs * num_batches
                if num_warmup_steps > 0:
                    current_step = global_step + 1
                    lr_scale = min(1.0, float(current_step) / num_warmup_steps)
                    new_lr = base_lr * lr_scale
                    for param_group in optimizer.param_groups:
                        param_group['lr'] = new_lr
            # After warmup, the scheduler (stepped per epoch) handles decay.

            imgs = imgs.to(device, non_blocking=True)

            # Implement gradient accumulation
            if batch_idx % gradient_accumulation_steps == 0:
                optimizer.zero_grad()

            with torch.cuda.amp.autocast(enabled=use_amp):
                losses, _, _ = model(imgs, mask_ratio=mask_ratio)
                # Normalize loss for accumulation
                for k in losses:
                    losses[k] = losses[k] / gradient_accumulation_steps

            # Accumulate scaled loss
            scaler.scale(losses['total_loss']).backward()

            # Store loss values (un-normalized for reporting)
            current_losses = {k: v.item() * gradient_accumulation_steps for k, v in losses.items()}

            # Check for NaN
            if math.isnan(current_losses['total_loss']):
                print(f"Warning: NaN loss detected at epoch {epoch+1}, batch {batch_idx}. Skipping batch.")
                optimizer.zero_grad()
                continue

            # Update weights after accumulation steps
            if (batch_idx + 1) % gradient_accumulation_steps == 0 or (batch_idx + 1) == num_batches:
                scaler.step(optimizer)
                scaler.update()
                optimizer.zero_grad(set_to_none=True)

            # Update totals
            for k in total_losses:
                if k in current_losses:
                    total_losses[k] += current_losses[k]

            processed_batches += 1

            # Update progress bar
            pbar.set_postfix(
                loss=f"{current_losses['total_loss']:.4f}",
                mse=f"{current_losses.get('mse_loss', 0):.4f}",
                perc=f"{current_losses.get('perceptual_loss', 0):.4f}",
                lr=f"{optimizer.param_groups[0]['lr']:.1e}"
            )

            # Log to tensorboard
            if batch_idx % log_interval == 0:
                for k, v in current_losses.items():
                    writer.add_scalar(f'Loss/{k}', v, global_step)
                writer.add_scalar('LR', optimizer.param_groups[0]['lr'], global_step)

    # Calculate averages
    avg_losses = {k: v / processed_batches if processed_batches > 0 else 0 for k, v in total_losses.items()}
    return avg_losses

def pretrain_mae(args):
    """Swin MAE 自监督预训练函数"""
    # Set random seed for reproducibility
    set_seed(args.seed)

    device = torch.device('cuda' if torch.cuda.is_available() else 'cpu')
    print(f"Using device: {device}")
    print(f"Swin MAE Pretraining Parameters: {args}")

    os.makedirs(args.checkpoint_dir, exist_ok=True)
    os.makedirs(args.log_dir, exist_ok=True)
    writer = SummaryWriter(args.log_dir)

    # Instantiate Swin MAE model with improved masking strategy
    model = MaskedAutoencoderSwinSimpleDecoder(
        img_size=args.img_size, patch_size=args.patch_size, in_chans=args.in_chans,
        swin_embed_dim=args.swin_embed_dim, swin_depths=args.swin_depths, swin_num_heads=args.swin_num_heads,
        swin_window_size=args.swin_window_size, swin_ape=args.swin_ape,
        drop_path_rate=args.drop_path_rate,
        # Decoder params
        decoder_embed_dim=args.decoder_embed_dim,
        decoder_depth=args.decoder_depth,
        decoder_num_heads=args.decoder_num_heads,
        decoder_mlp_ratio=args.decoder_mlp_ratio,
        # Loss params
        norm_pix_loss=args.norm_pix_loss,
        perceptual_loss_weight=args.perceptual_loss_weight,
        perc_layers_resnet=args.perc_layers_resnet,
        perc_norm_ct_resnet=args.perc_norm_ct_resnet
    ).to(device)

    print(f"Model Parameters: {sum(p.numel() for p in model.parameters() if p.requires_grad):,}")

    # Get MAE data loader
    train_loader = get_mae_loader(
        data_dir=args.data_dir,
        batch_size=args.batch_size,
        num_workers=args.num_workers,
        crop_size=args.img_size, # Crop size must match model img_size
        augment=True
    )

    # Setup optimizer and scheduler
    optimizer_params = {
        'lr': args.lr,
        'epochs_after_warmup': max(0, args.epochs - args.warmup_epochs), # Ensure non-negative T_max
        'weight_decay': args.weight_decay,
        'use_amp': args.use_amp
    }
    optimizer, scheduler, scaler = setup_mae_training(model.parameters(), optimizer_params)

    start_epoch = 0
    best_loss = float('inf')

    # Resume logic
    if args.resume and os.path.isfile(args.resume):
        print(f"Resuming from checkpoint: {args.resume}")
        checkpoint = torch.load(args.resume, map_location='cpu')
        msg = model.load_state_dict(checkpoint['model_state_dict'], strict=False)
        print(f"Model load_state_dict message: {msg}")
        if 'optimizer_state_dict' in checkpoint:
             optimizer.load_state_dict(checkpoint['optimizer_state_dict'])
        if 'scheduler_state_dict' in checkpoint and hasattr(scheduler, 'load_state_dict'):
             if scheduler.T_max == optimizer_params['epochs_after_warmup']:
                 scheduler.load_state_dict(checkpoint['scheduler_state_dict'])
             else:
                 print("Warning: Scheduler T_max mismatch, not loading scheduler state.")
        start_epoch = checkpoint.get('epoch', 0)
        best_loss = checkpoint.get('loss', float('inf'))
        if args.use_amp and 'scaler_state_dict' in checkpoint and checkpoint['scaler_state_dict'] is not None:
            scaler.load_state_dict(checkpoint['scaler_state_dict'])
        print(f"Resumed from epoch {start_epoch}")
        if 'scheduler_state_dict' in checkpoint and hasattr(scheduler, 'load_state_dict') and scheduler.T_max == optimizer_params['epochs_after_warmup']:
             scheduler.last_epoch = max(0, start_epoch - args.warmup_epochs) -1

    # Training loop
    for epoch in range(start_epoch, args.epochs):
        print(f"\nEpoch {epoch+1}/{args.epochs}:")
        avg_losses = train_epoch_mae(
            model, train_loader, optimizer, scaler, device, writer, epoch,
            args.epochs, base_lr=args.lr, warmup_epochs=args.warmup_epochs,
            gradient_accumulation_steps=args.gradient_accumulation_steps, use_amp=args.use_amp,
            mask_ratio=args.mask_ratio
        )

        if epoch >= args.warmup_epochs:
             if hasattr(scheduler, 'step'):
                 scheduler.step()
             else:
                 print(f"Warning: Scheduler {type(scheduler)} does not have step method.")

        current_lr = optimizer.param_groups[0]['lr']
        print(f"Epoch {epoch+1}/{args.epochs} completed. Average Losses: Total={avg_losses['total_loss']:.6f}, MSE={avg_losses['mse_loss']:.6f}, Perceptual={avg_losses['perceptual_loss']:.6f}, LR={current_lr:.6f}")

        # Log epoch metrics
        for k, v in avg_losses.items():
            writer.add_scalar(f'Loss/train_epoch_{k}', v, epoch)
        writer.add_scalar('LR_epoch', current_lr, epoch)

        # Check if this is the best model
        is_best = avg_losses['total_loss'] < best_loss
        if is_best:
            best_loss = avg_losses['total_loss']
            print(f"New best loss: {best_loss:.6f}")

        # Save checkpoint
        if (epoch + 1) % args.save_interval == 0 or is_best:
             saved_args = {k: v for k, v in vars(args).items() if 'swin' in k or k in ['img_size', 'patch_size', 'decoder_embed_dim', 'decoder_depth', 'decoder_num_heads', 'lr', 'warmup_epochs', 'epochs', 'norm_pix_loss', 'seed']}
             checkpoint_data = {
                 'epoch': epoch + 1,
                 'model_state_dict': model.state_dict(),
                 'optimizer_state_dict': optimizer.state_dict(),
                 'scheduler_state_dict': scheduler.state_dict() if hasattr(scheduler, 'state_dict') else None,
                 'scaler_state_dict': scaler.state_dict() if args.use_amp else None,
                 'loss': avg_losses['total_loss'],
                 'args': saved_args
             }
             save_path = f"{args.checkpoint_dir}/checkpoint_epoch{epoch+1}.pth"
             torch.save(checkpoint_data, save_path)
             print(f"Saved checkpoint: {save_path}")
             if is_best:
                 best_path = f"{args.checkpoint_dir}/best_model.pth"
                 torch.save(checkpoint_data, best_path)
                 print(f"Saved best model to: {best_path}")

    writer.close()
    print("Swin MAE Pretraining with improved masking strategy completed!")


if __name__ == '__main__':
    parser = argparse.ArgumentParser('Swin MAE Simple Decoder Pretraining with Improved Masking Strategy', add_help=False)

    # Random Seed
    parser.add_argument('--seed', type=int, default=42, help='Random seed for reproducibility')

    # Model Parameters (Encoder - Swin Specific)
    parser.add_argument('--img_size', default=256, type=int, help='images input size')
    parser.add_argument('--patch_size', default=4, type=int, help='Swin patch size (usually 4)')
    parser.add_argument('--in_chans', default=1, type=int, help='Input channels')
    parser.add_argument('--swin_embed_dim', default=96, type=int, help='Swin encoder embedding dimension')
    parser.add_argument('--swin_depths', type=int, nargs='+', default=[2, 2, 6, 2], help='Swin encoder depth of each stage')
    parser.add_argument('--swin_num_heads', type=int, nargs='+', default=[3, 6, 12, 24], help='Swin encoder number of attention heads')
    parser.add_argument('--swin_window_size', type=int, default=7, help='Swin window size')
    parser.add_argument('--swin_ape', action='store_true', default=False, help='Use absolute position embedding in Swin encoder')
    parser.add_argument('--drop_path_rate', type=float, default=0.1, help='Drop path rate for Swin Transformer')

    # Model Parameters (Decoder)
    parser.add_argument('--decoder_embed_dim', default=512, type=int, help='Decoder embedding dimension')
    parser.add_argument('--decoder_depth', default=8, type=int, help='Decoder depth')
    parser.add_argument('--decoder_num_heads', default=16, type=int, help='Decoder number of attention heads')
    parser.add_argument('--decoder_mlp_ratio', default=4.0, type=float, help='Decoder MLP ratio')

    # Masking Parameters
    parser.add_argument('--mask_ratio', type=float, default=0.75, help='Ratio of patches to mask')

    # Loss Parameters
    parser.add_argument('--norm_pix_loss', action='store_true', default=False, help='Enable normalized pixel loss')
    parser.add_argument('--perceptual_loss_weight', type=float, default=0.01, help='Weight for ResNet18 perceptual loss')
    parser.add_argument('--perc_layers_resnet', type=str, nargs='+', default=['layer2', 'layer3'], help='ResNet layers for perceptual loss')
    parser.add_argument('--perc_norm_ct_resnet', action='store_true', default=True, help='Use custom CT normalization for ResNet')
    parser.add_argument('--no_perc_norm_ct_resnet', action='store_false', dest='perc_norm_ct_resnet', help='Disable custom CT normalization')

    # Training Parameters
    parser.add_argument('--batch_size', default=64, type=int, help='Batch size per GPU')
    parser.add_argument('--epochs', default=200, type=int, help='Number of epochs')
    parser.add_argument('--lr', type=float, default=1.5e-4, help='Base learning rate')
    parser.add_argument('--min_lr', type=float, default=1.5e-6, help='Minimum learning rate')
    parser.add_argument('--warmup_epochs', type=int, default=40, help='Epochs to warmup LR')
    parser.add_argument('--weight_decay', type=float, default=0.05, help='Weight decay')
    parser.add_argument('--use_amp', action='store_true', default=True, help='Enable mixed precision training')
    parser.add_argument('--gradient_accumulation_steps', type=int, default=2, help='Number of steps to accumulate gradients')

    # Dataset Parameters
    parser.add_argument('--data_dir', default='E:/vscode/2号CT数据', type=str, help='Dataset path')
    parser.add_argument('--num_workers', default=8, type=int, help='Number of data loading workers')

    # Checkpoint/Log Parameters
    parser.add_argument('--checkpoint_dir', default='checkpoints/swin_mae_improved_masking', help='Path to save checkpoints')
    parser.add_argument('--log_dir', default='logs/swin_mae_improved_masking', help='Path for tensorboard logs')
    parser.add_argument('--save_interval', default=20, type=int, help='Save checkpoint every x epochs')
    parser.add_argument('--resume', default='', help='Resume from checkpoint')

    args = parser.parse_args()
    pretrain_mae(args)