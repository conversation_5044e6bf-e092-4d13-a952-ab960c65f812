#!/usr/bin/env python3
"""
测试SSIM损失函数修复效果
验证各种维度和尺寸的tensor是否能正确处理
"""

import torch
import sys
import os

# 添加项目路径
sys.path.append(os.path.dirname(os.path.abspath(__file__)))

from utils.losses import SSIMLoss

def test_ssim_fix():
    """测试SSIM损失函数的修复效果"""
    print("🔧 测试SSIM损失函数修复效果...")
    
    # 初始化SSIM损失
    ssim_loss = SSIMLoss()
    
    # 测试用例1: 正常4D tensor
    print("\n✅ 测试1: 正常4D tensor (1, 1, 64, 64)")
    pred1 = torch.randn(1, 1, 64, 64)
    target1 = torch.randn(1, 1, 64, 64)
    try:
        loss1 = ssim_loss(pred1, target1)
        print(f"   结果: {loss1.item():.6f} ✅")
    except Exception as e:
        print(f"   错误: {e} ❌")
    
    # 测试用例2: 问题维度 (1, 1, 1, 21, 21)
    print("\n🔧 测试2: 问题维度 (1, 1, 1, 21, 21)")
    pred2 = torch.randn(1, 1, 1, 21, 21)
    target2 = torch.randn(1, 1, 1, 21, 21)
    try:
        loss2 = ssim_loss(pred2, target2)
        print(f"   结果: {loss2.item():.6f} ✅ (已修复)")
    except Exception as e:
        print(f"   错误: {e} ❌")
    
    # 测试用例3: 更复杂的维度 (1, 1, 1, 1, 16, 16)
    print("\n🔧 测试3: 复杂维度 (1, 1, 1, 1, 16, 16)")
    pred3 = torch.randn(1, 1, 1, 1, 16, 16)
    target3 = torch.randn(1, 1, 1, 1, 16, 16)
    try:
        loss3 = ssim_loss(pred3, target3)
        print(f"   结果: {loss3.item():.6f} ✅ (已修复)")
    except Exception as e:
        print(f"   错误: {e} ❌")
    
    # 测试用例4: 极小尺寸 (1, 1, 8, 8)
    print("\n🔧 测试4: 极小尺寸 (1, 1, 8, 8)")
    pred4 = torch.randn(1, 1, 8, 8)
    target4 = torch.randn(1, 1, 8, 8)
    try:
        loss4 = ssim_loss(pred4, target4)
        print(f"   结果: {loss4.item():.6f} ✅ (自动插值到32x32)")
    except Exception as e:
        print(f"   错误: {e} ❌")
    
    # 测试用例5: 不匹配的形状
    print("\n🔧 测试5: 不匹配的形状")
    pred5 = torch.randn(2, 1, 32, 32)
    target5 = torch.randn(1, 1, 32, 32)
    try:
        loss5 = ssim_loss(pred5, target5)
        print(f"   结果: {loss5.item():.6f} ✅ (自动调整形状)")
    except Exception as e:
        print(f"   错误: {e} ❌")
    
    print("\n🎉 SSIM损失函数修复测试完成！")

def test_gradient_loss_fix():
    """测试梯度损失函数的修复效果"""
    print("\n🔧 测试梯度损失函数修复效果...")
    
    try:
        from utils.losses import GradientLoss
        gradient_loss = GradientLoss()
        
        # 测试问题维度
        print("\n🔧 测试: 问题维度 (1, 1, 1, 21, 21)")
        pred = torch.randn(1, 1, 1, 21, 21)
        target = torch.randn(1, 1, 1, 21, 21)
        
        try:
            loss = gradient_loss(pred, target)
            print(f"   结果: {loss.item():.6f} ✅ (已修复)")
        except Exception as e:
            print(f"   错误: {e} ❌")
            
    except ImportError as e:
        print(f"   跳过梯度损失测试 (kornia未安装): {e}")
    
    print("\n🎉 梯度损失函数修复测试完成！")

if __name__ == "__main__":
    print("="*60)
    print("🧪 损失函数修复效果测试")
    print("="*60)
    
    test_ssim_fix()
    test_gradient_loss_fix()
    
    print("\n" + "="*60)
    print("✅ 所有测试完成！")
    print("="*60)
