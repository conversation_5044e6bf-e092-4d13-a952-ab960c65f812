data:
  aniso_prob: 0.5
  blur_kernel_size: 21
  blur_prob: 1.0
  blur_sigma_range:
  - 0.2
  - 3.0
  downsample_methods:
  - bicubic
  - bilinear
  - nearest
  downsample_prob: 1.0
  hr_dir: "data/3\u53F7CT\u6570\u636E"
  lr_dir: "data/2\u53F7CT\u6570\u636E"
  noise_gaussian_sigma_range:
  - 1
  - 25
  noise_prob: 1.0
  patch_size_hr: 128
  scale_factor: 4
  shuffle_prob: 0.5
diffusion:
  schedule_name: cosine
  timesteps: 1000
discriminator:
  lr: 0.0001
  n_layers: 3
  ndf: 64
inference:
  eta: 0.0
  sampling_steps: 100
model:
  attention_resolutions:
  - 16
  - 8
  base_channels: 128
  channel_mults:
  - 1
  - 2
  - 2
  - 4
  condition_method: CrossAttention
  dropout: 0.1
  encoder_checkpoint: null
  encoder_injection_levels:
  - 0
  - 1
  - 2
  - 3
  encoder_type: Swin-MAE
  freeze_encoder: true
  in_channels: 1
  num_heads: 8
  num_res_blocks: 2
  out_channels: 1
  use_adaptive_gate: true
  use_pretrained_encoder: false
training:
  batch_size: 8
  checkpoint_root: ./checkpoints/sr_diffusion
  diffusion_loss_type: l1
  epochs: 200
  gan_loss_weight: 0.05
  gradient_loss_weight: 0.1
  learning_rate: 0.0001
  log_interval: 1
  log_root: ./logs/sr_diffusion
  num_workers: 4
  perceptual_loss_type: l1
  perceptual_loss_weight: 0.1
  save_interval: 10
  seed: 42
  ssim_loss_weight: 0.2
  use_amp: true
  use_gan: true
  weight_decay: 0.0
