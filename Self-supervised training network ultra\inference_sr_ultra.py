#!/usr/bin/env python3
"""
Inference script for Conditional Diffusion Super-Resolution Model
Supports both patch-based and full-image super-resolution
"""

import os
import sys
import argparse
import torch
import torch.nn.functional as F
import numpy as np
from PIL import Image
import yaml
from tqdm import tqdm
import math

# Add project root to path
PROJECT_ROOT = os.path.abspath(os.path.join(os.path.dirname(__file__), '..'))
if PROJECT_ROOT not in sys.path:
    sys.path.insert(0, PROJECT_ROOT)

from models.diffusion_sr_model_ultra import DiffusionSRModel
from utils.diffusion_utils import get_diffusion_schedule, ddim_sample_loop

def load_config(config_path):
    """Load configuration from YAML file"""
    with open(config_path, 'r', encoding='utf-8') as f:
        config = yaml.safe_load(f)
    return config

def load_model(config, checkpoint_path, device):
    """Load trained diffusion model with enhanced checkpoint support"""
    model = DiffusionSRModel(config).to(device)

    # Load checkpoint
    checkpoint = torch.load(checkpoint_path, map_location=device)
    if 'model_state_dict' in checkpoint:
        model.load_state_dict(checkpoint['model_state_dict'])

        # Print checkpoint information if available
        if 'epoch' in checkpoint:
            print(f"Loaded model from epoch: {checkpoint['epoch']}")
        if 'best_total_loss' in checkpoint:
            print(f"Best total loss: {checkpoint['best_total_loss']:.6f}")
        if 'best_epoch' in checkpoint:
            print(f"Best epoch: {checkpoint['best_epoch']}")
    else:
        model.load_state_dict(checkpoint)

    model.eval()
    print(f"Model loaded from: {checkpoint_path}")
    return model

def find_best_model(checkpoint_dir):
    """Find the best model in checkpoint directory"""
    # Priority order for model selection
    model_priorities = [
        'best_model_generator.pth',  # Best generator (preferred for inference)
        'best_model.pth',           # Best complete model
        'final_model_generator.pth', # Final generator
        'final_model.pth'           # Final complete model
    ]

    for model_name in model_priorities:
        model_path = os.path.join(checkpoint_dir, model_name)
        if os.path.exists(model_path):
            print(f"Auto-selected model: {model_name}")
            return model_path

    # Fallback: look for any checkpoint files
    checkpoint_files = [f for f in os.listdir(checkpoint_dir)
                       if f.startswith('checkpoint_epoch_') and f.endswith('.pth')]

    if checkpoint_files:
        # Get the latest checkpoint
        latest_checkpoint = sorted(checkpoint_files)[-1]
        model_path = os.path.join(checkpoint_dir, latest_checkpoint)
        print(f"Auto-selected latest checkpoint: {latest_checkpoint}")
        return model_path

    return None

def get_circle_mask(image_shape, center=None, radius_ratio=0.45):
    """Generate circular mask for CT images"""
    h, w = image_shape
    if center is None:
        center_y, center_x = h // 2, w // 2
    else:
        center_y, center_x = center

    radius = int(min(h, w) * radius_ratio)
    Y, X = np.ogrid[:h, :w]
    dist_from_center = np.sqrt((X - center_x)**2 + (Y - center_y)**2)
    return dist_from_center <= radius

def extract_patches_with_overlap(image, patch_size, overlap, mask=None):
    """Extract overlapping patches from image"""
    h, w = image.shape
    stride = patch_size - overlap
    patches = []
    positions = []

    for i in range(0, h - patch_size + 1, stride):
        for j in range(0, w - patch_size + 1, stride):
            patch = image[i:i+patch_size, j:j+patch_size]

            # Check if patch is mostly within valid region
            if mask is not None:
                patch_mask = mask[i:i+patch_size, j:j+patch_size]
                if np.mean(patch_mask) < 0.5:  # Skip if less than 50% valid
                    continue

            patches.append(patch)
            positions.append((i, j))

    return patches, positions

def merge_patches_with_blending(patches, positions, target_shape, patch_size, overlap):
    """Merge overlapping patches with blending"""
    h, w = target_shape
    result = np.zeros((h, w), dtype=np.float32)
    weight_map = np.zeros((h, w), dtype=np.float32)

    # Create blending weights (higher in center, lower at edges)
    blend_weights = np.ones((patch_size, patch_size), dtype=np.float32)
    if overlap > 0:
        fade_size = overlap // 2
        for i in range(fade_size):
            weight = (i + 1) / (fade_size + 1)
            blend_weights[i, :] *= weight
            blend_weights[-(i+1), :] *= weight
            blend_weights[:, i] *= weight
            blend_weights[:, -(i+1)] *= weight

    # Merge patches
    for patch, (i, j) in zip(patches, positions):
        end_i = min(i + patch_size, h)
        end_j = min(j + patch_size, w)

        patch_h = end_i - i
        patch_w = end_j - j

        current_weights = blend_weights[:patch_h, :patch_w]

        result[i:end_i, j:end_j] += patch[:patch_h, :patch_w] * current_weights
        weight_map[i:end_i, j:end_j] += current_weights

    # Normalize by weights
    valid_mask = weight_map > 0
    result[valid_mask] /= weight_map[valid_mask]

    return result

def inference_single_image(model, lr_image_path, output_path, config, device,
                         patch_based=True, patch_size=32, overlap=8):
    """Perform super-resolution on a single image"""

    # Load image
    lr_image = Image.open(lr_image_path).convert('L')
    lr_array = np.array(lr_image, dtype=np.float32) / 65535.0  # Normalize to [0,1]

    # Apply circular mask
    mask = get_circle_mask(lr_array.shape)
    lr_array_masked = lr_array * mask

    if patch_based:
        # Patch-based inference for large images
        print(f"Processing {lr_image_path} with patch-based inference...")

        # Extract patches
        patches, positions = extract_patches_with_overlap(
            lr_array_masked, patch_size, overlap, mask
        )

        if len(patches) == 0:
            print(f"Warning: No valid patches found for {lr_image_path}")
            return

        # Process patches in batches
        sr_patches = []
        batch_size = config['training']['batch_size']

        with torch.no_grad():
            for i in tqdm(range(0, len(patches), batch_size), desc="Processing patches"):
                batch_patches = patches[i:i+batch_size]

                # Convert to tensors
                batch_tensors = []
                for patch in batch_patches:
                    # Normalize to [-1, 1] for diffusion model
                    patch_tensor = torch.from_numpy(patch).unsqueeze(0).unsqueeze(0)  # [1, 1, H, W]
                    patch_tensor = (patch_tensor - 0.5) * 2.0  # [0,1] -> [-1,1]
                    batch_tensors.append(patch_tensor)

                batch_tensor = torch.cat(batch_tensors, dim=0).to(device)

                # Generate SR patches
                sr_batch = model.sample(
                    condition=batch_tensor,
                    batch_size=len(batch_patches),
                    device=device,
                    sampling_timesteps=config['inference']['sampling_steps'],
                    eta=config['inference']['eta']
                )

                # Convert back to numpy
                sr_batch = (sr_batch + 1.0) / 2.0  # [-1,1] -> [0,1]
                sr_batch = sr_batch.cpu().numpy()

                for j in range(len(batch_patches)):
                    sr_patches.append(sr_batch[j, 0])  # Remove batch and channel dims

        # Merge patches
        scale_factor = config['data']['scale_factor']
        target_shape = (lr_array.shape[0] * scale_factor, lr_array.shape[1] * scale_factor)
        sr_result = merge_patches_with_blending(
            sr_patches,
            [(pos[0]*scale_factor, pos[1]*scale_factor) for pos in positions],
            target_shape,
            patch_size * scale_factor,
            overlap * scale_factor
        )

    else:
        # Direct inference (for smaller images)
        print(f"Processing {lr_image_path} with direct inference...")

        with torch.no_grad():
            # Convert to tensor
            lr_tensor = torch.from_numpy(lr_array_masked).unsqueeze(0).unsqueeze(0)  # [1, 1, H, W]
            lr_tensor = (lr_tensor - 0.5) * 2.0  # [0,1] -> [-1,1]
            lr_tensor = lr_tensor.to(device)

            # Generate SR image
            sr_tensor = model.sample(
                condition=lr_tensor,
                batch_size=1,
                device=device,
                sampling_timesteps=config['inference']['sampling_steps'],
                eta=config['inference']['eta']
            )

            # Convert back to numpy
            sr_result = (sr_tensor + 1.0) / 2.0  # [-1,1] -> [0,1]
            sr_result = sr_result.squeeze().cpu().numpy()

    # Apply circular mask to result
    scale_factor = config['data']['scale_factor']
    sr_mask = get_circle_mask((sr_result.shape[0], sr_result.shape[1]))
    sr_result = sr_result * sr_mask

    # Convert to uint16 and save
    sr_result = np.clip(sr_result, 0, 1) * 65535
    sr_result = sr_result.astype(np.uint16)

    # Save result
    sr_image = Image.fromarray(sr_result, mode='I;16')
    sr_image.save(output_path)
    print(f"Saved SR result to: {output_path}")

def main():
    parser = argparse.ArgumentParser(description='Super-Resolution Inference')
    parser.add_argument('--config', type=str, required=True, help='Path to config file')
    parser.add_argument('--checkpoint', type=str, default=None, help='Path to model checkpoint (auto-detect if not provided)')
    parser.add_argument('--checkpoint_dir', type=str, default=None, help='Checkpoint directory for auto-detection')
    parser.add_argument('--input_dir', type=str, required=True, help='Input directory with LR images')
    parser.add_argument('--output_dir', type=str, required=True, help='Output directory for SR images')
    parser.add_argument('--patch_based', action='store_true', help='Use patch-based inference')
    parser.add_argument('--patch_size', type=int, default=32, help='Patch size for inference')
    parser.add_argument('--overlap', type=int, default=8, help='Overlap between patches')

    args = parser.parse_args()

    # Load config
    config = load_config(args.config)

    # Setup device
    device = torch.device('cuda' if torch.cuda.is_available() else 'cpu')
    print(f"Using device: {device}")

    # Determine checkpoint path
    checkpoint_path = args.checkpoint
    if not checkpoint_path:
        if args.checkpoint_dir:
            checkpoint_path = find_best_model(args.checkpoint_dir)
        else:
            # Try to auto-detect from config experiment name
            experiment_name = config.get('experiment', {}).get('name', 'default')
            auto_checkpoint_dir = os.path.join('./checkpoints/sr_diffusion', experiment_name)
            if os.path.exists(auto_checkpoint_dir):
                checkpoint_path = find_best_model(auto_checkpoint_dir)
                print(f"Auto-detected checkpoint directory: {auto_checkpoint_dir}")

    if not checkpoint_path or not os.path.exists(checkpoint_path):
        print("❌ No valid checkpoint found. Please specify --checkpoint or --checkpoint_dir")
        return

    # Load model
    model = load_model(config, checkpoint_path, device)

    # Create output directory
    os.makedirs(args.output_dir, exist_ok=True)

    # Get input images
    input_files = [f for f in os.listdir(args.input_dir)
                   if f.lower().endswith(('.tif', '.tiff', '.png', '.jpg', '.jpeg'))]

    print(f"Found {len(input_files)} images to process")

    # Process each image
    for filename in tqdm(input_files, desc="Processing images"):
        input_path = os.path.join(args.input_dir, filename)
        output_filename = f"sr_{os.path.splitext(filename)[0]}.tif"
        output_path = os.path.join(args.output_dir, output_filename)

        try:
            inference_single_image(
                model, input_path, output_path, config, device,
                patch_based=args.patch_based,
                patch_size=args.patch_size,
                overlap=args.overlap
            )
        except Exception as e:
            print(f"Error processing {filename}: {e}")
            continue

    print("Inference completed!")

if __name__ == '__main__':
    main()
