# CT图像超分辨率项目 - 更新版

## 🎯 项目概述

本项目旨在通过自监督预训练和监督训练相结合的方式，实现岩心CT图像的超分辨率重建。项目已取得重大突破，正处于从自监督预训练优化到监督训练启动的关键阶段。

### 📊 当前项目状态

#### 🚀 自监督预训练重大突破
- **最佳性能**: PSNR = 21.51 dB, SSIM = 0.626
- **最佳模型**: `train_swin_mae_resnet_random_mask_hierarchical.py`
- **最佳检查点**: `checkpoints/swin_mae_hierarchical_random_ssim_nce_w002/best_model.pth`
- **关键技术**: 层次化解码器 + 随机掩码 + SSIM损失 + PatchNCE对比损失

#### 🎯 项目目标
1. **短期目标**: 自监督预训练PSNR突破22.0，SSIM达到0.65+
2. **核心目标**: 实现高质量CT图像超分辨率，PSNR提升2-3dB
3. **最终目标**: 为岩心CT图像分析提供高分辨率重建能力

## 📁 数据集信息

- **数据集#2**: 用于自监督预训练
  - 1218个切片，2700×2700像素
  - 体素大小: 0.03889mm
  - 路径: `data/2号CT数据`

- **数据集#3**: 用于监督训练（高分辨率参考）
  - 897个切片，2700×2700像素
  - 体素大小: 0.00956mm
  - 路径: `data/3号CT数据`

## 🚀 快速开始

### 1. 环境准备
```bash
# 激活Python环境
source activate pytorchEnv  # 或您的环境名称

# 验证GPU可用性
python -c "import torch; print(f'CUDA: {torch.cuda.is_available()}')"
```

### 2. 自监督预训练最终优化
```bash
# 启动最终优化训练（目标：PSNR > 22.0）
bash start_final_optimization.sh
```

### 3. 监督超分辨率训练
```bash
# 启动监督训练
bash start_supervised_training.sh
```

## 🔧 核心技术架构

### 自监督预训练阶段
- **编码器**: Swin Transformer (Swin-T配置)
- **解码器**: 层次化解码器（跨阶段特征融合）
- **掩码策略**: 随机掩码（编码器处理完整图像）
- **损失函数**: 
  - 重建损失 (L1/L2)
  - ResNet18感知损失
  - SSIM损失（结构相似性）
  - PatchNCE对比损失（特征判别性）

### 监督训练阶段
- **架构**: 条件扩散模型 (Conditional U-Net)
- **预训练集成**: 冻结/微调的Swin-MAE编码器
- **特征融合**: 自适应门控机制 + 交叉注意力
- **损失函数**:
  - 扩散损失 (噪声预测)
  - 感知损失 (VGG特征)
  - SSIM损失 (结构保持)
  - 梯度损失 (边缘清晰度)
  - GAN损失 (真实感)

## 📈 性能指标

### 自监督预训练
| 指标 | 当前最佳 | 目标 | 状态 |
|------|---------|------|------|
| PSNR | 21.51 dB | >22.0 dB | 🔥 接近目标 |
| SSIM | 0.626 | >0.65 | 🚀 显著提升 |

### 监督训练
| 指标 | 目标 | 状态 |
|------|------|------|
| 超分辨率PSNR | 提升2-3dB | ⏳ 准备启动 |
| 超分辨率SSIM | 提升5-10% | ⏳ 准备启动 |

## 📋 项目结构

```
├── train_swin_mae_resnet_random_mask_hierarchical.py  # 最佳预训练模型
├── train_swin_mae_final_optimization.py              # 最终优化脚本
├── start_final_optimization.sh                       # 预训练启动脚本
├── start_supervised_training.sh                      # 监督训练启动脚本
├── Self-supervised training network ultra/           # 超分辨率网络
│   ├── train_sr_ultra.py                            # 监督训练主脚本
│   ├── configs/config_ultra.yaml                    # 配置文件
│   ├── models/diffusion_sr_model_ultra.py          # 扩散模型
│   └── data/sr_dataloader.py                       # 数据加载器
├── checkpoints/                                      # 模型检查点
│   └── swin_mae_hierarchical_random_ssim_nce_w002/  # 最佳预训练模型
├── data/                                            # 数据目录
│   ├── 2号CT数据/                                   # 低分辨率数据
│   └── 3号CT数据/                                   # 高分辨率数据
└── memory-bank/                                     # 项目文档
    ├── project_plan.md                             # 项目计划
    ├── progress.md                                 # 进展记录
    └── activeContext.md                            # 当前上下文
```

## 🔄 三阶段实施计划

### 阶段一: 自监督预训练最终优化 (1-2周)
- **目标**: PSNR从21.51提升到22.0+，SSIM从0.626提升到0.65+
- **方法**: 超参数精细调优，模型架构微调，训练策略优化
- **执行**: `bash start_final_optimization.sh`

### 阶段二: 监督训练启动 (1周)
- **目标**: 成功启动超分辨率监督训练并建立基线
- **方法**: 配置文件修正，环境验证，基线训练
- **执行**: `bash start_supervised_training.sh`

### 阶段三: 监督训练优化 (2-3周)
- **目标**: 实现高质量CT图像超分辨率
- **方法**: 损失函数优化，编码器微调，门控优化
- **预期**: 超分辨率PSNR提升2-3dB，SSIM提升5-10%

## 🛠️ 关键配置

### 最佳预训练配置
```bash
python train_swin_mae_final_optimization.py \
    --decoder_embed_dim 256 \
    --decoder_depths 2 2 2 2 \
    --decoder_num_heads 12 8 6 4 \
    --patchnce_loss_weight 0.003 \
    --ssim_loss_weight 0.06 \
    --perceptual_loss_weight 0.005
```

### 监督训练配置
```yaml
# config_ultra.yaml 关键设置
model:
  encoder_checkpoint: ../checkpoints/swin_mae_hierarchical_random_ssim_nce_w002/best_model.pth
  freeze_encoder: true
  use_adaptive_gate: true
training:
  batch_size: 8
  epochs: 200
  perceptual_loss_weight: 0.1
  ssim_loss_weight: 0.2
```

## 📊 监控和评估

### TensorBoard监控
```bash
# 预训练监控
tensorboard --logdir logs/swin_mae_final_optimization

# 监督训练监控
tensorboard --logdir logs/sr_diffusion
```

### 关键指标
- **预训练**: PSNR, SSIM, 重建损失, 对比损失
- **监督训练**: 扩散损失, 感知损失, SSIM损失, 梯度损失

## 🎯 预期成果

### 短期里程碑 (2周内)
- ✅ 自监督预训练PSNR突破22.0
- ✅ SSIM达到0.65+
- ✅ 监督训练成功启动

### 最终目标 (6周内)
- 🏆 超分辨率PSNR提升2-3dB
- 🏆 视觉质量显著改善
- 🏆 完整的评估报告和模型交付

## 📞 技术支持

如遇到问题，请检查：
1. GPU内存是否充足 (建议16GB+)
2. 数据路径是否正确
3. 预训练模型是否存在
4. Python环境依赖是否完整

项目已达到业界先进水平，预期能够取得优秀的最终结果！
