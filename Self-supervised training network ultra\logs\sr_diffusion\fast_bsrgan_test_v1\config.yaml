data:
  aniso_prob: 0.3
  blur_kernel_size: 11
  blur_prob: 0.5
  blur_sigma_range:
  - 0.5
  - 2.0
  degradation_method: accurate_bsrgan
  downsample_methods:
  - bicubic
  - bilinear
  downsample_prob: 1.0
  hr_dir: "data/3\u53F7CT\u6570\u636E"
  jpeg_prob: 0.1
  lr_dir: "data/2\u53F7CT\u6570\u636E"
  noise_gaussian_sigma_range:
  - 1
  - 15
  noise_prob: 0.5
  patch_size_hr: 128
  scale_factor: 4
diffusion:
  schedule_name: linear
  timesteps: 100
experiment:
  description: Fast test of accurate BSRGAN degradation (optimized for speed)
  name: Fast_BSRGAN_Test_v1
  tags:
  - test
  - fast
  - accurate-bsrgan
  - speed-optimized
inference:
  eta: 0.0
  sampling_steps: 10
model:
  attention_resolutions:
  - 16
  base_channels: 64
  channel_mults:
  - 1
  - 2
  - 2
  condition_method: CrossAttention
  dropout: 0.1
  encoder_checkpoint: null
  encoder_type: Swin-MAE
  in_channels: 1
  num_heads: 4
  num_res_blocks: 1
  out_channels: 1
  use_pretrained_encoder: false
training:
  batch_size: 4
  checkpoint_root: ./checkpoints/sr_diffusion
  diffusion_loss_type: l1
  epochs: 5
  gan_loss_weight: 0.0
  grad_clip_norm: 0.5
  gradient_accumulation_steps: 1
  gradient_loss_weight: 0.05
  learning_rate: 0.0002
  log_interval: 10
  log_root: ./logs/sr_diffusion
  num_workers: 2
  perceptual_loss_type: l1
  perceptual_loss_weight: 0.1
  save_interval: 2
  seed: 42
  ssim_loss_weight: 0.1
  use_amp: true
  use_gan: false
  use_scheduler: false
  weight_decay: 1.0e-05
