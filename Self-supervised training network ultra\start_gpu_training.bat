@echo off
echo 🚀 启动GPU环境训练...
echo.

REM 激活conda环境
call conda activate pytorchEnv
if %errorlevel% neq 0 (
    echo ❌ 无法激活pytorchEnv环境
    pause
    exit /b 1
)

echo ✅ 已激活pytorchEnv环境

REM 检查CUDA是否可用
python -c "import torch; print(f'CUDA可用: {torch.cuda.is_available()}'); print(f'GPU数量: {torch.cuda.device_count()}'); print(f'当前设备: {torch.cuda.get_device_name(0) if torch.cuda.is_available() else \"无GPU\"}')"

echo.
echo 🔥 开始训练...
echo.

REM 启动训练
python train_sr_ultra.py --config configs/config_ultra_no_gan.yaml --tag baseline_gpu_fixed_v1

echo.
echo 训练完成或中断
pause
