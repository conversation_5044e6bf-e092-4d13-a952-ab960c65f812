import os
import torch
import glob
import argparse
import numpy as np
import matplotlib.pyplot as plt
import matplotlib.font_manager as fm
from datetime import datetime
from tensorboard.backend.event_processing.event_accumulator import EventAccumulator
import warnings

# Suppress specific UserWarning from tight_layout if it still occurs indirectly
warnings.filterwarnings("ignore", message="This figure includes Axes that are not compatible with tight_layout")

class TrainingAnalyzer:
    """
    MAE 预训练分析工具:
    1. 分析TensorBoard日志 (支持最新或合并所有日志)
    2. 绘制检查点损失曲线
    """

    def __init__(self, output_dir=None):
        """初始化分析器并设置输出目录"""
        if output_dir is None:
            # Default output directory specific to MAE analysis
            self.output_dir = os.path.join("plots", # Use relative path
                                          f"mae_analysis_{datetime.now().strftime('%Y%m%d_%H%M%S')}")
        else:
            self.output_dir = output_dir

        os.makedirs(self.output_dir, exist_ok=True)
        print(f"分析结果将保存到: {self.output_dir}")

    def _setup_plot_style(self):
        """设置 Matplotlib 绘图风格 (Times New Roman)"""
        plt.style.use('seaborn-v0_8-whitegrid')
        plt.rcParams.update({
            'font.family': 'serif',
            'font.serif': ['Times New Roman'],
            'mathtext.fontset': 'stix',
            'axes.unicode_minus': False,
            'figure.dpi': 300,
            'axes.linewidth': 0.8,
            'axes.edgecolor': 'black',
            'grid.color': '#E5E5E5',
            'grid.linestyle': ':',
            'grid.linewidth': 0.3,
            'lines.linewidth': 1.2,
            'font.size': 9,
            'axes.titlesize': 10,
            'axes.labelsize': 9,
            'xtick.labelsize': 8,
            'ytick.labelsize': 8,
            'legend.fontsize': 8,
            'legend.frameon': True,
            'legend.edgecolor': 'none',
            'legend.facecolor': 'white',
            'legend.framealpha': 0.6,
        })

    def analyze_tensorboard_logs(self, log_dir='logs/mae_pretrain', mode='latest', smooth_factor=0.9):
        """
        分析TensorBoard日志文件。

        Args:
            log_dir (str): TensorBoard日志目录。
            mode (str): 分析模式 ('latest' 或 'all')。'all'会合并所有日志文件的数据。
            smooth_factor (float): 平滑因子 (0-1)。
        """
        if not os.path.isdir(log_dir):
            print(f"错误: 日志目录不存在 {log_dir}")
            return None
        event_files = [f for f in os.listdir(log_dir) if f.startswith('events.out.tfevents')]
        if not event_files:
            print(f"未在 {log_dir} 中找到任何 TensorBoard 日志文件")
            return None

        if mode == 'latest':
            event_file_path = max([os.path.join(log_dir, f) for f in event_files], key=os.path.getmtime)
            print(f"分析最新的训练日志: {event_file_path}")
            stats = self._analyze_single_log(event_file_path, smooth_factor)
            print(f"TensorBoard日志分析完成")
            return [stats] if stats else [] # Return stats in a list for consistency
        elif mode == 'all':
            print(f"分析所有训练日志 ({len(event_files)} 个文件) 并合并...")
            combined_data = {}
            all_metrics = set()
            event_file_paths = [os.path.join(log_dir, f) for f in event_files]

            # Load data from all files
            for event_file in event_file_paths:
                try:
                    event_acc = EventAccumulator(event_file)
                    event_acc.Reload()
                    available_scalars = event_acc.Tags().get('scalars', [])
                    all_metrics.update(available_scalars)
                    for metric in available_scalars:
                        try:
                            # Filter out potential NaN/inf values during extraction
                            steps_values = [(s.step, s.value) for s in event_acc.Scalars(metric) if np.isfinite(s.value)]
                            if not steps_values: continue # Skip if no valid data points
                            if metric not in combined_data:
                                combined_data[metric] = []
                            combined_data[metric].extend(steps_values)
                        except KeyError:
                            print(f"警告: 无法从 {os.path.basename(event_file)} 提取指标 '{metric}'")
                except Exception as e:
                    print(f"错误: 无法加载或处理日志文件 {event_file}: {e}")

            if not combined_data:
                print("未能从任何日志文件中加载有效数据。")
                return []

            # Sort and remove duplicates for each metric
            for metric in combined_data:
                # Sort by step
                combined_data[metric].sort(key=lambda x: x[0])
                # Remove duplicates based on step, keeping the first occurrence
                unique_data = []
                seen_steps = set()
                for step, value in combined_data[metric]:
                    if step not in seen_steps:
                        unique_data.append((step, value))
                        seen_steps.add(step)
                combined_data[metric] = unique_data

            # Now plot the combined data
            stats = self._plot_combined_logs(combined_data, smooth_factor)
            print(f"合并的 TensorBoard 日志分析完成")
            return [stats] if stats else [] # Return stats in a list for consistency
        else:
            print(f"错误: 未知的模式 '{mode}'")
            return None

    def _plot_combined_logs(self, combined_data, smooth_factor=0.9):
        """为合并后的日志数据生成图表"""
        available_scalars = list(combined_data.keys())
        n_metrics = len(available_scalars)

        if n_metrics == 0:
            print("没有合并后的标量数据可供绘制。")
            return None

        # Determine layout
        if n_metrics <= 2: rows, cols = 1, n_metrics
        elif n_metrics <= 4: rows, cols = 2, 2
        elif n_metrics <= 6: rows, cols = 2, 3
        else: rows, cols = (n_metrics + 2) // 3, 3

        # Setup plot style
        self._setup_plot_style()
        plt.rcParams['figure.figsize'] = (5 * cols, 4 * rows) # Adjust figure size

        fig = plt.figure() # Use default layout manager initially
        gs = fig.add_gridspec(rows, cols, wspace=0.3, hspace=0.4) # Adjusted spacing
        stats = {}

        for i, metric in enumerate(available_scalars):
            ax = fig.add_subplot(gs[i])
            steps_values = combined_data.get(metric, [])
            if not steps_values: continue

            steps, values = zip(*steps_values)
            steps = np.array(steps)
            values = np.array(values)

            # Filter out potential non-finite values before calculating stats
            finite_values = values[np.isfinite(values)]
            if finite_values.size == 0:
                print(f"警告: 指标 '{metric}' 没有有效的数值数据点。")
                stats[metric] = {'final': np.nan, 'best': np.nan, 'mean': np.nan, 'std': np.nan}
                continue # Skip plotting if no valid data

            # Store stats based on finite values
            stats[metric] = {
                'final': finite_values[-1],
                'best': np.min(finite_values) if "loss" in metric.lower() else np.max(finite_values),
                'mean': np.mean(finite_values),
                'std': np.std(finite_values)
            }

            # Plot raw and smoothed
            ax.plot(steps, values, color='#AAAAAA', alpha=0.4, linewidth=0.8, label='Raw', zorder=1) # Lighter raw
            smooth_values = np.copy(values)
            if len(smooth_values) > 1:
                # Simple exponential moving average
                for j in range(1, len(smooth_values)):
                     # Handle potential NaNs during smoothing
                     if np.isfinite(smooth_values[j-1]) and np.isfinite(values[j]):
                         smooth_values[j] = smooth_values[j-1] * smooth_factor + values[j] * (1 - smooth_factor)
                     elif np.isfinite(values[j]):
                         smooth_values[j] = values[j] # Start new smoothing if previous was NaN
                     else:
                         smooth_values[j] = np.nan # Propagate NaN
                ax.plot(steps, smooth_values, color='#0B559F', linewidth=1.2, label='Smoothed', zorder=2) # Darker blue
            else:
                ax.plot(steps, values, color='#0B559F', linewidth=1.2, label='Data', zorder=2)

            # Axis setup
            ax.spines['top'].set_visible(False); ax.spines['right'].set_visible(False)
            ax.spines['left'].set_linewidth(0.8); ax.spines['bottom'].set_linewidth(0.8)
            ax.set_title(metric, pad=10) # Increased padding
            xlabel = 'Epoch' if 'epoch' in metric.lower() else 'Step'
            ax.set_xlabel(xlabel, labelpad=6) # Increased padding
            ax.set_ylabel('Value', labelpad=6) # Increased padding
            ax.tick_params(direction='out', length=3, width=0.8, pad=4) # Increased padding

            # Log scale for LR
            if 'LR' in metric or 'lr' in metric.lower():
                ax.set_yscale('log')
                ax.grid(True, which='both', axis='y', alpha=0.2) # Grid only on y for log
                ax.grid(True, which='major', axis='x', alpha=0.2)
            else:
                ax.grid(True, alpha=0.2)
                ymin, ymax = ax.get_ylim()
                if not np.isclose(ymin, ymax):
                    y_margin = (ymax - ymin) * 0.08
                    ax.set_ylim(ymin - y_margin, ymax + y_margin)

            # Stats text
            stats_text = (f'Final: {stats[metric]["final"]:.4g}\nBest: {stats[metric]["best"]:.4g}\n'
                          f'Mean: {stats[metric]["mean"]:.4g}\nStd: {stats[metric]["std"]:.4g}')
            text_x, text_y, text_ha = (0.98, 0.98, 'right') if 'LR' in metric else (0.02, 0.98, 'left')
            ax.text(text_x, text_y, stats_text, transform=ax.transAxes, fontsize=7, family='monospace',
                   verticalalignment='top', horizontalalignment=text_ha,
                   bbox=dict(boxstyle='round,pad=0.4', facecolor='white', edgecolor='#DDDDDD', alpha=0.8)) # Added subtle border

            # Legend
            legend_loc = 'lower right' if 'LR' in metric else 'lower left'
            legend_anchor = (0.98, 0.02) if 'LR' in metric else (0.02, 0.02)
            ax.legend(loc=legend_loc, frameon=True, framealpha=0.8, edgecolor='none',
                     facecolor='white', bbox_to_anchor=legend_anchor)

        # Use fig.tight_layout() for better automatic spacing adjustment
        try:
            fig.tight_layout(pad=2.0) # Add padding
        except ValueError:
             print("Warning: tight_layout failed, using default layout.")


        output_file = os.path.join(self.output_dir, 'mae_log_analysis_combined.png') # Combined filename
        plt.savefig(output_file, dpi=300, bbox_inches='tight', facecolor='white', edgecolor='none')
        print(f"Combined analysis chart saved to {output_file}")
        plt.close(fig)
        return stats

    def _analyze_single_log(self, event_file, smooth_factor=0.9):
        """分析单个TensorBoard日志文件 (用于 mode='latest')"""
        # 提取文件名用于输出
        try:
            # Attempt to get a meaningful part of the filename
            fname = os.path.basename(event_file)
            parts = fname.split('.')
            if len(parts) > 3 and parts[-3].isdigit(): # e.g., events.out.tfevents.1743503759.DESKTOP-VIC5FR9.20780.0
                file_name_part = parts[-3]
            else: # Fallback for different naming conventions
                file_name_part = fname.replace('events.out.tfevents.', '').split('.')[0]
        except IndexError:
            file_name_part = "unknown" # Fallback

        # 读取TensorBoard日志
        try:
            event_acc = EventAccumulator(event_file)
            event_acc.Reload()
        except Exception as e:
            print(f"错误: 无法加载日志文件 {event_file}: {e}")
            return None

        # 获取可用的指标
        available_scalars = event_acc.Tags().get('scalars', [])
        if not available_scalars:
             print(f"日志文件中未找到标量数据: {event_file}")
             return None
        print(f"Available metrics in {os.path.basename(event_file)}: {available_scalars}")

        # 确定子图数量和布局
        n_metrics = len(available_scalars)

        # 优化布局
        if n_metrics == 0: return None
        elif n_metrics <= 2: rows, cols = 1, n_metrics
        elif n_metrics <= 4: rows, cols = 2, 2
        elif n_metrics <= 6: rows, cols = 2, 3
        else: rows, cols = (n_metrics + 2) // 3, 3

        # 设置绘图风格
        self._setup_plot_style()
        plt.rcParams['figure.figsize'] = (5 * cols, 4 * rows)

        # 创建图表
        fig = plt.figure()
        gs = fig.add_gridspec(rows, cols, wspace=0.3, hspace=0.4)
        stats = {}

        for i, metric in enumerate(available_scalars):
            ax = fig.add_subplot(gs[i])
            try:
                steps_values = [(s.step, s.value) for s in event_acc.Scalars(metric) if np.isfinite(s.value)]
            except KeyError:
                print(f"警告: 无法提取指标 '{metric}' 的数据")
                continue
            if not steps_values:
                print(f"指标 '{metric}' 没有有效的数值数据点")
                continue

            steps, values = zip(*steps_values)
            steps = np.array(steps)
            values = np.array(values)

            # Store stats
            stats[metric] = {
                'final': values[-1],
                'best': np.min(values) if "loss" in metric.lower() else np.max(values),
                'mean': np.mean(values),
                'std': np.std(values)
            }

            # Plot raw and smoothed
            ax.plot(steps, values, color='#AAAAAA', alpha=0.4, linewidth=0.8, label='Raw', zorder=1)
            smooth_values = np.copy(values)
            if len(smooth_values) > 1:
                for j in range(1, len(smooth_values)):
                     if np.isfinite(smooth_values[j-1]) and np.isfinite(values[j]):
                         smooth_values[j] = smooth_values[j-1] * smooth_factor + values[j] * (1 - smooth_factor)
                     elif np.isfinite(values[j]):
                         smooth_values[j] = values[j]
                     else:
                         smooth_values[j] = np.nan
                ax.plot(steps, smooth_values, color='#0B559F', linewidth=1.2, label='Smoothed', zorder=2)
            else:
                ax.plot(steps, values, color='#0B559F', linewidth=1.2, label='Data', zorder=2)

            # Axis setup
            ax.spines['top'].set_visible(False); ax.spines['right'].set_visible(False)
            ax.spines['left'].set_linewidth(0.8); ax.spines['bottom'].set_linewidth(0.8)
            ax.set_title(metric, pad=10)
            xlabel = 'Epoch' if 'epoch' in metric.lower() else 'Step'
            ax.set_xlabel(xlabel, labelpad=6)
            ax.set_ylabel('Value', labelpad=6)
            ax.tick_params(direction='out', length=3, width=0.8, pad=4)

            # Log scale for LR
            if 'LR' in metric or 'lr' in metric.lower():
                ax.set_yscale('log')
                ax.grid(True, which='both', axis='y', alpha=0.2)
                ax.grid(True, which='major', axis='x', alpha=0.2)
            else:
                ax.grid(True, alpha=0.2)
                ymin, ymax = ax.get_ylim()
                if not np.isclose(ymin, ymax):
                    y_margin = (ymax - ymin) * 0.08
                    ax.set_ylim(ymin - y_margin, ymax + y_margin)

            # Stats text
            stats_text = (f'Final: {stats[metric]["final"]:.4g}\nBest: {stats[metric]["best"]:.4g}\n'
                          f'Mean: {stats[metric]["mean"]:.4g}\nStd: {stats[metric]["std"]:.4g}')
            text_x, text_y, text_ha = (0.98, 0.98, 'right') if 'LR' in metric else (0.02, 0.98, 'left')
            ax.text(text_x, text_y, stats_text, transform=ax.transAxes, fontsize=7, family='monospace',
                   verticalalignment='top', horizontalalignment=text_ha,
                   bbox=dict(boxstyle='round,pad=0.4', facecolor='white', edgecolor='#DDDDDD', alpha=0.8))

            # Legend
            legend_loc = 'lower right' if 'LR' in metric else 'lower left'
            legend_anchor = (0.98, 0.02) if 'LR' in metric else (0.02, 0.02)
            ax.legend(loc=legend_loc, frameon=True, framealpha=0.8, edgecolor='none',
                     facecolor='white', bbox_to_anchor=legend_anchor)

        # Use fig.tight_layout() for better automatic spacing adjustment
        try:
            fig.tight_layout(pad=2.0)
        except ValueError:
             print("Warning: tight_layout failed, using default layout.")

        # 保存图表
        output_file = os.path.join(self.output_dir, f'mae_log_analysis_{file_name_part}.png')
        plt.savefig(output_file, dpi=300, bbox_inches='tight', facecolor='white', edgecolor='none')
        print(f"Analysis chart saved to {output_file}")
        plt.close(fig)

        return stats

    def plot_checkpoint_loss(self, checkpoint_dir='checkpoints/mae_pretrain'):
        """绘制检查点损失曲线"""
        if not os.path.isdir(checkpoint_dir):
            print(f"错误: 检查点目录不存在 {checkpoint_dir}")
            return
        checkpoint_files = glob.glob(os.path.join(checkpoint_dir, "checkpoint_epoch*.pth"))
        if not checkpoint_files:
             print(f"在 {checkpoint_dir} 中未找到 'checkpoint_epoch*.pth' 文件")
             return
        try:
            checkpoint_files.sort(key=lambda x: int(os.path.basename(x).split("epoch")[1].split(".")[0]))
        except (IndexError, ValueError):
             print("错误: 无法从文件名中解析 epoch 数字进行排序。请确保文件名格式为 'checkpoint_epoch<number>.pth'")
             return


        # Load best model to get best epoch/loss
        best_epoch = None
        best_loss = None
        best_model_path = os.path.join(checkpoint_dir, "best_model.pth")
        if os.path.exists(best_model_path):
            try:
                # Use weights_only=True if you only need metadata and not the model weights
                best_checkpoint = torch.load(best_model_path, map_location='cpu', weights_only=False) # Set to False if you need more than weights
                best_epoch = best_checkpoint.get('epoch')
                best_loss = best_checkpoint.get('loss')
                if best_epoch is None or best_loss is None:
                     print("警告: 未能从 best_model.pth 中提取 epoch 或 loss")
                     best_epoch, best_loss = None, None
            except Exception as e:
                print(f"警告: 加载或解析 best_model.pth 时出错: {e}")
                best_epoch, best_loss = None, None


        # Collect loss values from all epoch checkpoints
        epochs = []
        losses = []
        final_epoch = None
        final_loss = None

        for file in checkpoint_files:
            try:
                # Use weights_only=True if you only need metadata and not the model weights
                checkpoint = torch.load(file, map_location='cpu', weights_only=False) # Set to False if you need more than weights
                epoch = checkpoint.get('epoch')
                loss = checkpoint.get('loss')
                if epoch is not None and loss is not None and np.isfinite(loss): # Check for finite loss
                    epochs.append(epoch)
                    losses.append(loss)
                    final_epoch = epoch # Update final epoch/loss based on sorted list
                    final_loss = loss
                else:
                    print(f"警告: 跳过检查点 {os.path.basename(file)}，缺少 epoch/loss 或 loss 无效。")
            except Exception as e:
                print(f"警告: 加载或解析检查点 {os.path.basename(file)} 时出错: {e}")

        if not epochs:
            print("未找到有效的检查点数据用于绘图。")
            return

        # If best_loss wasn't directly in best_model.pth, find it from the list
        if best_epoch is not None and best_loss is None:
             try:
                 best_loss_index = epochs.index(best_epoch)
                 best_loss = losses[best_loss_index]
             except ValueError:
                 print(f"警告: 最佳 epoch {best_epoch} 未在检查点列表中找到。")
                 best_epoch, best_loss = None, None # Reset if epoch not found


        # 设置绘图风格
        self._setup_plot_style()
        plt.rcParams['figure.figsize'] = (10, 6)

        # 创建图表
        fig = plt.figure()
        ax = fig.add_subplot(111)

        # 绘制损失曲线
        ax.plot(epochs, losses, color='#ff7f0e', alpha=0.4, linewidth=0.8, # Changed raw data color to orange
               label='Raw Epoch Loss', zorder=1)

        # 平滑处理
        smooth_losses = np.array(losses)
        if len(smooth_losses) > 1:
            for i in range(1, len(smooth_losses)):
                 if np.isfinite(smooth_losses[i-1]) and np.isfinite(losses[i]):
                     smooth_losses[i] = smooth_losses[i-1] * 0.9 + losses[i] * 0.1 # Keep smoothing factor
                 elif np.isfinite(losses[i]):
                      smooth_losses[i] = losses[i]
                 else:
                      smooth_losses[i] = np.nan # Propagate NaN
            ax.plot(epochs, smooth_losses, color='#1f77b4', linewidth=1.5, # Standard blue, slightly thicker
                   label='Smoothed Epoch Loss', zorder=2)
        else:
             ax.plot(epochs, losses, color='#1f77b4', linewidth=1.5,
                   label='Epoch Loss', zorder=2)


        # 标记最佳模型和最终模型
        if best_epoch is not None and best_loss is not None:
            ax.plot(best_epoch, best_loss, 'o', color='#d62728', markersize=7, # Red circle marker
                    label=f'Best (Epoch {best_epoch})', zorder=3, markeredgecolor='white', markeredgewidth=0.5)

        if final_epoch is not None and final_loss is not None:
            ax.plot(final_epoch, final_loss, 'o', color='#2ca02c', markersize=7, # Green circle marker
                    label=f'Final (Epoch {final_epoch})', zorder=3, markeredgecolor='white', markeredgewidth=0.5)

        # 优化坐标轴
        ax.spines['top'].set_visible(False); ax.spines['right'].set_visible(False)
        ax.spines['left'].set_linewidth(0.8); ax.spines['bottom'].set_linewidth(0.8)

        # 设置标题和标签
        ax.set_title('MAE Checkpoint Loss Curve', fontname='Times New Roman', pad=10)
        ax.set_xlabel('Epoch', fontname='Times New Roman', labelpad=6)
        ax.set_ylabel('Loss', fontname='Times New Roman', labelpad=6)

        # 优化刻度
        ax.tick_params(direction='out', length=3, width=0.8, pad=4)
        ax.grid(True, alpha=0.2)

        # 自动调整y轴范围
        valid_losses = [l for l in losses if np.isfinite(l)]
        if valid_losses:
            ymin, ymax = min(valid_losses), max(valid_losses)
            if not np.isclose(ymin, ymax):
                y_margin = (ymax - ymin) * 0.1
                ax.set_ylim(max(0, ymin - y_margin), ymax + y_margin)


        # 添加统计信息
        stats_text = (f'Final Loss: {final_loss:.4g}\n'
                     f'Best Loss: {min(valid_losses):.4g}\n'
                     f'Mean Loss: {np.mean(valid_losses):.4g}\n'
                     f'Std Dev: {np.std(valid_losses):.4g}')

        # 智能定位统计信息框
        if losses[0] > losses[-1]: text_x, text_y, text_ha = 0.98, 0.98, 'right'
        else: text_x, text_y, text_ha = 0.02, 0.98, 'left'

        ax.text(text_x, text_y, stats_text,
               transform=ax.transAxes, fontsize=7, family='monospace',
               verticalalignment='top', horizontalalignment=text_ha,
               bbox=dict(boxstyle='round,pad=0.4', facecolor='white', edgecolor='#DDDDDD', alpha=0.8))

        # 将图例放在右侧
        ax.legend(loc='center right', frameon=True, framealpha=0.8, edgecolor='none',
                 facecolor='white', prop={'family': 'Times New Roman'})

        # 调整布局，为右侧图例留出空间
        try:
            fig.tight_layout(rect=[0, 0, 0.88, 1]) # Leave space on the right
        except ValueError:
             print("Warning: tight_layout failed, using default layout.")


        # 保存图表
        output_file = os.path.join(self.output_dir, 'mae_checkpoint_loss_curve.png')
        plt.savefig(output_file, dpi=300, bbox_inches='tight', facecolor='white', edgecolor='none')
        print(f"Checkpoint loss curve saved to {output_file}")

        plt.close(fig)


def main():
    """命令行接口"""
    parser = argparse.ArgumentParser(description="MAE Pretraining Analysis Tool")

    # 通用参数
    parser.add_argument("--output_dir", type=str, default=None,
                        help="分析结果输出目录 (默认: plots/mae_analysis_YYYYMMDD_HHMMSS)")

    # 子命令
    subparsers = parser.add_subparsers(dest="command", help="分析命令", required=True)

    # 1. 分析TensorBoard日志
    analyze_parser = subparsers.add_parser("analyze_logs", help="分析 MAE TensorBoard 日志")
    analyze_parser.add_argument("--log_dir", type=str, default="logs/mae_pretrain",
                              help="TensorBoard日志目录")
    analyze_parser.add_argument("--mode", type=str, default="latest", choices=["latest", "all"],
                              help="分析模式: 'latest'仅分析最新日志, 'all'合并所有日志")
    analyze_parser.add_argument("--smooth", type=float, default=0.9,
                              help="平滑因子 (0-1)")

    # 2. 绘制检查点损失曲线
    plot_parser = subparsers.add_parser("plot_checkpoints", help="绘制 MAE 检查点损失曲线")
    plot_parser.add_argument("--checkpoint_dir", type=str, default="checkpoints/mae_pretrain",
                           help="检查点目录")

    args = parser.parse_args()

    # 创建分析器
    analyzer = TrainingAnalyzer(output_dir=args.output_dir)

    # 执行命令
    if args.command == "analyze_logs":
        analyzer.analyze_tensorboard_logs(
            log_dir=args.log_dir,
            mode=args.mode,
            smooth_factor=args.smooth
        )
    elif args.command == "plot_checkpoints":
        analyzer.plot_checkpoint_loss(
            checkpoint_dir=args.checkpoint_dir
        )

if __name__ == "__main__":
    main()
