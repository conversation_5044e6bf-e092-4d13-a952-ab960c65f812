# CT图像超分辨率项目技术背景

## 使用的技术

1. **深度学习框架**：
   - PyTorch：主要深度学习框架
   - torchvision：图像处理和模型组件
   - timm：提供预训练模型和组件，用于MAE和Swin-MAE实现

2. **模型架构**：
   - Swin Transformer：层次化视觉Transformer，用于`train_swin_mae.py`
   - Vision Transformer (ViT)：标准视觉Transformer，用于`train_mae.py`
   - MAE (Masked Autoencoder)：自监督学习方法，随机掩蔽图像区域并重建
   - 多尺度对比学习：用于`train_enhanced.py`中的自监督预训练
   - EnhancedSRNetwork：超分辨率网络，结合多种先进组件

3. **损失函数**：
   - MSE/L1损失：基础重建损失，用于像素级比较
   - PerceptualLoss：基于VGG16提取特征，通过通道复制处理灰度图
   - FrequencyLoss/FrequencyDomainLoss：在频域计算差异，考虑体素大小
   - StructureSimilarityLoss：关注结构而非像素级差异
   - TextureLoss：特别关注岩心纹理特征
   - CompositeLoss：组合多种损失，考虑体素大小差异

4. **注意力机制**：
   - EnhancedFrequencyChannelAttention：频域通道注意力，专为CT图像设计
   - WaveletAttention：小波注意力，用于多尺度特征提取
   - MultiScaleFeatureFusion：多尺度特征融合
   - DeformableLKABlock：可变形大核注意力

5. **图像处理**：
   - OpenCV/PIL：基础图像处理
   - scikit-image：高级图像处理和评估
   - 傅里叶变换：用于频域处理和损失计算
   - 小波变换：用于多尺度特征提取

6. **训练技巧**：
   - 梯度累积：处理大尺寸CT图像时增加有效批量大小
   - 混合精度训练：使用FP16加速训练
   - 学习率预热：稳定初期训练
   - 余弦退火学习率调度：优化训练过程

7. **评估指标**：
   - PSNR (Peak Signal-to-Noise Ratio)
   - SSIM (Structural Similarity Index)
   - 频域分析：评估频域特征恢复情况
   - HU值分布分析：评估CT值分布的准确性

8. **实验管理**：
   - Tensorboard：实验跟踪和可视化
   - 检查点保存与恢复：保存训练状态和最佳模型

## 开发环境

1. **硬件要求**：
   - GPU：NVIDIA GPU，至少8GB显存
   - RAM：至少16GB
   - 存储：足够存储大型CT数据集的空间

2. **软件环境**：
   - 操作系统：Windows
   - Python 3.8.0 ('pytorchEnv')，路径: D:\anaconda\anaconda\envs\pytorchEnv\python.exe
   - CUDA 11.0+（用于GPU加速）
   - cuDNN（用于深度学习加速）
   - 所有程序运行必须在此Python环境中进行

3. **主要依赖库**：
   ```
   torch>=1.7.0
   torchvision>=0.8.0
   numpy
   opencv-python
   scikit-image
   matplotlib
   tensorboard
   ```

## 技术约束

1. **计算资源限制**：
   - 处理2700×2700大尺寸CT图像需要大量GPU内存
   - 可能需要分块处理或降采样策略

2. **数据限制**：
   - 高分辨率参考数据（数据集#3）数量有限
   - 需要有效利用自监督学习和数据增强

3. **模型大小**：
   - Swin Transformer模型参数量大
   - 需要平衡模型容量和计算效率

4. **灰度图像处理**：
   - 大多数预训练模型针对RGB图像设计
   - 需要适应CT图像的灰度特性

## 依赖关系

1. **核心代码文件**：
   - `train_swin_mae_refactored.py`：重构版Swin-MAE训练代码
   - `train_swin_mae.py`：原始Swin-MAE训练代码
   - `E:/vscode/非配位超分辨/data/mae_loader.py`：数据加载器

2. **外部依赖**：
   - Swin Transformer实现
   - MAE实现
   - 预训练特征提取器（如VGG）

3. **数据依赖**：
   - 数据集#2：用于自监督预训练（2700×2700像素，1218个切片）
   - 数据集#3：用于监督训练（897个切片，高分辨率参考）

## 工具使用模式

1. **数据预处理**：
   - 使用Python脚本进行CT图像预处理
   - 数据集划分为训练/验证/测试集

2. **模型训练**：
   - 使用配置文件定义训练参数
   - 通过命令行启动训练任务
   - 使用Tensorboard监控训练进度

3. **结果评估**：
   - 计算定量评估指标（PSNR、SSIM等）
   - 生成可视化比较结果
   - 分析不同损失函数组合的效果

4. **模型部署**：
   - 导出训练好的模型
   - 提供推理脚本用于处理新的CT图像

## 技术挑战与解决方案

1. **针对CT图像的特征提取器**：
   - 挑战：标准VGG等模型主要针对RGB自然图像训练
   - 解决方案：探索专门针对医学/CT图像的预训练特征提取器，或考虑自定义特征提取方法

2. **大尺寸图像处理**：
   - 挑战：2700×2700像素图像处理需要大量内存
   - 解决方案：分块处理、渐进式训练或使用混合精度训练

3. **有效的掩蔽策略**：
   - 挑战：为CT图像设计合适的掩蔽策略
   - 解决方案：探索不同的掩蔽比例和模式，适应CT图像的特点
