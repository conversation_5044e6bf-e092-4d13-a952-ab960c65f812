# 非重叠岩心 CT 数据超分辨率 (监督训练阶段)

本项目旨在使用预训练的自监督模型 (如 MAE, Swin-MAE) 作为编码器，结合条件扩散模型，对非重叠的低分辨率岩心 CT 数据 (数据集 #2) 进行超分辨率重建，利用高分辨率数据集 (#3) 的特征分布信息。

## 方案概述

- **模型:** 条件 U-Net 扩散模型
- **条件:** 低分辨率 CT 图像 (数据集 #2)，可能通过预训练编码器提取特征注入
- **预训练编码器:** 加载在数据集 #2 上预训练的 ViT-MAE 或 Swin-MAE 权重
- **目标:** 生成在视觉和分布上接近数据集 #3 (HR) 的超分辨率图像
- **损失函数:** 扩散损失 (MSE/L1 on noise) + 感知损失 + (可选) 对抗损失

## 文件结构

- `README.md`: 本文档
- `train_sr.py`: 主训练脚本
- `inference_sr.py`: 推理脚本
- `configs/`: 配置文件目录
  - `diffusion_sr_config.yaml`: 训练超参数、路径等配置
- `data/`: 数据加载模块
  - `sr_dataloader.py`: 数据集类和 Dataloader 实现
- `models/`: 模型定义
  - `diffusion_sr_model.py`: 条件 U-Net 扩散模型实现
  - `discriminator.py`: (可选) PatchGAN 判别器实现
- `utils/`: 工具函数
  - `losses.py`: 损失函数 (感知损失, GAN 损失等)
  - `evaluation.py`: 评估指标计算 (PSNR, SSIM, FID 等)
  - `logger.py`: 日志记录 (TensorBoard)
- `pretrained_encoders/`: (可选) 存放或链接预训练编码器权重

## 运行指南

(待补充)
