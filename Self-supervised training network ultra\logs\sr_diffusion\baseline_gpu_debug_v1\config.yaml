data:
  aniso_prob: 0.5
  blur_kernel_size: 21
  blur_prob: 1.0
  blur_sigma_range:
  - 0.2
  - 3.0
  downsample_methods:
  - bicubic
  - bilinear
  - nearest
  downsample_prob: 1.0
  hr_dir: "data/3\u53F7CT\u6570\u636E"
  lr_dir: "data/2\u53F7CT\u6570\u636E"
  noise_gaussian_sigma_range:
  - 1
  - 25
  noise_prob: 1.0
  patch_size_hr: 128
  scale_factor: 4
  shuffle_prob: 0.5
diffusion:
  schedule_name: cosine
  timesteps: 1000
discriminator:
  lr: 0.0001
  n_layers: 3
  ndf: 64
experiment:
  description: Baseline diffusion SR without GAN loss (focus on diffusion+perceptual+SSIM+gradient)
  name: SwinMAE_Baseline_NoGAN_v1
  tags:
  - diffusion
  - super-resolution
  - no-gan
  - ct-images
  - stable-training
inference:
  eta: 0.0
  sampling_steps: 50
model:
  attention_resolutions:
  - 16
  - 8
  base_channels: 128
  channel_mults:
  - 1
  - 2
  - 2
  - 4
  condition_method: CrossAttention
  dropout: 0.1
  encoder_checkpoint: null
  encoder_type: Swin-MAE
  in_channels: 1
  num_heads: 8
  num_res_blocks: 2
  out_channels: 1
  use_pretrained_encoder: false
training:
  batch_size: 8
  checkpoint_root: ./checkpoints/sr_diffusion
  diffusion_loss_type: l1
  epochs: 200
  gan_loss_weight: 0.0
  grad_clip_norm: 1.0
  gradient_accumulation_steps: 2
  gradient_loss_weight: 0.15
  learning_rate: 0.0001
  log_interval: 50
  log_root: ./logs/sr_diffusion
  num_workers: 4
  perceptual_loss_type: l1
  perceptual_loss_weight: 0.15
  save_interval: 10
  scheduler_type: cosine
  seed: 42
  ssim_loss_weight: 0.25
  use_amp: true
  use_gan: false
  use_scheduler: true
  warmup_epochs: 20
  weight_decay: 0.0001
