# 增强模型保存和加载指南

## 🎯 概述

增强模型保存系统遵循预训练模式，在训练过程中自动跟踪并保存性能最佳的模型。这确保您始终能够访问用于推理的最优模型。

## 📁 模型文件类型

训练完成后，您将在检查点目录中找到以下模型文件：

### **最佳模型（推荐使用）**
- **`best_model_generator.pth`** ⭐ **推荐用于推理**
  - 仅包含生成器模型（文件大小最小）
  - 基于总损失的最佳性能模型
  - 针对推理任务优化

- **`best_model.pth`** ⭐ **推荐用于恢复训练**
  - 包含所有训练状态的完整检查点
  - 包含优化器、调度器等的最佳性能模型
  - 用于从最佳点恢复训练

### **最终模型**
- **`final_model_generator.pth`**
  - 来自最后训练轮次的生成器模型
  - 如果您特别需要最终轮次模型时使用

- **`final_model.pth`**
  - 来自最后训练轮次的完整检查点
  - 包含最终轮次的所有训练状态

### **常规检查点**
- **`checkpoint_epoch_X.pth`**
  - 每N个轮次保存一次（通过`save_interval`配置）
  - 包含该轮次的完整训练状态
  - 用于调试或从特定轮次恢复

## 🔍 模型信息

每个保存的模型都包含全面的信息：

```python
checkpoint = {
    'epoch': epoch,                    # 训练轮次
    'global_step': global_step,        # 总训练步数
    'model_state_dict': model.state_dict(),  # 模型权重
    'optimizer_state_dict': optimizer.state_dict(),  # 优化器状态
    'scheduler_state_dict': scheduler.state_dict(),   # 学习率调度器状态
    'scaler_state_dict': scaler.state_dict(),         # AMP缩放器状态
    'total_loss': total_loss,          # 当前轮次损失
    'best_total_loss': best_total_loss,  # 达到的最佳损失
    'best_epoch': best_epoch,          # 最佳损失出现的轮次
    'config': config,                  # 训练配置
    'args': args,                      # 命令行参数
    # GAN特定状态（如果适用）
    'discriminator_state_dict': discriminator.state_dict(),
    'optimizer_D_state_dict': optimizer_D.state_dict(),
}
```

## 🚀 使用示例

### **1. 使用最佳模型进行推理**

```bash
# 自动最佳模型检测
python inference_sr_ultra.py \
    --config configs/config_ultra_no_gan.yaml \
    --input_dir "../data/2号CT数据" \
    --output_dir "results/best_model" \
    --patch_based

# 手动指定最佳模型
python inference_sr_ultra.py \
    --config configs/config_ultra_no_gan.yaml \
    --checkpoint "checkpoints/sr_diffusion/SwinMAE_Baseline_NoGAN_v1/best_model_generator.pth" \
    --input_dir "../data/2号CT数据" \
    --output_dir "results/best_model" \
    --patch_based
```

### **2. 从最佳模型恢复训练**

```bash
python train_sr_ultra.py \
    --config configs/config_ultra_no_gan.yaml \
    --resume "checkpoints/sr_diffusion/SwinMAE_Baseline_NoGAN_v1/best_model.pth"
```

### **3. 测试模型加载**

```bash
# 测试所有保存的模型
python test_model_loading.py \
    --checkpoint_dir "checkpoints/sr_diffusion/SwinMAE_Baseline_NoGAN_v1" \
    --config configs/config_ultra_no_gan.yaml

# 自动检测最新检查点目录
python test_model_loading.py \
    --config configs/config_ultra_no_gan.yaml
```

### **4. 自动检测批量推理**

```bash
python batch_inference.py \
    --config configs/config_ultra_no_gan.yaml \
    --checkpoint_dir "checkpoints/sr_diffusion/SwinMAE_Baseline_NoGAN_v1" \
    --input_dir "../data/2号CT数据" \
    --output_dir "results/batch_processing" \
    --num_gpus 2
```

## 📊 模型选择优先级

推理脚本按以下优先级顺序自动选择模型：

1. **`best_model_generator.pth`** （最高优先级）
2. **`best_model.pth`**
3. **`final_model_generator.pth`**
4. **`final_model.pth`**
5. **最新的 `checkpoint_epoch_X.pth`** （备选）

## 🔧 训练输出示例

训练期间，您将看到如下输出：

```
Epoch 45 Avg Losses -> G_Total: 0.4523 | Diffusion: 0.0234 | Perceptual: 0.4123 | SSIM: 0.0166
发现新的最佳模型！总损失：0.452300，轮次：45
保存检查点：checkpoints/sr_diffusion/SwinMAE_Baseline_NoGAN_v1/checkpoint_epoch_50.pth
新的最佳模型已保存，总损失：0.452300
最佳生成器模型保存至：checkpoints/sr_diffusion/SwinMAE_Baseline_NoGAN_v1/best_model_generator.pth

...

训练完成！最佳总损失：0.452300，轮次：45
模型保存至：checkpoints/sr_diffusion/SwinMAE_Baseline_NoGAN_v1
最佳模型位置：checkpoints/sr_diffusion/SwinMAE_Baseline_NoGAN_v1/best_model.pth
最佳生成器模型位置：checkpoints/sr_diffusion/SwinMAE_Baseline_NoGAN_v1/best_model_generator.pth
```

## 🎯 最佳实践

### **推理方面：**
- 始终使用 `best_model_generator.pth` 以获得最佳结果
- 使用自动检测功能避免手动指定路径
- 在运行大规模推理前测试模型加载

### **训练方面：**
- 从 `best_model.pth` 恢复以从最优点继续
- 监控训练期间的"发现新的最佳模型！"消息
- 频繁保存检查点（推荐每10轮）

### **评估方面：**
- 比较 `best_model_generator.pth` 与 `final_model_generator.pth` 的结果
- 使用测试脚本验证所有模型正确加载
- 跟踪最佳轮次和损失值用于报告

## 🔍 故障排除

### **模型未找到：**
```bash
❌ 未找到有效检查点。请指定 --checkpoint 或 --checkpoint_dir
```
**解决方案：** 检查检查点目录是否存在并包含模型文件。

### **加载错误：**
```bash
❌ 加载检查点状态时出错：...
```
**解决方案：** 确保配置文件与训练期间使用的模型架构匹配。

### **内存问题：**
```bash
CUDA内存不足
```
**解决方案：** 使用 `best_model_generator.pth`（较小）而不是完整检查点进行推理。

## 📈 性能对比

| 模型类型 | 文件大小 | 加载速度 | 使用场景 |
|------------|-----------|---------------|----------|
| `best_model_generator.pth` | ~500MB | 快速 | ✅ 推理 |
| `best_model.pth` | ~1GB | 中等 | ✅ 恢复训练 |
| `final_model.pth` | ~1GB | 中等 | 调试/分析 |
| `checkpoint_epoch_X.pth` | ~1GB | 中等 | 特定轮次 |

## 🎉 增强保存的优势

1. **自动最佳模型跟踪：** 无需手动损失比较
2. **多种模型类型：** 针对不同使用场景优化
3. **完整状态保存：** 从中断处精确恢复训练
4. **自动检测：** 智能推理模型选择
5. **全面元数据：** 保存完整训练信息
6. **预训练兼容性：** 与现有预训练模式一致

这个增强保存系统确保您始终能够访问性能最佳的模型，同时保持与现有工作流程的完全兼容性。
