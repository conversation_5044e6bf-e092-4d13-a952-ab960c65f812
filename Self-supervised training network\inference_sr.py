# -*- coding: utf-8 -*-
"""
Inference script for the Conditional Diffusion Super-Resolution model.
Loads a trained model and performs SR on input Low-Resolution images.
"""

import torch
import yaml
import argparse
import os
from PIL import Image
import torchvision.transforms as T
import torchvision.transforms.functional as TF
from tqdm import tqdm
import numpy as np

# --- Local Imports ---
from models.diffusion_sr_model import DiffusionSRModel
# Assuming config loading function is in train_sr or a utils file
# from train_sr import load_config # Or from utils.config import load_config

# --- Helper Functions ---
def load_config(config_path):
    """ Loads config from YAML file. """
    with open(config_path, 'r') as f:
        config = yaml.safe_load(f)
    return config

def save_image(tensor, filename):
    """ Saves a tensor image (range [-1, 1]) to file. """
    # Denormalize from [-1, 1] to [0, 1]
    tensor = (tensor + 1.0) / 2.0
    tensor = tensor.squeeze(0).cpu().clamp(0, 1) # Remove batch dim, clamp
    # Convert to PIL image
    img = TF.to_pil_image(tensor)
    img.save(filename)
    print(f"Saved image to {filename}")

# --- Argument Parsing ---
def parse_args():
    parser = argparse.ArgumentParser(description='Inference for Conditional Diffusion SR Model')
    parser.add_argument('--config', type=str, required=True, help='Path to the configuration YAML file used during training')
    parser.add_argument('--checkpoint', type=str, required=True, help='Path to the trained model checkpoint (.pth)')
    parser.add_argument('--input_dir', type=str, required=True, help='Directory containing Low-Resolution input images')
    parser.add_argument('--output_dir', type=str, required=True, help='Directory to save the Super-Resolved output images')
    parser.add_argument('--sampling_steps', type=int, default=50, help='Number of DDIM sampling steps')
    parser.add_argument('--eta', type=float, default=0.0, help='DDIM eta parameter (0 for deterministic)')
    parser.add_argument('--device', type=str, default='cuda', help='Device to use (cuda or cpu)')
    # Add arguments for batch size if processing multiple images at once
    # parser.add_argument('--batch_size', type=int, default=1, help='Batch size for inference')
    args = parser.parse_args()
    return args

# --- Main Inference Function ---
def inference(args):
    # --- Setup ---
    device = torch.device(args.device if torch.cuda.is_available() else "cpu")
    print(f"Using device: {device}")

    # Load configuration used during training
    config = load_config(args.config)
    print("Loaded training configuration.")

    # Create output directory
    os.makedirs(args.output_dir, exist_ok=True)
    print(f"Output directory: {args.output_dir}")

    # --- Model Initialization and Loading ---
    print("Initializing model...")
    model = DiffusionSRModel(config).to(device)

    if not os.path.isfile(args.checkpoint):
        print(f"Error: Checkpoint file not found at {args.checkpoint}")
        return

    print(f"Loading checkpoint: {args.checkpoint}")
    checkpoint = torch.load(args.checkpoint, map_location=device)
    try:
        # Adjust key if necessary based on how checkpoints are saved
        if 'model_state_dict' in checkpoint:
            model.load_state_dict(checkpoint['model_state_dict'])
        else:
            model.load_state_dict(checkpoint) # Assume checkpoint is the state_dict
        print("Model weights loaded successfully.")
    except Exception as e:
        print(f"Error loading model state_dict: {e}")
        print("Attempting to load with strict=False...")
        try:
             if 'model_state_dict' in checkpoint:
                 model.load_state_dict(checkpoint['model_state_dict'], strict=False)
             else:
                 model.load_state_dict(checkpoint, strict=False)
             print("Model weights loaded with strict=False.")
        except Exception as e2:
             print(f"Failed to load model weights even with strict=False: {e2}")
             return

    model.eval() # Set model to evaluation mode

    # --- Prepare Input Transform ---
    # Input transform should match the conditioning input during training
    # Typically: ToTensor, Normalize to [-1, 1]
    input_channels = config['model'].get('in_channels', 1)
    lr_transform = T.Compose([
        T.ToTensor(), # Converts to [0, 1]
        T.Normalize((0.5,) * input_channels, (0.5,) * input_channels) # Normalize to [-1, 1]
    ])

    # --- Process Input Images ---
    input_files = sorted([
        os.path.join(args.input_dir, f) for f in os.listdir(args.input_dir)
        if f.lower().endswith(('.png', '.jpg', '.jpeg', '.tif', '.bmp'))
    ])

    if not input_files:
        print(f"No image files found in input directory: {args.input_dir}")
        return

    print(f"Found {len(input_files)} images to process.")

    with torch.no_grad():
        for img_path in tqdm(input_files, desc="Processing Images"):
            try:
                # Load LR image
                lr_image_pil = Image.open(img_path)
                # Ensure correct channels
                if input_channels == 1 and lr_image_pil.mode != 'L':
                    lr_image_pil = lr_image_pil.convert('L')
                elif input_channels == 3 and lr_image_pil.mode != 'RGB':
                    lr_image_pil = lr_image_pil.convert('RGB')
                elif lr_image_pil.mode not in ['L', 'RGB']:
                    lr_image_pil = lr_image_pil.convert('L' if input_channels == 1 else 'RGB')

                # Apply transform and add batch dimension
                lr_tensor = lr_transform(lr_image_pil).unsqueeze(0).to(device)

                # Perform sampling
                # Assuming batch_size=1 for simplicity here
                sr_tensor = model.sample(
                    condition=lr_tensor,
                    batch_size=1,
                    device=device,
                    sampling_timesteps=args.sampling_steps,
                    sampling_method='DDIM', # Hardcoded as it's the implemented one
                    eta=args.eta
                )

                # Save the output image
                base_filename = os.path.basename(img_path)
                output_filename = os.path.join(args.output_dir, f"{os.path.splitext(base_filename)[0]}_SR.png") # Save as PNG
                save_image(sr_tensor, output_filename)

            except Exception as e:
                print(f"Error processing image {img_path}: {e}")
                continue # Skip to the next image

    print("Inference finished.")

if __name__ == '__main__':
    args = parse_args()
    inference(args)
