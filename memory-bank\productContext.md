# CT图像超分辨率项目产品背景

## 项目存在的原因

岩心CT扫描是研究岩石内部结构的重要手段，但受限于设备和成本因素，获取超高分辨率CT图像往往困难。通过深度学习方法提高现有CT图像的分辨率，可以在不增加硬件成本的情况下，获得更清晰的岩心内部结构图像，为地质研究和石油工程提供更精确的数据支持。

## 解决的问题

1. **分辨率限制**：现有CT扫描设备可能无法达到研究所需的超高分辨率
2. **成本约束**：高分辨率CT扫描设备价格昂贵，使用成本高
3. **数据质量**：低分辨率CT图像可能无法显示岩心的细微结构和特征
4. **分析精度**：低质量图像导致后续分析（如孔隙度、渗透率计算）精度不足

## 预期工作方式

1. **自监督预训练阶段**：
   - 输入：数据集#2中的2700×2700像素CT图像
   - 处理：使用多种自监督学习方法（Swin-MAE、MAE、多尺度对比学习）
   - 输出：预训练模型，学习到CT图像的特征表示

2. **非重叠数据处理策略**：
   - 挑战：数据集#2和#3来自同一岩心的不同、非重叠部位，无法直接进行像素级配对训练
   - 解决方案：
     - **伪配对训练**：对高分辨率的3号CT数据进行降采样，生成对应的低分辨率版本，使用这些配对数据训练SR网络
     - **特征级对齐**：不直接比较像素值，而是在特征空间进行比较，使用预训练特征提取器计算特征距离
     - **域适应方法**：学习低分辨率域到高分辨率域的映射，保持内容一致性

3. **监督训练阶段**：
   - 输入：数据集#3（高分辨率）及其降采样版本（伪低分辨率）
   - 处理：使用预训练模型为基础，通过监督学习学习超分辨率映射
   - 输出：能够将低分辨率CT图像转换为高分辨率图像的模型
   - 应用：将训练好的模型应用于数据集#2（真实低分辨率数据）

4. **推理阶段**：
   - 输入：数据集#2的低分辨率CT图像
   - 处理：使用训练好的模型进行超分辨率重建
   - 输出：高分辨率CT图像，保留更多岩心结构细节

## 用户体验目标

1. **研究人员视角**：
   - 能够从现有CT扫描获得更高质量的图像
   - 无需重新进行高成本的CT扫描
   - 可以观察到更多岩心细微结构
   - 提高后续分析的准确性

2. **技术用户视角**：
   - 模型训练流程清晰，易于复现
   - 推理过程高效，资源消耗合理
   - 可以根据不同CT图像特性进行参数调整
   - 结果可靠，无明显伪影

## 成功的产品形态

1. **完整的训练和推理流程**：
   - 数据预处理模块
   - 自监督预训练模块
   - 监督训练模块
   - 推理和评估模块

2. **优化的损失函数组合**：
   - 针对CT图像特性的感知损失实现
   - 有效的特征模仿策略
   - 适合CT图像的对比损失设计

3. **可视化和评估工具**：
   - 训练过程监控
   - 结果质量评估
   - 不同方法的比较分析

4. **文档和示例**：
   - 详细的方法说明
   - 使用教程
   - 示例结果展示
