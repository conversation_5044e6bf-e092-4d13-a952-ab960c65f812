#!/usr/bin/env python3
"""
Error handling utilities for training processes
Provides clean error logging and tensor information filtering
"""

import torch
import numpy as np
import traceback
import re
from typing import Any, Optional

class TrainingErrorHandler:
    """处理训练过程中的错误和异常"""
    
    def __init__(self, max_error_length: int = 200, max_tensor_elements: int = 10):
        """
        初始化错误处理器
        
        Args:
            max_error_length: 错误信息的最大长度
            max_tensor_elements: tensor显示的最大元素数量
        """
        self.max_error_length = max_error_length
        self.max_tensor_elements = max_tensor_elements
        self.error_count = {}  # 跟踪错误出现次数
        
    def clean_error_message(self, error: Exception) -> str:
        """
        清理错误信息，移除冗长的tensor数据
        
        Args:
            error: 异常对象
            
        Returns:
            清理后的错误信息字符串
        """
        error_str = str(error)
        
        # 移除tensor数据的正则表达式
        tensor_patterns = [
            r'tensor\([^)]*\)',  # 匹配 tensor(...) 
            r'\[\[\[.*?\]\]\]',  # 匹配多维数组
            r'\d+\.\d+e[+-]\d+',  # 匹配科学计数法（如果太多的话）
        ]
        
        for pattern in tensor_patterns:
            # 如果找到tensor数据，替换为简化信息
            if re.search(pattern, error_str):
                error_str = re.sub(pattern, '[tensor数据已省略]', error_str)
        
        # 截断过长的错误信息
        if len(error_str) > self.max_error_length:
            error_str = error_str[:self.max_error_length] + "... [错误信息已截断]"
            
        return error_str
    
    def format_tensor_info(self, tensor: torch.Tensor) -> str:
        """
        格式化tensor信息，只显示关键信息
        
        Args:
            tensor: PyTorch tensor
            
        Returns:
            格式化的tensor信息字符串
        """
        if not isinstance(tensor, torch.Tensor):
            return f"非tensor类型: {type(tensor)}"
        
        info = f"shape={tensor.shape}, dtype={tensor.dtype}"
        
        if tensor.device.type != 'cpu':
            info += f", device={tensor.device}"
        
        # 只显示统计信息，不显示具体数值
        if tensor.numel() > 0:
            try:
                info += f", min={tensor.min().item():.4f}, max={tensor.max().item():.4f}"
                if tensor.dtype.is_floating_point:
                    info += f", mean={tensor.mean().item():.4f}"
            except:
                info += ", [统计信息计算失败]"
        
        return info
    
    def log_data_loading_error(self, file_path: str, index: int, error: Exception) -> str:
        """
        记录数据加载错误
        
        Args:
            file_path: 文件路径
            index: 数据索引
            error: 异常对象
            
        Returns:
            格式化的错误日志
        """
        error_key = f"{type(error).__name__}:{str(error)[:50]}"
        self.error_count[error_key] = self.error_count.get(error_key, 0) + 1
        
        clean_msg = self.clean_error_message(error)
        
        # 如果同样的错误出现多次，减少日志输出
        if self.error_count[error_key] <= 3:
            log_msg = f"数据加载错误 [{file_path}] (索引 {index}): {clean_msg}"
        elif self.error_count[error_key] == 4:
            log_msg = f"数据加载错误 (类似错误已出现{self.error_count[error_key]}次，后续将减少日志): {clean_msg}"
        else:
            # 每10次记录一次
            if self.error_count[error_key] % 10 == 0:
                log_msg = f"数据加载错误 (累计{self.error_count[error_key]}次): {clean_msg}"
            else:
                return ""  # 不输出日志
        
        return log_msg
    
    def log_training_error(self, step: int, epoch: int, error: Exception, 
                          context: Optional[str] = None) -> str:
        """
        记录训练过程中的错误
        
        Args:
            step: 训练步数
            epoch: 训练轮次
            error: 异常对象
            context: 额外的上下文信息
            
        Returns:
            格式化的错误日志
        """
        clean_msg = self.clean_error_message(error)
        
        log_msg = f"训练错误 [Epoch {epoch}, Step {step}]: {clean_msg}"
        if context:
            log_msg += f" | 上下文: {context}"
        
        return log_msg
    
    def safe_tensor_operation(self, operation, *args, **kwargs):
        """
        安全执行tensor操作，捕获并清理错误信息
        
        Args:
            operation: 要执行的操作函数
            *args: 位置参数
            **kwargs: 关键字参数
            
        Returns:
            操作结果或None（如果出错）
        """
        try:
            return operation(*args, **kwargs)
        except Exception as e:
            clean_msg = self.clean_error_message(e)
            print(f"Tensor操作错误: {clean_msg}")
            return None
    
    def get_error_summary(self) -> str:
        """
        获取错误统计摘要
        
        Returns:
            错误统计信息字符串
        """
        if not self.error_count:
            return "无错误记录"
        
        summary = "错误统计摘要:\n"
        for error_key, count in sorted(self.error_count.items(), 
                                     key=lambda x: x[1], reverse=True):
            summary += f"  - {error_key}: {count}次\n"
        
        return summary

# 全局错误处理器实例
global_error_handler = TrainingErrorHandler()

def clean_error_log(error: Exception, max_length: int = 200) -> str:
    """
    便捷函数：清理错误日志
    
    Args:
        error: 异常对象
        max_length: 最大长度
        
    Returns:
        清理后的错误信息
    """
    return global_error_handler.clean_error_message(error)

def log_data_error(file_path: str, index: int, error: Exception) -> None:
    """
    便捷函数：记录数据加载错误
    
    Args:
        file_path: 文件路径
        index: 数据索引
        error: 异常对象
    """
    log_msg = global_error_handler.log_data_loading_error(file_path, index, error)
    if log_msg:
        print(log_msg)

def format_tensor_info(tensor: torch.Tensor) -> str:
    """
    便捷函数：格式化tensor信息
    
    Args:
        tensor: PyTorch tensor
        
    Returns:
        格式化的tensor信息
    """
    return global_error_handler.format_tensor_info(tensor)

def print_error_summary() -> None:
    """便捷函数：打印错误统计摘要"""
    print(global_error_handler.get_error_summary())
