#!/usr/bin/env python3
"""
降质方法性能分析工具（简化版）
用于精确定位不同降质方法的性能瓶颈
"""

import torch
import time
import numpy as np
from data.sr_dataloader import SRDataset
import yaml
from tqdm import tqdm

def load_config(config_path):
    """加载配置文件"""
    with open(config_path, 'r', encoding='utf-8') as f:
        return yaml.safe_load(f)

def profile_degradation_method(dataset, method_name, num_samples=20):
    """分析特定降质方法的性能（简化版）"""
    print(f"\n🔍 分析降质方法: {method_name}")
    print("=" * 60)

    # 性能指标
    times = []

    # 临时修改降质方法
    original_method = dataset.data_config.get('degradation_method', 'simple')
    dataset.data_config['degradation_method'] = method_name

    try:
        for i in tqdm(range(num_samples), desc=f"测试 {method_name}"):
            # 记录开始时间
            start_time = time.time()

            # 执行降质操作
            try:
                _ = dataset[i % len(dataset)]  # 只关心时间，不存储结果

                # 记录结束时间
                end_time = time.time()
                times.append(end_time - start_time)

            except Exception as e:
                print(f"❌ 样本 {i} 处理失败: {e}")
                continue

    finally:
        # 恢复原始降质方法
        dataset.data_config['degradation_method'] = original_method

    # 计算统计信息
    if times:
        avg_time = np.mean(times)
        std_time = np.std(times)
        max_time = np.max(times)
        min_time = np.min(times)

        print(f"📊 性能统计:")
        print(f"   平均时间: {avg_time:.4f}s (±{std_time:.4f}s)")
        print(f"   时间范围: {min_time:.4f}s - {max_time:.4f}s")
        print(f"   预估批次时间 (batch_size=8): {avg_time * 8:.2f}s")

        return {
            'method': method_name,
            'avg_time': avg_time,
            'std_time': std_time,
            'max_time': max_time,
            'min_time': min_time,
            'batch_time_estimate': avg_time * 8
        }
    else:
        print("❌ 没有成功的样本")
        return None

def profile_specific_operations(dataset):
    """分析特定操作的性能"""
    print(f"\n🔬 分析特定操作性能")
    print("=" * 60)

    # 测试各向异性高斯核生成
    print("测试各向异性高斯核生成...")
    start_time = time.time()
    for _ in range(100):
        _ = dataset._create_anisotropic_gaussian_kernel(21, 2.0, 1.5, 0.5)
    kernel_time = (time.time() - start_time) / 100
    print(f"   各向异性高斯核生成: {kernel_time:.6f}s/次")

    # 测试泊松噪声
    print("测试泊松噪声生成...")
    test_tensor = torch.rand(1, 1, 128, 128)
    start_time = time.time()
    for _ in range(10):
        scaled = test_tensor * 100
        noisy = torch.poisson(scaled)
        _ = noisy / 100
    poisson_time = (time.time() - start_time) / 10
    print(f"   泊松噪声生成: {poisson_time:.6f}s/次")

    # 测试高斯噪声（对比）
    print("测试高斯噪声生成...")
    start_time = time.time()
    for _ in range(100):
        noise = torch.randn_like(test_tensor) * 0.05
        _ = test_tensor + noise
    gaussian_time = (time.time() - start_time) / 100
    print(f"   高斯噪声生成: {gaussian_time:.6f}s/次")

    print(f"\n📈 性能对比:")
    print(f"   泊松噪声 vs 高斯噪声: {poisson_time/gaussian_time:.1f}x 慢")
    print(f"   各向异性核 vs 高斯噪声: {kernel_time/gaussian_time:.1f}x 慢")

def main():
    """主函数"""
    print("🚀 降质方法性能分析工具")
    print("=" * 60)

    # 加载配置
    config_paths = [
        'configs/config_ultra_no_gan.yaml',
        'configs/config_accurate_bsrgan.yaml',
        'configs/config_sandstone_ct.yaml'
    ]

    results = []

    for config_path in config_paths:
        try:
            print(f"\n📁 加载配置: {config_path}")
            config = load_config(config_path)

            # 创建数据集
            dataset = SRDataset(config=config)

            print(f"   数据集大小: {len(dataset)} 样本")

            # 测试不同降质方法
            methods_to_test = [
                'simple',
                'fast_bsrgan',
                'accurate_bsrgan',
                'sandstone_ct'
            ]

            for method in methods_to_test:
                try:
                    result = profile_degradation_method(dataset, method, num_samples=20)
                    if result:
                        results.append(result)
                except Exception as e:
                    print(f"❌ 测试方法 {method} 失败: {e}")

            # 分析特定操作（只需要一次）
            if config_path == config_paths[0]:
                profile_specific_operations(dataset)

            break  # 只需要一个配置文件即可

        except Exception as e:
            print(f"❌ 配置文件 {config_path} 加载失败: {e}")
            continue

    # 生成性能对比报告
    if results:
        print(f"\n📊 性能对比报告")
        print("=" * 70)
        print(f"{'方法':<20} {'平均时间':<12} {'批次时间':<12}")
        print("-" * 70)

        for result in sorted(results, key=lambda x: x['avg_time']):
            print(f"{result['method']:<20} "
                  f"{result['avg_time']:.4f}s     "
                  f"{result['batch_time_estimate']:.2f}s")

        # 找出最慢的方法
        slowest = max(results, key=lambda x: x['avg_time'])
        fastest = min(results, key=lambda x: x['avg_time'])

        print(f"\n🏆 性能总结:")
        print(f"   最快方法: {fastest['method']} ({fastest['avg_time']:.4f}s)")
        print(f"   最慢方法: {slowest['method']} ({slowest['avg_time']:.4f}s)")
        print(f"   性能差异: {slowest['avg_time']/fastest['avg_time']:.1f}x")

if __name__ == "__main__":
    main()
