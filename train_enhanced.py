import torch
import torch.nn as nn
import torch.nn.functional as F # Ensure this import is present
import torch.optim as optim
from torch.optim.lr_scheduler import CosineAnnealingLR
from data.ct_loader import get_enhanced_data_loader, get_self_supervised_loader
from models.enhanced_sr_network import EnhancedSRNetwork
from modules.losses import CompositeLoss
from modules.frequency_attention import FrequencyDomainLoss
from tqdm import tqdm
import os
import numpy as np
import matplotlib.pyplot as plt
from torch.utils.tensorboard import SummaryWriter
import math  # 添加math模块导入

def save_checkpoint(state_dict, is_best, checkpoint_dir, epoch=None, save_interval=10):
    """通用的模型保存函数"""
    if epoch is not None and (epoch + 1) % save_interval == 0:
        torch.save(state_dict, f"{checkpoint_dir}/checkpoint_epoch{epoch+1}.pth")
    
    if is_best:
        torch.save(state_dict, f"{checkpoint_dir}/best_model.pth")
        print(f"Saved best model with loss: {state_dict['loss']:.6f}")

def setup_training(model_params, optimizer_params, device):
    """设置优化器和学习率调度器"""
    optimizer = optim.AdamW(model_params, lr=optimizer_params['lr'], weight_decay=1e-4)
    
    # 使用带warmup的余弦退火学习率调度器
    from modules.scheduler import WarmupCosineScheduler
    warmup_epochs = optimizer_params.get('warmup_epochs', 5)  # 默认预热5个epoch
    scheduler = WarmupCosineScheduler(
        optimizer, 
        warmup_epochs=warmup_epochs,
        total_epochs=optimizer_params['epochs'],
        min_lr=optimizer_params['lr']/100,
        warmup_start_lr=optimizer_params['lr']/10
    )
    scaler = torch.cuda.amp.GradScaler(enabled=optimizer_params.get('use_amp', True))
    return optimizer, scheduler, scaler

def train_epoch(model, train_loader, optimizer, scaler, criterion, device, writer, epoch, gradient_accumulation_steps=1):
    """通用的训练循环函数"""
    model.train()
    total_loss = 0
    num_batches = len(train_loader)
    
    with tqdm(train_loader, unit="batch") as pbar:
        for batch_idx, data in enumerate(pbar):
            global_step = epoch * num_batches + batch_idx
            
            # 处理自监督训练的双视图输入
            if isinstance(data, (list, tuple)) and len(data) == 2:
                view1, view2 = data
                view1 = view1.to(device)
                view2 = view2.to(device)
            
            with torch.cuda.amp.autocast(enabled=scaler is not None):
                # 对于自监督训练，模型输出投影特征
                proj1, proj2 = model(view1, view2)
                # 直接使用传入的 criterion (应为 contrastive_loss) 计算损失
                loss = criterion(proj1.view(proj1.size(0), -1), proj2.view(proj2.size(0), -1))
                loss = loss / gradient_accumulation_steps
            
            scaler.scale(loss).backward()
            
            if (batch_idx + 1) % gradient_accumulation_steps == 0:
                # 计算梯度范数
                total_norm = 0.0
                for p in model.parameters():
                    if p.grad is not None:
                        param_norm = p.grad.detach().data.norm(2)
                        total_norm += param_norm.item() ** 2
                total_norm = total_norm ** 0.5
                
                scaler.step(optimizer)
                scaler.update()
                optimizer.zero_grad(set_to_none=True)
                
                # 记录梯度范数
                writer.add_scalar('Gradients/norm', total_norm, global_step)
            
            current_loss = loss.item() * gradient_accumulation_steps
            total_loss += current_loss
            pbar.set_postfix(loss=current_loss, grad_accum_steps=gradient_accumulation_steps)
            
            # 记录每个batch的损失和学习率
            writer.add_scalar('Loss/batch', current_loss, global_step)
            writer.add_scalar('LR', optimizer.param_groups[0]['lr'], global_step)
    
    return total_loss / len(train_loader)

def train_enhanced_sr(lr_dir='E:/vscode/2号CT数据', hr_dir='E:/vscode/3号CT数据', batch_size=8, num_workers=2, # Updated paths
                     epochs=150, lr=1e-4, temporal_window=5, patch_size=128,
                     pretrained_encoder_path=None, checkpoint_dir='checkpoints/enhanced',
                     log_dir='logs/enhanced', gradient_accumulation_steps=4, use_amp=True,
                     device=None, save_interval=10, resume=None, warmup_epochs=5):
    """增强型超分辨率网络训练函数"""
    device = torch.device('cuda' if torch.cuda.is_available() else 'cpu')
    print(f"Using device: {device}")
    
    os.makedirs(checkpoint_dir, exist_ok=True)
    os.makedirs(log_dir, exist_ok=True)
    writer = SummaryWriter(log_dir)
    
    model = EnhancedSRNetwork(
        temporal_size=temporal_window,
        pretrained_encoder_path=pretrained_encoder_path,
        depth=8,
        dim=64
    ).to(device)
    
    print(f"Total parameters: {sum(p.numel() for p in model.parameters()):,}")
    
    train_loader = get_enhanced_data_loader(
        lr_dir=lr_dir,
        hr_dir=hr_dir,
        batch_size=batch_size,
        num_workers=num_workers,
        temporal_window=temporal_window,
        patch_size=32
    )
    
    # 初始化损失函数时启用体素大小权重
    criterion = lambda sr, hr: CompositeLoss(voxel_weight=True)(sr, hr) + 0.1 * FrequencyDomainLoss()(sr, hr)
    
    optimizer_params = {'lr': lr, 'epochs': epochs, 'use_amp': use_amp, 'warmup_epochs': warmup_epochs}
    optimizer, scheduler, scaler = setup_training(model.parameters(), optimizer_params, device)
    
    best_loss = float('inf')
    for epoch in range(epochs):
        pbar = tqdm(train_loader, desc=f"Epoch {epoch+1}/{epochs}")
        avg_loss = train_epoch(model, train_loader, optimizer, scaler, criterion, device, writer, epoch, gradient_accumulation_steps)
        
        scheduler.step()
        print(f"Epoch {epoch+1}/{epochs}, Average Loss: {avg_loss:.6f}")
        
        # 仅在每个epoch结束时记录一次
        writer.add_scalar('Loss/train', avg_loss, epoch)
        writer.add_scalar('LR', scheduler.get_last_lr()[0], epoch)
        
        checkpoint = {
            'epoch': epoch + 1,
            'model_state_dict': model.state_dict(),
            'optimizer_state_dict': optimizer.state_dict(),
            'scheduler_state_dict': scheduler.state_dict(),
            'loss': avg_loss,
        }
        
        save_checkpoint(
            checkpoint,
            avg_loss < best_loss,
            checkpoint_dir,
            epoch,
            save_interval
        )
        
        if avg_loss < best_loss:
            best_loss = avg_loss
    
    torch.save(checkpoint, f"{checkpoint_dir}/final_model.pth")
    writer.close()
    print("Training completed!")

def pretrain_self_supervised(data_dir='E:/vscode/2号CT数据', batch_size=6, num_workers=4, # Updated path
                         epochs=50, lr=1e-4, patch_size=32, device=None,
                         save_interval=5, resume=None, use_amp=True,
                         use_mem_efficient=True, use_xformers=True,
                         checkpoint_dir='checkpoints/pretrain',
                          log_dir='logs/pretrain', warmup_epochs=5,
                          gradient_accumulation_steps=16, model_dim=64, model_depth=4,
                          temperature=0.1, patch_sizes=[32, 64, 128]): # 添加 patch_sizes 参数
    """自监督预训练函数，用于提取通用特征 (多尺度版本)"""
    device = torch.device('cuda' if torch.cuda.is_available() else 'cpu')
    print(f"Using device: {device}")
    # 更新打印信息以反映多尺度
    print(f"预训练参数: epochs={epochs}, batch_size={batch_size}, patch_sizes={patch_sizes}")
    print(f"梯度累积步数: {gradient_accumulation_steps}")
    print(f"模型维度: {model_dim}, 模型深度: {model_depth}") # model_dim 仍用于 TransformerEncoder
    
    os.makedirs(checkpoint_dir, exist_ok=True)
    os.makedirs(log_dir, exist_ok=True)
    writer = SummaryWriter(log_dir)
    
    from models.art_transformer import TransformerEncoder
    from modules.attention import MultiScaleCrossAttention, DualAttention # 导入新模块
    
    # 定义自监督预训练模型 (多尺度版本)
    class SelfSupervisedModel(nn.Module):
        def __init__(self, patch_sizes=[32, 64, 128], encoder_dim=64, encoder_depth=4, num_heads=8):
            super().__init__()
            self.patch_sizes = sorted(patch_sizes) # 确保排序
            self.encoder_dim = encoder_dim # Transformer 输出维度

            # 共享的初始特征提取器 (3D Conv)
            # 输入是 [B, 1, 1, H, W] (C=1, D=1)
            self.feature_extractor_conv = nn.Sequential(
                nn.Conv3d(1, encoder_dim // 2, kernel_size=(1, 3, 3), padding=(0, 1, 1), bias=False),
                nn.BatchNorm3d(encoder_dim // 2),
                nn.ReLU(inplace=True),
                nn.Conv3d(encoder_dim // 2, encoder_dim, kernel_size=(1, 3, 3), padding=(0, 1, 1), bias=False),
                nn.BatchNorm3d(encoder_dim),
                nn.ReLU(inplace=True)
            )
            # 共享的初始注意力 (在3D特征上操作)
            self.initial_attention = DualAttention(channels=encoder_dim, reduction=encoder_dim // 4)

            # 共享的Transformer编码器 (在2D特征上操作)
            self.encoder = TransformerEncoder(
                dim=encoder_dim,
                depth=encoder_depth,
                window_size=8, # 窗口大小可能需要根据最小 patch 调整
                num_heads=num_heads
            )
            # 共享的残差连接卷积 (在2D特征上操作)
            self.residual_conv = nn.Conv2d(encoder_dim, encoder_dim, kernel_size=1)

            # 跨尺度注意力模块
            self.multi_scale_attention = MultiScaleCrossAttention(
                channels=encoder_dim,
                num_scales=len(patch_sizes),
                reference_scale_index=0 # 使用最小尺度的特征图作为参考尺寸
            )

            # 投影头 (输入来自 multi_scale_attention)
            self.projection_head = nn.Sequential(
                nn.Conv2d(encoder_dim, 128, 3, padding=1),
                nn.BatchNorm2d(128),
                nn.ReLU(inplace=True),
                nn.Conv2d(128, 256, 3, padding=1),
                nn.BatchNorm2d(256),
                nn.ReLU(inplace=True),
                nn.Conv2d(256, 128, 1) # 输出128维特征用于对比损失
            )

        def process_view(self, view_dict):
            """处理单个视图字典 (包含所有尺度)"""
            import torch.nn.functional as F # 在方法内部导入 F
            encoded_features = {}
            initial_features_2d = {} # Store features before encoder for residual connection

            for size in self.patch_sizes:
                patch_tensor = view_dict[size] # [B, 1, 1, H, W]
                if patch_tensor.dim() != 5 or patch_tensor.shape[1:3] != (1, 1):
                     raise ValueError(f"Incorrect input shape for size {size}: {patch_tensor.shape}. Expected [B, 1, 1, H, W]")

                # 1. 初始特征提取 (3D Conv)
                feat_3d = self.feature_extractor_conv(patch_tensor) # [B, C, 1, H, W]

                # 2. 压缩深度维度 -> 2D 特征
                feat_2d_pre_attn = feat_3d.squeeze(2) # [B, C, H, W]

                # 3. 应用初始注意力 (在 2D 特征上)
                feat_2d = self.initial_attention(feat_2d_pre_attn) # [B, C, H, W]
                initial_features_2d[size] = feat_2d # Store features *after* initial attention for residual

                # 4. Transformer 编码 (2D)
                encoded_2d = self.encoder(feat_2d) # [B, C, H, W]

                # 5. 残差连接 (2D) - 使用注意力后的特征作为残差输入
                encoded_2d = encoded_2d + self.residual_conv(feat_2d)

                encoded_features[size] = encoded_2d

            # 6. 跨尺度融合
            fused_features = self.multi_scale_attention(encoded_features) # [B, C, H_ref, W_ref]

            # 7. 投影
            projection = self.projection_head(fused_features) # [B, proj_dim, H_ref, W_ref]

            # 8. Global Average Pooling for contrastive loss
            pooled_projection = F.adaptive_avg_pool2d(projection, (1, 1)).squeeze() # [B, proj_dim]

            return pooled_projection


        def forward(self, batch_data: dict):
            """
            Args:
                batch_data (dict): 包含多尺度视图的字典, 结构如:
                                   {32: (view1_batch, view2_batch),
                                    64: (view1_batch, view2_batch), ...}
            Returns:
                proj1, proj2: 全局池化后的投影特征 [B, proj_dim]
            """
            # 从批次数据中分离 view1 和 view2 的字典
            view1_dict = {size: batch_data[size][0] for size in self.patch_sizes}
            view2_dict = {size: batch_data[size][1] for size in self.patch_sizes}

            # 分别处理两个视图
            proj1 = self.process_view(view1_dict)
            proj2 = self.process_view(view2_dict)

            return proj1, proj2

    # 实例化模型时传递参数
    model = SelfSupervisedModel(
        patch_sizes=patch_sizes,
        encoder_dim=model_dim, # 使用 model_dim 作为 Transformer 维度
        encoder_depth=model_depth
    ).to(device)
    
    # 更新数据加载器调用以使用 patch_sizes
    train_loader = get_self_supervised_loader(
        data_dir=data_dir,
        batch_size=batch_size,
        num_workers=num_workers,
        patch_sizes=patch_sizes, # 传递 patch_sizes 列表
        # model_dim 参数不再需要传递给 loader
    )
    
    # 对比损失函数 (保持不变)
    def contrastive_loss(q, k, temp):
        q = nn.functional.normalize(q, dim=1)
        k = nn.functional.normalize(k, dim=1)
        # Ensure q and k are 2D: [B, FeatureDim]
        if q.dim() > 2: q = q.squeeze()
        if k.dim() > 2: k = k.squeeze()
        if q.dim() == 1: q = q.unsqueeze(0) # Handle batch size 1 case
        if k.dim() == 1: k = k.unsqueeze(0)

        logits = torch.mm(q, k.transpose(0, 1)) / temp
        labels = torch.arange(logits.shape[0], device=device)
        return nn.CrossEntropyLoss()(logits, labels)

    # 更新 train_epoch 函数以处理字典输入
    def train_epoch_multi_scale(model, train_loader, optimizer, scaler, criterion, device, writer, epoch, gradient_accumulation_steps=1):
        """训练循环函数 (多尺度版本)"""
        model.train()
        total_loss = 0
        num_batches = len(train_loader)

        with tqdm(train_loader, unit="batch") as pbar:
            for batch_idx, batch_data in enumerate(pbar):
                if batch_data is None: # Skip if collate_fn returned None
                    print("Skipping empty batch.")
                    continue

                global_step = epoch * num_batches + batch_idx

                # 将字典中所有张量移动到设备
                batch_data_device = {}
                try:
                    for size, (v1, v2) in batch_data.items():
                        batch_data_device[size] = (v1.to(device, non_blocking=True), v2.to(device, non_blocking=True))
                except AttributeError as e:
                     print(f"Error processing batch data: {e}. Batch data structure: {batch_data}")
                     continue # Skip this batch

                optimizer.zero_grad(set_to_none=True) # Zero grad before forward pass

                with torch.cuda.amp.autocast(enabled=scaler is not None):
                    # 模型直接接收字典
                    proj1, proj2 = model(batch_data_device) # [B, proj_dim]
                    # 使用 criterion (contrastive_loss) 计算损失
                    loss = criterion(proj1, proj2) # proj1/proj2 已经是 [B, proj_dim]
                    loss = loss / gradient_accumulation_steps

                scaler.scale(loss).backward()

                if (batch_idx + 1) % gradient_accumulation_steps == 0 or (batch_idx + 1) == num_batches:
                    # Clip gradients before optimizer step
                    # scaler.unscale_(optimizer) # Unscale gradients before clipping
                    # torch.nn.utils.clip_grad_norm_(model.parameters(), max_norm=1.0)

                    scaler.step(optimizer)
                    scaler.update()
                    optimizer.zero_grad(set_to_none=True) # Zero grad after step

                current_loss = loss.item() * gradient_accumulation_steps
                total_loss += current_loss
                pbar.set_postfix(loss=f"{current_loss:.4f}", lr=f"{optimizer.param_groups[0]['lr']:.1e}")

                # 记录每个batch的损失和学习率
                writer.add_scalar('Loss/batch', current_loss, global_step)
                writer.add_scalar('LR', optimizer.param_groups[0]['lr'], global_step)

        avg_loss = total_loss / num_batches if num_batches > 0 else 0
        return avg_loss


    params = model.parameters()
    optimizer_params = {'lr': lr, 'epochs': epochs, 'use_amp': use_amp, 'warmup_epochs': warmup_epochs}
    optimizer, scheduler, scaler = setup_training(params, optimizer_params, device)

    best_loss = float('inf')
    for epoch in range(epochs):
        print(f"\nEpoch {epoch+1}/{epochs}:")
        model.train()

        # 使用新的多尺度训练循环
        avg_loss = train_epoch_multi_scale(
            model,
            train_loader,
            optimizer,
            scaler,
            lambda q, k: contrastive_loss(q, k, temp=temperature), # 传递对比损失函数
            device,
            writer,
            epoch,
            gradient_accumulation_steps=gradient_accumulation_steps
        )

        scheduler.step() # 更新学习率
        print(f"Epoch {epoch+1}/{epochs} completed. Average Loss: {avg_loss:.6f}, LR: {scheduler.get_last_lr()[0]:.6f}")

        writer.add_scalar('Loss/train_epoch', avg_loss, epoch)
        writer.add_scalar('LR_epoch', scheduler.get_last_lr()[0], epoch)

        # 保存检查点逻辑保持不变
        checkpoint = {
            'epoch': epoch + 1,
            'model_state_dict': model.state_dict(), # 保存整个模型状态
            'optimizer_state_dict': optimizer.state_dict(),
            'scheduler_state_dict': scheduler.state_dict(),
            'epoch': epoch + 1,
            'loss': avg_loss,
        }
        
        save_checkpoint(
            checkpoint,
            avg_loss < best_loss,
            checkpoint_dir,
            epoch,
            save_interval
        )
        
        if avg_loss < best_loss:
            best_loss = avg_loss
    
    torch.save(checkpoint, f"{checkpoint_dir}/final_model.pth")
    writer.close()
    print("Training completed!")
