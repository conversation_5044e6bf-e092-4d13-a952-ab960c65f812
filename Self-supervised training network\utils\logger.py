# -*- coding: utf-8 -*-
"""
Logging utility, primarily for TensorBoard.
"""

import os
from torch.utils.tensorboard import SummaryWriter
# Add other logging library imports if needed (e.g., logging module)

class TensorBoardLogger:
    """Logs training metrics and potentially images to TensorBoard."""
    def __init__(self, log_dir):
        print(f"Initializing TensorBoard Logger at: {log_dir}")
        os.makedirs(log_dir, exist_ok=True)
        self.writer = SummaryWriter(log_dir=log_dir)

    def log_scalar(self, tag, scalar_value, global_step):
        """Logs a scalar value."""
        self.writer.add_scalar(tag, scalar_value, global_step)

    def log_image(self, tag, image_tensor, global_step):
        """Logs a single image."""
        # Ensure image tensor is suitable for add_image (e.g., CHW or HWC)
        # May need normalization or range adjustment
        # TODO: Add image preprocessing if necessary
        self.writer.add_image(tag, image_tensor, global_step)

    def log_images(self, tag, images_tensor, global_step):
        """Logs a batch of images as a grid."""
        # Ensure images tensor is suitable (e.g., NCHW)
        # May need normalization or range adjustment
        # TODO: Add image preprocessing if necessary
        # grid = torchvision.utils.make_grid(images_tensor)
        # self.writer.add_image(tag, grid, global_step)
        pass # Placeholder

    def log_hparams(self, hparams_dict, metrics_dict):
        """Logs hyperparameters and final metrics."""
        self.writer.add_hparams(hparams_dict, metrics_dict)

    def close(self):
        """Closes the TensorBoard writer."""
        self.writer.close()

# Example usage (for testing)
if __name__ == '__main__':
    dummy_log_dir = './dummy_logs'
    logger = TensorBoardLogger(dummy_log_dir)
    print("Logging dummy data...")
    logger.log_scalar('test/loss', 0.5, 0)
    logger.log_scalar('test/loss', 0.4, 1)
    # logger.log_image('test/image', torch.rand(3, 64, 64), 0) # Example image log
    logger.close()
    print(f"Dummy logs written to {dummy_log_dir}. Check with TensorBoard.")
    # Clean up
    # import shutil
    # shutil.rmtree(dummy_log_dir)
