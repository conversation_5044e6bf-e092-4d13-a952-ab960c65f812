import torch
import torch.nn as nn
import torch.nn.functional as F
import numpy as np

class EnhancedFrequencyChannelAttention(nn.Module):
    """增强型频域通道注意力模块，专为CT图像超分辨率设计"""
    def __init__(self, channels, reduction=16, freq_bands=4):
        super().__init__()
        self.channels = channels
        self.freq_bands = freq_bands
        
        # 通道注意力
        self.avg_pool = nn.AdaptiveAvgPool2d(1)
        self.max_pool = nn.AdaptiveMaxPool2d(1)
        
        self.fc = nn.Sequential(
            nn.Conv2d(channels, channels // reduction, 1, bias=False),
            nn.ReLU(inplace=True),
            nn.Conv2d(channels // reduction, channels, 1, bias=False)
        )
        
        # 频带选择性注意力
        self.freq_attention = nn.ModuleList([
            nn.Sequential(
                nn.Conv2d(channels, channels // reduction, 1, bias=False),
                nn.ReLU(inplace=True),
                nn.Conv2d(channels // reduction, channels, 1, bias=False),
                nn.Sigmoid()
            ) for _ in range(freq_bands)
        ])
        
        # 频域滤波器参数
        self.alpha = nn.Parameter(torch.ones(1, channels, 1, 1) * 0.5)
        self.beta = nn.Parameter(torch.ones(1, channels, 1, 1) * 0.5)
        
        self.sigmoid = nn.Sigmoid()
        
    def create_band_mask(self, h, w, band_idx):
        """创建频带掩码，将频域空间分为多个环形区域"""
        center_h, center_w = h // 2, w // 2
        y_grid, x_grid = torch.meshgrid(torch.arange(h), torch.arange(w))
        y_grid, x_grid = y_grid.to(self.alpha.device), x_grid.to(self.alpha.device)
        
        # 计算到中心的距离
        dist = torch.sqrt((y_grid - center_h)**2 + (x_grid - center_w)**2)
        
        # 归一化距离
        dist = dist / torch.max(dist)
        
        # 创建频带掩码
        band_width = 1.0 / self.freq_bands
        low_thresh = band_idx * band_width
        high_thresh = (band_idx + 1) * band_width
        
        mask = ((dist >= low_thresh) & (dist < high_thresh)).float()
        return mask
    
    def forward(self, x):
        b, c, h, w = x.shape
        
        # 傅里叶变换
        x_freq = torch.fft.rfft2(x)
        x_freq_abs = torch.abs(x_freq)
        x_freq_angle = torch.angle(x_freq)
        
        # 分频带处理
        enhanced_freq = torch.zeros_like(x_freq)
        
        for i in range(self.freq_bands):
            # 创建频带掩码
            mask = self.create_band_mask(h, w // 2 + 1, i)  # rfft2结果的宽度是w//2+1
            mask = mask.unsqueeze(0).unsqueeze(0)  # 添加批次和通道维度
            
            # 提取频带特征
            band_freq = x_freq * mask
            
            # 转回空域进行注意力处理
            band_spatial = torch.fft.irfft2(band_freq, s=(h, w))
            
            # 应用频带特定的注意力
            band_attn = self.freq_attention[i](band_spatial)
            
            # 增强后的频带
            enhanced_band_spatial = band_spatial * band_attn
            enhanced_band_freq = torch.fft.rfft2(enhanced_band_spatial)
            
            # 累加增强后的频带
            enhanced_freq = enhanced_freq + enhanced_band_freq
        
        # 通道注意力
        spatial_feat = torch.fft.irfft2(x_freq, s=(h, w))
        avg_out = self.fc(self.avg_pool(spatial_feat))
        max_out = self.fc(self.max_pool(spatial_feat))
        channel_attn = self.sigmoid(avg_out + max_out)
        
        # 频域和空域特征融合
        x_enhanced = torch.fft.irfft2(enhanced_freq, s=(h, w))
        x_enhanced = x_enhanced * channel_attn
        
        # 残差连接
        return x + self.alpha * x_enhanced

class FrequencyDomainLoss(nn.Module):
    """频域损失函数，关注频域特征的差异"""
    def __init__(self, alpha=1.0, beta=0.1, voxel_size=0.03889):  # 默认使用旧路径 '2号CT数据' 的体素大小
        super().__init__()
        self.voxel_size = voxel_size
        # 根据体素大小动态调整权重
        self.alpha = alpha * (0.00956 / voxel_size)  # 幅度损失权重
        self.beta = beta * (0.00956 / voxel_size)    # 相位损失权重
        
    def forward(self, pred, target):
        # 傅里叶变换
        pred_freq = torch.fft.rfft2(pred)
        target_freq = torch.fft.rfft2(target)
        
        # 计算幅度和相位
        pred_mag = torch.abs(pred_freq)
        target_mag = torch.abs(target_freq)
        pred_phase = torch.angle(pred_freq)
        target_phase = torch.angle(target_freq)
        
        # 计算幅度损失（对数域）
        eps = 1e-8
        mag_loss = F.l1_loss(torch.log(pred_mag + eps), torch.log(target_mag + eps))
        
        # 计算相位损失（周期性考虑）
        phase_diff = torch.abs(pred_phase - target_phase)
        phase_diff = torch.min(phase_diff, 2 * np.pi - phase_diff)
        phase_loss = phase_diff.mean()
        
        # 加权组合
        return self.alpha * mag_loss + self.beta * phase_loss

class WaveletAttention(nn.Module):
    """小波注意力模块，用于多尺度特征提取"""
    def __init__(self, channels, reduction=16):
        super().__init__()
        self.channels = channels
        
        # 小波分解卷积
        self.wavelet_conv_ll = nn.Conv2d(channels, channels, kernel_size=3, padding=1, bias=False)
        self.wavelet_conv_lh = nn.Conv2d(channels, channels, kernel_size=3, padding=1, bias=False)
        self.wavelet_conv_hl = nn.Conv2d(channels, channels, kernel_size=3, padding=1, bias=False)
        self.wavelet_conv_hh = nn.Conv2d(channels, channels, kernel_size=3, padding=1, bias=False)
        
        # 注意力机制
        self.attention = nn.Sequential(
            nn.Conv2d(channels*4, channels//reduction, kernel_size=1),
            nn.ReLU(inplace=True),
            nn.Conv2d(channels//reduction, channels*4, kernel_size=1),
            nn.Sigmoid()
        )
        
        # 融合卷积
        self.fusion = nn.Conv2d(channels*4, channels, kernel_size=1)
        
    def forward(self, x):
        # 小波分解（使用平均池化和差分模拟）
        # LL: 低频
        ll = F.avg_pool2d(x, kernel_size=2, stride=2)
        ll = F.interpolate(ll, scale_factor=2, mode='bilinear', align_corners=False)
        ll = self.wavelet_conv_ll(ll)
        
        # LH: 水平高频
        lh = x - ll
        lh = self.wavelet_conv_lh(lh)
        
        # HL: 垂直高频
        hl = x - F.avg_pool2d(x, kernel_size=(1, 2), stride=(1, 2))
        hl = F.interpolate(hl, scale_factor=(1, 2), mode='bilinear', align_corners=False)
        hl = self.wavelet_conv_hl(hl)
        
        # HH: 对角高频
        hh = x - ll - lh - hl
        hh = self.wavelet_conv_hh(hh)
        
        # 特征拼接
        wavelet_features = torch.cat([ll, lh, hl, hh], dim=1)
        
        # 注意力机制
        attention_weights = self.attention(wavelet_features)
        wavelet_features = wavelet_features * attention_weights
        
        # 特征融合
        output = self.fusion(wavelet_features)
        
        return output + x  # 残差连接
