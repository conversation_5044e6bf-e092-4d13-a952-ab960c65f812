import torch
import torch.nn as nn
import torch.nn.functional as F
from models.art_transformer import TransformerEncoder
from models.pseudo3d_net import Pseudo3DNet
from modules.advanced_integrated_modules import (
    AdvancedIntegratedAttention,
    EnhancedMultiScaleFrequencyBlock,
    AdvancedDeformableLKA,
    AdvancedIntegratedLoss,
    CrossScaleAttention
)
from modules.frequency_attention import WaveletAttention

class EnhancedResidualBlock(nn.Module):
    """增强型残差块，使用高级集成注意力机制"""
    def __init__(self, dim, kernel_size=3):
        super().__init__()
        self.conv1 = nn.Conv2d(dim, dim, kernel_size, padding=kernel_size//2)
        self.conv2 = nn.Conv2d(dim, dim, kernel_size, padding=kernel_size//2)
        
        # 高级集成注意力
        self.integrated_attn = AdvancedIntegratedAttention(dim)
        
        # 多尺度频域处理
        self.freq_block = EnhancedMultiScaleFrequencyBlock(dim)
        
        # 层归一化
        self.norm = nn.LayerNorm(dim)
        
    def forward(self, x):
        # 卷积分支
        x1 = x.permute(0, 2, 3, 1)  # B, H, W, C
        x1 = self.norm(x1)
        x1 = x1.permute(0, 3, 1, 2)  # B, C, H, W
        x1 = self.conv1(x1)
        x1 = F.gelu(x1)
        x1 = self.conv2(x1)
        
        # 高级注意力处理
        x2 = self.integrated_attn(x)
        
        # 多尺度频域处理
        x3 = self.freq_block(x)
        
        # 融合
        return x + x1 + x2 + x3

class DeformableLKABlock(nn.Module):
    """高级可变形大核注意力块"""
    def __init__(self, dim, kernel_size=7):
        super().__init__()
        # 使用高级可变形大核注意力
        self.advanced_lka = AdvancedDeformableLKA(dim, kernel_size)
        
    def forward(self, x):
        return self.advanced_lka(x)

class MultiScaleFeatureFusion(nn.Module):
    """多尺度特征融合模块"""
    def __init__(self, dim):
        super().__init__()
        self.cross_scale_attn = CrossScaleAttention(dim)
        self.wavelet_attn = WaveletAttention(dim)
        self.fusion_conv = nn.Conv2d(dim*2, dim, kernel_size=1)
        
    def forward(self, x):
        # 跨尺度注意力
        x1 = self.cross_scale_attn(x)
        
        # 小波注意力
        x2 = self.wavelet_attn(x)
        
        # 特征融合
        combined = torch.cat([x1, x2], dim=1)
        fused = self.fusion_conv(combined)
        
        return fused + x  # 残差连接

class EnhancedSRNetwork(nn.Module):
    """增强型超分辨率网络，结合3D感知和多种先进注意力机制"""
    def __init__(self, in_channels=1, out_channels=1, dim=64, depth=8,
                 window_size=8, num_heads=8, temporal_size=5,
                 pretrained_encoder_path=None):
        super().__init__()

        # 特征提取
        self.first_conv = nn.Conv2d(in_channels, dim, kernel_size=3, padding=1)
        
        # Transformer编码器
        self.encoder = TransformerEncoder(
            dim=dim,
            depth=depth,
            window_size=window_size,
            num_heads=num_heads
        )

        # 加载预训练编码器（可选）
        if pretrained_encoder_path:
            state_dict = torch.load(pretrained_encoder_path)
            # 移除预训练模型中与当前模型不匹配的层
            filtered_state_dict = {k: v for k, v in state_dict.items() if k in self.encoder.state_dict()}
            self.encoder.load_state_dict(filtered_state_dict, strict=False)

        # 伪3D上下文
        self.pseudo3d = Pseudo3DNet(
            in_channels=dim,
            out_channels=dim,
            temporal_size=temporal_size,
            depth=4  # 增加深度以提取更丰富的时间信息
        )
        
        # 增强型残差块
        self.res_blocks = nn.ModuleList([
            EnhancedResidualBlock(dim) for _ in range(4)
        ])
        
        # 可变形大核注意力块
        self.lka_blocks = nn.ModuleList([
            DeformableLKABlock(dim) for _ in range(2)
        ])
        
        # 多尺度特征融合
        self.multi_scale_fusion = MultiScaleFeatureFusion(dim)

        # 上采样
        self.upsampler = nn.Sequential(
            nn.Conv2d(dim, dim * 4, kernel_size=3, padding=1),
            nn.PixelShuffle(2),
            nn.LeakyReLU(0.2),
            nn.Conv2d(dim, dim * 4, kernel_size=3, padding=1),
            nn.PixelShuffle(2),
            nn.LeakyReLU(0.2)
        )

        # 重建
        self.last_conv = nn.Conv2d(dim, out_channels, kernel_size=3, padding=1)

    def forward(self, x_sequence):
        # x_sequence: [B, C, H, W] (C已包含时间窗口信息)
        B, C, H, W = x_sequence.shape

        # 特征提取
        x = self.first_conv(x_sequence)  # 直接使用整个输入
        x = self.encoder(x)

        # 伪3D上下文
        # 确保输入维度匹配 [B, T*C, H, W]
        B, TC, H, W = x_sequence.shape
        x_3d = self.pseudo3d(x_sequence)
        x = x + x_3d  # 融合
        
        # 增强型残差块处理
        for res_block in self.res_blocks:
            x = res_block(x)
        
        # 可变形大核注意力处理
        for lka_block in self.lka_blocks:
            x = lka_block(x)
        
        # 多尺度特征融合
        x = self.multi_scale_fusion(x)

        # 上采样
        x = self.upsampler(x)

        # 重建
        x = self.last_conv(x)

        return x
