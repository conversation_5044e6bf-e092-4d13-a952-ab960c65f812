# 脚本文件夹说明

本文件夹包含项目的各种实用脚本，用于简化训练、优化和部署流程。

## 📁 文件夹结构

```
scripts/
├── README.md                           # 本说明文件
├── pretraining/                        # 自监督预训练相关脚本
│   ├── train_swin_mae_final_optimization.py  # 最终优化训练脚本
│   ├── start_final_optimization.sh           # 预训练优化启动脚本
│   └── optimize_from_best_config.sh          # 基于最优配置的微调实验
├── supervised/                         # 监督训练相关脚本
│   └── start_supervised_training.sh          # 监督训练启动脚本
└── documentation/                      # 文档和总结
    ├── README_UPDATED.md                     # 更新的项目说明
    └── PROJECT_STATUS_SUMMARY.md             # 项目状态总结
```

## 🚀 快速使用指南

### 自监督预训练优化
```bash
# 进入脚本目录
cd scripts/pretraining

# 启动最终优化（推荐）
bash start_final_optimization.sh

# 或运行微调实验
bash optimize_from_best_config.sh
```

### 监督训练启动
```bash
# 进入脚本目录
cd scripts/supervised

# 启动监督训练
bash start_supervised_training.sh
```

## 📋 脚本详细说明

### 预训练脚本

#### `train_swin_mae_final_optimization.py`
- **功能**: 基于当前最优配置的最终优化训练
- **特点**: 集成梯度裁剪、学习率调度、混合精度训练
- **目标**: PSNR从21.51提升到22.0+

#### `start_final_optimization.sh`
- **功能**: 一键启动预训练优化
- **特点**: 自动环境检查、参数配置、目录创建
- **配置**: 基于实际最优参数设置

#### `optimize_from_best_config.sh`
- **功能**: 基于最优配置的5个微调实验
- **实验**: SSIM权重、NCE权重、组合优化、训练轮数、嵌入维度

### 监督训练脚本

#### `start_supervised_training.sh`
- **功能**: 安全启动超分辨率监督训练
- **特点**: 全面的前置条件检查
- **验证**: 数据路径、预训练模型、环境依赖

## ⚠️ 使用注意事项

1. **路径问题**: 所有脚本都假设从项目根目录执行
2. **环境依赖**: 确保Python环境和GPU可用
3. **数据验证**: 运行前检查数据路径是否正确
4. **权限问题**: 确保脚本有执行权限 (`chmod +x *.sh`)

## 🔧 自定义配置

如需修改配置，请编辑对应的脚本文件：
- 修改超参数: 编辑 `.sh` 文件中的参数
- 修改路径: 更新脚本中的 `--data_dir`, `--checkpoint_dir` 等参数
- 修改训练策略: 编辑 `.py` 文件中的训练逻辑

## 📞 技术支持

如遇到问题，请检查：
1. 脚本执行权限
2. Python环境激活
3. GPU内存充足
4. 数据路径正确
5. 预训练模型存在
