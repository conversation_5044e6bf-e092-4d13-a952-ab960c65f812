# Configuration for Conditional Diffusion Super-Resolution Training

# --- Data Configuration ---
data:
  # Using relative paths from the project root (e:/vscode/非配位超分辨)
  lr_dir: 'data/2号CT数据' # Relative path to Low-Resolution CT images (Dataset #2)
  hr_dir: 'data/3号CT数据' # Relative path to High-Resolution CT images (Dataset #3)
  patch_size_hr: 128          # Patch size for High-Resolution images during training
  scale_factor: 4             # Upscaling factor (HR_size / LR_size)
  # patch_size_lr will be calculated as patch_size_hr // scale_factor

  # --- Parameters for BSRGAN-style Degradation ---
  # Probabilities
  blur_prob: 1.0              # Probability of applying blur
  downsample_prob: 1.0        # Probability of applying downsampling (should usually be 1.0 for SR)
  noise_prob: 1.0             # Probability of adding noise
  shuffle_prob: 0.5           # Probability of shuffling degradation order (blur, downsample, noise)

  # Blur Settings
  blur_kernel_size: 21        # Kernel size for Gaussian blur
  blur_sigma_range: [0.2, 3.0] # Range for isotropic/anisotropic sigma
  aniso_prob: 0.5             # Probability of using anisotropic vs isotropic Gaussian blur

  # Downsampling Settings
  downsample_methods: ['bicubic', 'bilinear', 'nearest'] # Methods to randomly choose from

  # Noise Settings
  noise_gaussian_sigma_range: [1, 25] # Range for Gaussian noise std dev (applied to [0, 255] range conceptually)
  # Add other noise types (e.g., Poisson) if implemented later

# --- Model Configuration ---
model:
  in_channels: 1              # Number of input channels (1 for grayscale CT)
  out_channels: 1             # Number of output channels (1 for grayscale CT)
  base_channels: 128          # Base number of channels in the U-Net
  channel_mults: [1, 2, 2, 4] # Channel multipliers for each U-Net level (e.g., 128, 256, 256, 512)
  attention_resolutions: [16, 8] # Resolutions at which to use self-attention blocks (e.g., if patch_size_hr=128, these correspond to 16x16 and 8x8 feature maps)
  num_res_blocks: 2           # Number of residual blocks per U-Net level
  dropout: 0.1                # Dropout rate

  # --- Pretrained Encoder Settings ---
  use_pretrained_encoder: True # Enable pretrained encoder for enhanced training
  encoder_type: "Swin-MAE"     # Type of pretrained encoder ('Swin-MAE', 'ViT-MAE', etc.) - Must match model class name logic in diffusion_sr_model.py
  # UPDATED: Using the best performing hierarchical model with PSNR=21.51, SSIM=0.626
  encoder_checkpoint: "../checkpoints/swin_mae_hierarchical_random_ssim_nce_w002.pth" # Best pretrained model
  freeze_encoder: True         # Start with frozen encoder, can be unfrozen later for fine-tuning
  # pretrain_encoder_path: ../pretrain_model/mae_vit_base_patch16_dec512d8b.pth
  # pretrain_discriminator_path: ../pretrain_model/discriminator.pth
  condition_method: 'CrossAttention' # How to inject condition ('CrossAttention', 'Concat', None)
  encoder_injection_levels: [0, 1, 2, 3] # Inject features from all 4 stages of Swin-T
  use_adaptive_gate: True     # <<< ENABLE ADAPTIVE GATING >>> (Set to False to disable)
  num_heads: 8                # Number of heads for attention blocks (SelfAttention and CrossAttention)
  # head_dim: 64              # Optional: Dimension per head (if not set, calculated as channels // num_heads)

# --- Discriminator Configuration (for GAN training) ---
discriminator:
  # Settings for the NLayerDiscriminator (PatchGAN)
  ndf: 64                     # Number of filters in the first conv layer of D
  n_layers: 3                 # Number of conv layers in the D
  # norm_layer: 'BatchNorm2d' # Normalization layer type (can be added if needed)
  lr: 1.0e-4                  # Learning rate for the discriminator optimizer (can be same or different from G)

# --- Diffusion Process Configuration ---
diffusion:
  schedule_name: 'cosine'     # Noise schedule type ('linear', 'cosine')
  timesteps: 1000             # Total number of diffusion timesteps

# --- Training Configuration ---
training:
  # !!! IMPORTANT: Update log_root and checkpoint_root if desired !!!
  log_root: './logs/sr_diffusion'          # Root directory for TensorBoard logs (Using relative path with forward slashes)
  checkpoint_root: './checkpoints/sr_diffusion' # Root directory for saving model checkpoints (Using relative path with forward slashes)
  learning_rate: 1.0e-4       # Initial learning rate
  weight_decay: 0.0           # Weight decay for AdamW optimizer
  batch_size: 8               # Training batch size (increased for better training)
  epochs: 200               # Total number of training epochs (restored to full training)
  diffusion_loss_type: 'l1'   # Loss for comparing predicted noise and actual noise ('l1' or 'mse')
  perceptual_loss_weight: 0.1 # Weight for the VGG perceptual loss (set to 0 to disable)
  perceptual_loss_type: 'l1'  # Type of loss for comparing VGG features ('l1' or 'mse')
  ssim_loss_weight: 0.2       # <<< SET SSIM LOSS WEIGHT >>> (Adjust as needed, e.g., 0.1-1.0)
  # use_ms_ssim: False        # Optional: Set to True to use MS-SSIM instead of SSIM
  gradient_loss_weight: 0.1   # <<< SET GRADIENT LOSS WEIGHT >>> (Adjust as needed, e.g., 0.05-0.5)
  use_gan: False              # <<< DISABLE GAN LOSS >>> (Temporarily disabled due to training imbalance)
  gan_loss_weight: 0.0        # <<< GAN LOSS DISABLED >>> (Set to 0 to focus on other losses)
  # encoder_lr: 5.0e-5        # Optional: Set a lower LR for the encoder during fine-tuning
  use_amp: True               # Use Automatic Mixed Precision (requires compatible GPU)
  seed: 42                    # Random seed for reproducibility
  num_workers: 4              # Number of workers for DataLoader (adjust based on system)
  log_interval: 1           # Log batch loss every N steps
  save_interval: 10           # Save checkpoint every N epochs
  # val_interval: 5             # Run validation every N epochs (requires validation dataloader setup)
  # grad_clip_norm: 1.0         # Optional gradient clipping norm value (set to None to disable)

# --- Inference Configuration ---
inference:
  sampling_steps: 100         # Number of DDIM steps for generation during inference
  eta: 0.0                    # DDIM eta parameter (0.0 for deterministic)
