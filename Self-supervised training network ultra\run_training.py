#!/usr/bin/env python3
"""
简化的训练启动脚本
直接在当前环境中运行，显示关键训练信息
"""

import os
import sys
import time
from datetime import datetime

def print_header():
    """打印训练开始信息"""
    print("="*70)
    print("🚀 CT图像超分辨率训练 - 增强安静模式")
    print("="*70)
    print(f"⏰ 开始时间: {datetime.now().strftime('%Y-%m-%d %H:%M:%S')}")
    print(f"📁 工作目录: {os.getcwd()}")
    print(f"🐍 Python环境: {sys.executable}")
    print(f"📋 配置文件: configs/config_ultra_no_gan.yaml")
    print("-"*70)

def main():
    """主函数"""
    print_header()

    # 检查必要文件
    config_file = "configs/config_ultra_no_gan.yaml"
    train_script = "train_sr_ultra.py"

    if not os.path.exists(config_file):
        print(f"❌ 配置文件不存在: {config_file}")
        return 1

    if not os.path.exists(train_script):
        print(f"❌ 训练脚本不存在: {train_script}")
        return 1

    print("✅ 文件检查通过，开始训练...")
    print("-"*70)

    # 导入并运行训练
    try:
        # 设置命令行参数
        sys.argv = [
            'train_sr_ultra.py',
            '--config', config_file,
            '--tag', f'NoGAN_Training_{datetime.now().strftime("%m%d_%H%M")}'
        ]

        # 导入训练模块
        from train_sr_ultra import parse_args, load_config, train

        # 解析参数并开始训练
        args = parse_args()
        config = load_config(args.config)

        print("🎯 训练配置:")
        print(f"   - 模型类型: 扩散超分辨率 (无GAN)")
        print(f"   - 批次大小: {config['training']['batch_size']}")
        print(f"   - 学习率: {config['training']['learning_rate']}")
        print(f"   - 训练轮次: {config['training']['epochs']}")
        print(f"   - 实验标签: {args.tag}")
        print("-"*70)

        # 开始训练
        train(config, args)

        print("\n" + "="*70)
        print("🎉 训练完成！")
        print(f"⏰ 结束时间: {datetime.now().strftime('%Y-%m-%d %H:%M:%S')}")
        print("="*70)

        return 0

    except KeyboardInterrupt:
        print("\n" + "="*70)
        print("⏹️ 训练被用户中断")
        print("="*70)
        return 1

    except Exception as e:
        print(f"\n❌ 训练过程中出现错误: {e}")
        import traceback
        traceback.print_exc()
        return 1

if __name__ == '__main__':
    exit_code = main()
    sys.exit(exit_code)
