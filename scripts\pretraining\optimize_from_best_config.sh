#!/bin/bash
# 基于当前最优配置的进一步优化实验
# 当前最佳: PSNR=21.51, SSIM=0.626
# 目标: PSNR>22.0, SSIM>0.65

echo "🎯 基于最优配置的进一步优化实验"
echo "=============================================="
echo "当前最佳配置: PSNR=21.51, SSIM=0.626"
echo "目标: PSNR>22.0, SSIM>0.65"
echo "=============================================="

# 基础配置（您的最优配置）
BASE_CONFIG="--seed 42 --patch_size 4 --swin_embed_dim 96 --decoder_embed_dim 128 --decoder_depths 1 1 1 1 --decoder_num_heads 8 8 4 2 --lr 1.5e-4 --warmup_epochs 40 --weight_decay 0.05 --epochs 200 --batch_size 8 --gradient_accumulation_steps 16 --perceptual_loss_weight 0.005 --nce_proj_dim 256 --nce_T 0.07 --use_amp"

# 实验1: 微调SSIM损失权重 (当前0.05 -> 0.06)
echo "🔬 实验1: 微调SSIM损失权重 0.05 -> 0.06"
python train_swin_mae_resnet_random_mask_hierarchical.py \
    $BASE_CONFIG \
    --ssim_loss_weight 0.06 \
    --patchnce_loss_weight 0.005 \
    --checkpoint_dir checkpoints/swin_mae_opt_exp1_ssim006 \
    --log_dir logs/swin_mae_opt_exp1_ssim006

echo "✅ 实验1完成"

# 实验2: 微调PatchNCE损失权重 (当前0.005 -> 0.004)
echo "🔬 实验2: 微调PatchNCE损失权重 0.005 -> 0.004"
python train_swin_mae_resnet_random_mask_hierarchical.py \
    $BASE_CONFIG \
    --ssim_loss_weight 0.05 \
    --patchnce_loss_weight 0.004 \
    --checkpoint_dir checkpoints/swin_mae_opt_exp2_nce004 \
    --log_dir logs/swin_mae_opt_exp2_nce004

echo "✅ 实验2完成"

# 实验3: 同时微调两个损失权重
echo "🔬 实验3: 同时微调 SSIM=0.06, NCE=0.004"
python train_swin_mae_resnet_random_mask_hierarchical.py \
    $BASE_CONFIG \
    --ssim_loss_weight 0.06 \
    --patchnce_loss_weight 0.004 \
    --checkpoint_dir checkpoints/swin_mae_opt_exp3_combined \
    --log_dir logs/swin_mae_opt_exp3_combined

echo "✅ 实验3完成"

# 实验4: 增加训练轮数 (200 -> 250)
echo "🔬 实验4: 增加训练轮数 200 -> 250"
python train_swin_mae_resnet_random_mask_hierarchical.py \
    $BASE_CONFIG \
    --ssim_loss_weight 0.05 \
    --patchnce_loss_weight 0.005 \
    --epochs 250 \
    --checkpoint_dir checkpoints/swin_mae_opt_exp4_epochs250 \
    --log_dir logs/swin_mae_opt_exp4_epochs250

echo "✅ 实验4完成"

# 实验5: 微调解码器嵌入维度 (128 -> 160)
echo "🔬 实验5: 微调解码器嵌入维度 128 -> 160"
python train_swin_mae_resnet_random_mask_hierarchical.py \
    $BASE_CONFIG \
    --decoder_embed_dim 160 \
    --ssim_loss_weight 0.05 \
    --patchnce_loss_weight 0.005 \
    --checkpoint_dir checkpoints/swin_mae_opt_exp5_embed160 \
    --log_dir logs/swin_mae_opt_exp5_embed160

echo "✅ 实验5完成"

echo ""
echo "🎉 所有优化实验完成！"
echo "=============================================="
echo "请检查以下目录的结果:"
echo "- 实验1 (SSIM=0.06): checkpoints/swin_mae_opt_exp1_ssim006/"
echo "- 实验2 (NCE=0.004): checkpoints/swin_mae_opt_exp2_nce004/"
echo "- 实验3 (组合优化): checkpoints/swin_mae_opt_exp3_combined/"
echo "- 实验4 (250轮): checkpoints/swin_mae_opt_exp4_epochs250/"
echo "- 实验5 (embed=160): checkpoints/swin_mae_opt_exp5_embed160/"
echo ""
echo "使用以下命令监控训练:"
echo "tensorboard --logdir logs/"
echo "=============================================="
