import torch
import torch.nn as nn
import torch.nn.functional as F
import torch.optim as optim
from torch.optim.lr_scheduler import CosineAnnealingLR
from data.mae_loader import get_mae_loader # Import new loader
import timm # Import timm
assert timm.__version__ >= "0.3.2" # MAE requires timm support, assert version
from timm.models.vision_transformer import Block # Use timm's Block for decoder
# Removed auxiliary loss imports as they are no longer used in feature prediction mode
# import torchvision.models as models
# from torchvision.models import vgg16
# from torchmetrics.image import StructuralSimilarityIndexMeasure
# try:
#     import kornia.filters as K
# except ImportError:
#     print("Warning: kornia not installed. Gradient loss will not be available.")
#     print("Install with: pip install kornia")
#     K = None
from tqdm import tqdm
import os
import numpy as np
import matplotlib.pyplot as plt
from torch.utils.tensorboard import SummaryWriter
import math
import random
from util.pos_embed import get_2d_sincos_pos_embed # Assuming pos embed helper is in util
import util.lr_sched as lr_sched # Import lr_sched for warmup adjustment
import argparse # Import argparse
from datetime import datetime # Import datetime

# --------------------------------------------------------
# Based on MAE implementation by Meta Platforms, Inc.
# --------------------------------------------------------

# --- Block Masking Function ---
def block_random_masking(x, mask_ratio, block_size, patch_grid_size, device):
    """
    Perform per-sample block-wise random masking.
    Returns visible patches, patch-level mask (0=keep, 1=mask), and patch-level restore indices.
    x: [N, L, D], sequence where L = H_patch * W_patch
    mask_ratio: float, target ratio of patches to mask
    block_size: int, size of the square block (e.g., 4 means 4x4 patches)
    patch_grid_size: tuple (H_patch, W_patch)
    """
    N, L, D = x.shape
    H_patch, W_patch = patch_grid_size
    assert H_patch * W_patch == L, "L does not match patch grid size"
    assert H_patch % block_size == 0 and W_patch % block_size == 0, "Patch grid must be divisible by block size"

    num_blocks_h = H_patch // block_size
    num_blocks_w = W_patch // block_size
    num_blocks = num_blocks_h * num_blocks_w
    num_patches_per_block = block_size * block_size

    num_patches_to_keep = int(L * (1 - mask_ratio))
    num_blocks_to_keep = math.ceil(num_patches_to_keep / num_patches_per_block)
    num_blocks_to_keep = min(num_blocks_to_keep, num_blocks)

    noise = torch.rand(N, num_blocks, device=device)
    ids_shuffle_blocks = torch.argsort(noise, dim=1)
    ids_restore_blocks = torch.argsort(ids_shuffle_blocks, dim=1)

    ids_keep_blocks = ids_shuffle_blocks[:, :num_blocks_to_keep]

    mask_blocks = torch.ones(N, num_blocks, device=device)
    mask_blocks.scatter_(1, ids_keep_blocks, 0)
    mask_blocks = torch.gather(mask_blocks, dim=1, index=ids_restore_blocks)

    mask_patches = mask_blocks.reshape(N, num_blocks_h, num_blocks_w)
    mask_patches = mask_patches.repeat_interleave(block_size, dim=1).repeat_interleave(block_size, dim=2)
    mask_patches = mask_patches.reshape(N, L)

    ids_shuffle_patches = torch.argsort(mask_patches + torch.rand_like(mask_patches) * 1e-6, dim=1)
    ids_restore_patches = torch.argsort(ids_shuffle_patches, dim=1)

    len_keep_patches = (mask_patches == 0).sum(dim=1).min().item()

    ids_keep_patches = ids_shuffle_patches[:, :len_keep_patches]

    x_visible = torch.gather(x, dim=1, index=ids_keep_patches.unsqueeze(-1).repeat(1, 1, D))

    return x_visible, mask_patches, ids_restore_patches


class MaskedAutoencoderViT(nn.Module):
    """ Masked Autoencoder with VisionTransformer backbone - Feature Prediction Target """
    def __init__(self, img_size=256, patch_size=16, in_chans=1,
                 embed_dim=768, depth=12, num_heads=12,
                 decoder_embed_dim=512, decoder_depth=8, decoder_num_heads=16,
                 mlp_ratio=4., norm_layer=nn.LayerNorm, norm_feat_loss=False, # Changed norm_pix_loss to norm_feat_loss
                 masking_strategy='random', block_size=4):
        super().__init__()

        self.img_size = img_size
        self.patch_size = patch_size
        self.in_chans = in_chans
        self.masking_strategy = masking_strategy
        self.block_size = block_size
        self.embed_dim = embed_dim # Store embed_dim for decoder_pred

        # --------------------------------------------------------------------------
        # Encoder
        vit_model_name = f'vit_base_patch{patch_size}_{img_size}'
        try:
            self.encoder = timm.create_model(
                vit_model_name, pretrained=False, in_chans=in_chans, img_size=img_size,
                embed_dim=embed_dim, depth=depth, num_heads=num_heads, mlp_ratio=mlp_ratio,
                norm_layer=norm_layer, global_pool=''
            )
            print(f"Using timm model: {vit_model_name}")
        except Exception as e:
            print(f"Warning: Could not create timm model {vit_model_name}. Error: {e}. Falling back to vit_base_patch16_224.")
            self.encoder = timm.create_model(
                'vit_base_patch16_224', pretrained=False, in_chans=in_chans, img_size=img_size,
                patch_size=patch_size, embed_dim=embed_dim, depth=depth, num_heads=num_heads,
                mlp_ratio=mlp_ratio, norm_layer=norm_layer, global_pool=''
            )

        # Remove final norm/head if they exist
        if hasattr(self.encoder, 'norm'): del self.encoder.norm
        if hasattr(self.encoder, 'head'): del self.encoder.head
        self.patch_embed = self.encoder.patch_embed
        self.pos_embed = self.encoder.pos_embed
        self.num_patches = self.patch_embed.num_patches
        self.patch_grid_size = self.patch_embed.grid_size
        # --------------------------------------------------------------------------

        # --------------------------------------------------------------------------
        # Decoder
        self.decoder_embed = nn.Linear(embed_dim, decoder_embed_dim, bias=True)
        self.mask_token = nn.Parameter(torch.zeros(1, 1, decoder_embed_dim))
        self.decoder_pos_embed = nn.Parameter(torch.zeros(1, self.num_patches + 1, decoder_embed_dim), requires_grad=False)
        self.decoder_blocks = nn.ModuleList([
            Block(decoder_embed_dim, decoder_num_heads, mlp_ratio, qkv_bias=True, norm_layer=norm_layer)
            for i in range(decoder_depth)])
        self.decoder_norm = norm_layer(decoder_embed_dim)
        # Modify decoder_pred to output features matching encoder embed_dim
        self.decoder_pred = nn.Linear(decoder_embed_dim, embed_dim, bias=True) # Output encoder features
        # --------------------------------------------------------------------------

        self.norm_feat_loss = norm_feat_loss # Use this flag for feature normalization

        self.initialize_weights()

    def initialize_weights(self):
        grid_size = self.patch_embed.grid_size
        pos_embed = get_2d_sincos_pos_embed(self.pos_embed.shape[-1], grid_size, cls_token=True)
        self.pos_embed.data.copy_(torch.from_numpy(pos_embed).float().unsqueeze(0))

        decoder_pos_embed = get_2d_sincos_pos_embed(self.decoder_pos_embed.shape[-1], grid_size, cls_token=True)
        self.decoder_pos_embed.data.copy_(torch.from_numpy(decoder_pos_embed).float().unsqueeze(0))

        w = self.patch_embed.proj.weight.data
        torch.nn.init.xavier_uniform_(w.view([w.shape[0], -1]))

        if hasattr(self.encoder, 'cls_token'):
             torch.nn.init.normal_(self.encoder.cls_token, std=.02)
        torch.nn.init.normal_(self.mask_token, std=.02)

        self.apply(self._init_weights)

    def _init_weights(self, m):
        if isinstance(m, nn.Linear):
            torch.nn.init.xavier_uniform_(m.weight)
            if isinstance(m, nn.Linear) and m.bias is not None:
                nn.init.constant_(m.bias, 0)
        elif isinstance(m, nn.LayerNorm):
            nn.init.constant_(m.bias, 0)
            nn.init.constant_(m.weight, 1.0)

    def _random_masking_original(self, x, mask_ratio):
        N, L, D = x.shape
        len_keep = int(L * (1 - mask_ratio))
        noise = torch.rand(N, L, device=x.device)
        ids_shuffle = torch.argsort(noise, dim=1)
        ids_restore = torch.argsort(ids_shuffle, dim=1)
        ids_keep = ids_shuffle[:, :len_keep]
        x_visible = torch.gather(x, dim=1, index=ids_keep.unsqueeze(-1).repeat(1, 1, D))
        mask = torch.ones([N, L], device=x.device)
        mask[:, :len_keep] = 0
        mask = torch.gather(mask, dim=1, index=ids_restore)
        return x_visible, mask, ids_restore

    def _block_random_masking(self, x, mask_ratio):
        return block_random_masking(x, mask_ratio, self.block_size, self.patch_grid_size, x.device)

    def forward_encoder(self, x, mask_ratio):
        x = self.patch_embed(x)
        x = x + self.pos_embed[:, 1:, :]

        if self.masking_strategy == 'random':
            x_visible, mask, ids_restore = self._random_masking_original(x, mask_ratio)
        elif self.masking_strategy == 'block':
            x_visible, mask, ids_restore = self._block_random_masking(x, mask_ratio)
        else:
            raise ValueError(f"Unknown masking strategy: {self.masking_strategy}")

        cls_token = self.encoder.cls_token + self.pos_embed[:, :1, :]
        cls_tokens = cls_token.expand(x_visible.shape[0], -1, -1)
        x = torch.cat((cls_tokens, x_visible), dim=1)

        for blk in self.encoder.blocks:
             x = blk(x)

        return x, mask, ids_restore

    def forward_decoder(self, x, ids_restore):
        x = self.decoder_embed(x)
        mask_tokens = self.mask_token.repeat(x.shape[0], ids_restore.shape[1] + 1 - x.shape[1], 1)
        x_ = torch.cat([x[:, 1:, :], mask_tokens], dim=1)
        x_ = torch.gather(x_, dim=1, index=ids_restore.unsqueeze(-1).repeat(1, 1, x.shape[2]))
        x = torch.cat([x[:, :1, :], x_], dim=1)
        x = x + self.decoder_pos_embed

        for blk in self.decoder_blocks:
            x = blk(x)
        x = self.decoder_norm(x)
        x = self.decoder_pred(x) # Output is now [N, L+1, embed_dim]
        x = x[:, 1:, :] # Remove cls token -> [N, L, embed_dim]
        return x

    def forward_loss(self, target_features, pred_features, mask):
        """
        Calculate the L2 loss between predicted and target features on masked patches.

        Args:
            target_features (torch.Tensor): Target features from encoder [N, L, embed_dim].
            pred_features (torch.Tensor): Predicted features from decoder [N, L, embed_dim].
            mask (torch.Tensor): Binary mask [N, L], 0 is keep, 1 is remove/masked.

        Returns:
            torch.Tensor: L2 loss computed only on the masked patches.
        """
        if self.norm_feat_loss: # Use the flag for feature normalization
            mean = target_features.mean(dim=-1, keepdim=True)
            var = target_features.var(dim=-1, keepdim=True)
            target_features = (target_features - mean) / (var + 1.e-6)**.5

        loss = (pred_features - target_features) ** 2
        loss = loss.mean(dim=-1)  # [N, L], mean loss per patch

        mask_sum = mask.sum()
        feature_loss = (loss * mask).sum() / (mask_sum + 1e-8) # Add epsilon for stability

        return feature_loss

    def forward(self, imgs, mask_ratio=0.75):
        """
        Forward pass for feature prediction MAE.

        Args:
            imgs (torch.Tensor): Input images [N, C, H, W].
            mask_ratio (float): Ratio of patches to mask.

        Returns:
            tuple: Contains:
                - torch.Tensor: The feature prediction loss value.
                - torch.Tensor: Predicted features [N, L, embed_dim].
                - torch.Tensor: Binary mask [N, L].
        """
        # 1. Get embeddings (without pos embed initially for target)
        x_unmasked_emb = self.patch_embed(imgs) # [N, L, D]

        # 2. Encode visible patches (includes masking and adding pos embed inside)
        latent_visible, mask, ids_restore = self.forward_encoder(imgs, mask_ratio)

        # 3. Decode to predict features for all patches
        pred_features = self.forward_decoder(latent_visible, ids_restore) # [N, L, embed_dim]

        # 4. Generate target features using the encoder (with no_grad)
        with torch.no_grad():
            # Add pos embed to unmasked embeddings for target generation
            target_input = x_unmasked_emb + self.pos_embed[:, 1:, :]

            # Append cls token if encoder uses it internally
            if hasattr(self.encoder, 'cls_token'):
                 cls_token = self.encoder.cls_token + self.pos_embed[:, :1, :]
                 cls_tokens = cls_token.expand(target_input.shape[0], -1, -1)
                 target_latent_with_cls = torch.cat((cls_tokens, target_input), dim=1)
            else:
                 target_latent_with_cls = target_input

            # Apply Transformer blocks
            for blk in self.encoder.blocks:
                target_latent_with_cls = blk(target_latent_with_cls)

            # Remove CLS token if added
            if hasattr(self.encoder, 'cls_token'):
                 target_features_all = target_latent_with_cls[:, 1:, :] # [N, L, embed_dim]
            else:
                 target_features_all = target_latent_with_cls # [N, L, embed_dim]

            # Optional: Normalize target features
            if self.norm_feat_loss: # Use the dedicated flag
                 mean = target_features_all.mean(dim=-1, keepdim=True)
                 var = target_features_all.var(dim=-1, keepdim=True)
                 target_features_all = (target_features_all - mean) / (var + 1.e-6)**.5

        # 5. Calculate feature loss on masked patches
        feature_loss = self.forward_loss(target_features_all, pred_features, mask) # Pass features to loss

        return feature_loss, pred_features, mask


# --- Training Function ---
def setup_mae_training(model_params, optimizer_params, device):
    """设置 MAE 训练的优化器和调度器"""
    lr = optimizer_params.get('lr', 1.5e-4)
    weight_decay = optimizer_params.get('weight_decay', 0.05)
    optimizer = optim.AdamW(model_params, lr=lr, betas=(0.9, 0.95), weight_decay=weight_decay)
    scheduler = CosineAnnealingLR(optimizer, T_max=optimizer_params['epochs'], eta_min=lr/100)
    scaler = torch.cuda.amp.GradScaler(enabled=optimizer_params.get('use_amp', False))
    return optimizer, scheduler, scaler

# --- Training Function ---
# Modified to handle only feature loss
def train_epoch_mae(model, train_loader, optimizer, scaler, device, writer, epoch, total_epochs, base_lr, warmup_epochs,
                    gradient_accumulation_steps=1, log_interval=50, use_amp=False,
                    mask_ratio=0.75):
    """MAE 训练循环 for Feature Prediction"""
    model.train()
    total_feature_loss = 0
    num_batches = len(train_loader)
    processed_batches = 0
    optimizer.zero_grad()

    with tqdm(train_loader, unit="batch") as pbar:
        for batch_idx, imgs in enumerate(pbar):
            if imgs is None or imgs.nelement() == 0:
                print(f"Skipping empty batch at index {batch_idx}")
                continue

            global_step = epoch * num_batches + batch_idx
            # Adjust learning rate
            if epoch < warmup_epochs:
                num_warmup_steps = warmup_epochs * num_batches
                if num_warmup_steps > 0:
                    lr_scale = min(1.0, float(global_step + 1) / num_warmup_steps)
                    new_lr = base_lr * lr_scale
                    for param_group in optimizer.param_groups:
                        param_group['lr'] = new_lr

            imgs = imgs.to(device, non_blocking=True)

            with torch.cuda.amp.autocast(enabled=use_amp):
                feature_loss, _, _ = model(imgs, mask_ratio=mask_ratio) # Model now returns feature_loss
                loss_value = feature_loss.item() # Store for logging

                # Scale loss for accumulation
                loss = feature_loss / gradient_accumulation_steps

            if math.isnan(loss_value):
                print(f"Warning: NaN loss detected at epoch {epoch+1}, batch {batch_idx}. Skipping gradient step.")
                if (batch_idx + 1) % gradient_accumulation_steps == 0 or (batch_idx + 1) == num_batches:
                    optimizer.zero_grad(set_to_none=True)
                continue

            scaler.scale(loss).backward()

            if (batch_idx + 1) % gradient_accumulation_steps == 0 or (batch_idx + 1) == num_batches:
                scaler.step(optimizer)
                scaler.update()
                optimizer.zero_grad(set_to_none=True)

            if math.isnan(loss_value): # Double check
                print(f"Warning: NaN loss detected after scaling at epoch {epoch+1}, batch {batch_idx}. Skipping batch.")
                optimizer.zero_grad()
                continue

            total_feature_loss += loss_value
            processed_batches += 1
            pbar.set_postfix(FeatLoss=f"{loss_value:.4f}", LR=f"{optimizer.param_groups[0]['lr']:.1e}")

            if global_step % (log_interval * gradient_accumulation_steps) == 0:
                 writer.add_scalar('Loss/FeatureLoss_batch', loss_value, global_step)
                 writer.add_scalar('LR', optimizer.param_groups[0]['lr'], global_step)

    avg_feature_loss = total_feature_loss / processed_batches if processed_batches > 0 else 0
    return avg_feature_loss # Return only the feature loss


def pretrain_mae(data_dir='E:/vscode/2号CT数据', batch_size=64, num_workers=4,
                 epochs=200, lr=1.5e-4, warmup_epochs=40, weight_decay=0.05, use_amp=True,
                 gradient_accumulation_steps=1,
                 img_size=256, patch_size=16,
                 mask_ratio=0.75,
                 masking_strategy='random', block_size=4,
                 model_embed_dim=768, model_depth=12, model_num_heads=12,
                 decoder_embed_dim=512, decoder_depth=8, decoder_num_heads=16,
                 clip_min=-1000.0, clip_max=1000.0,
                 norm_feat_loss=False, # Added flag for feature normalization
                 device=None, save_interval=20, resume=None,
                 checkpoint_dir='checkpoints/mae_pretrain_featpred', log_dir='logs/mae_pretrain_featpred'): # Updated default dirs
    """MAE Feature Prediction Pretraining"""

    device = torch.device('cuda' if torch.cuda.is_available() else 'cpu')
    print(f"Using device: {device}")
    effective_batch_size = batch_size * gradient_accumulation_steps
    print(f"MAE Feature Prediction Pretraining Parameters:")
    print(f"  - Training: epochs={epochs}, batch_size={batch_size}, grad_accum={gradient_accumulation_steps}, effective_batch_size={effective_batch_size}, use_amp={use_amp}")
    print(f"  - Model: img_size={img_size}, patch_size={patch_size}, mask_ratio={mask_ratio}, masking_strategy={masking_strategy}, block_size={block_size if masking_strategy=='block' else 'N/A'}, norm_feat_loss={norm_feat_loss}, decoder_depth={decoder_depth}")
    print(f"  - Data: clip_min={clip_min}, clip_max={clip_max}")

    run_name = f"ps{patch_size}_dd{decoder_depth}_mr{mask_ratio:.2f}_mask{masking_strategy}{block_size if masking_strategy=='block' else ''}_featpred_norm{norm_feat_loss}_{datetime.now().strftime('%Y%m%d_%H%M')}"
    checkpoint_dir = os.path.join(checkpoint_dir, run_name)
    log_dir = os.path.join(log_dir, run_name)

    os.makedirs(checkpoint_dir, exist_ok=True)
    os.makedirs(log_dir, exist_ok=True)
    writer = SummaryWriter(log_dir)

    model = MaskedAutoencoderViT(
        img_size=img_size, patch_size=patch_size, in_chans=1,
        embed_dim=model_embed_dim, depth=model_depth, num_heads=model_num_heads,
        decoder_embed_dim=decoder_embed_dim, decoder_depth=decoder_depth, decoder_num_heads=decoder_num_heads,
        mlp_ratio=4.0, norm_feat_loss=norm_feat_loss, # Pass feature norm flag
        masking_strategy=masking_strategy, block_size=block_size
    ).to(device)

    print(f"MAE Model Parameters: {sum(p.numel() for p in model.parameters() if p.requires_grad):,}")

    # Remove auxiliary loss criteria instantiation
    # ssim_criterion = ...
    # perceptual_criterion = ...

    train_loader = get_mae_loader(
        data_dir=data_dir, batch_size=batch_size, num_workers=num_workers,
        crop_size=img_size, augment=True, clip_min=clip_min, clip_max=clip_max
     )

    scheduler_epochs = epochs - warmup_epochs
    optimizer_params = {'lr': lr, 'epochs': scheduler_epochs, 'weight_decay': weight_decay, 'use_amp': use_amp}
    optimizer, scheduler, scaler = setup_mae_training(model.parameters(), optimizer_params, device)

    start_epoch = 0
    best_loss = float('inf') # Track best feature loss

    if resume and os.path.isfile(resume):
        print(f"Resuming from checkpoint: {resume}")
        checkpoint = torch.load(resume, map_location='cpu')
        try:
            model.load_state_dict(checkpoint['model_state_dict'], strict=False)
            optimizer.load_state_dict(checkpoint['optimizer_state_dict'])
            scheduler.load_state_dict(checkpoint['scheduler_state_dict'])
            start_epoch = checkpoint['epoch']
            best_loss = checkpoint.get('best_loss', checkpoint.get('loss', float('inf'))) # Use 'best_loss' if saved
            if use_amp and 'scaler_state_dict' in checkpoint and checkpoint['scaler_state_dict'] is not None:
                 scaler.load_state_dict(checkpoint['scaler_state_dict'])
            print(f"Resumed from epoch {start_epoch} with best loss {best_loss:.6f}")
        except Exception as e:
            print(f"Error loading checkpoint, starting from scratch: {e}")
            start_epoch = 0
            best_loss = float('inf')

    # Training loop
    for epoch in range(start_epoch, epochs):
        print(f"\nEpoch {epoch+1}/{epochs}:")
        # Only feature loss is returned now
        avg_feature_loss = train_epoch_mae(
            model, train_loader, optimizer, scaler, device, writer, epoch,
            total_epochs=epochs, base_lr=lr, warmup_epochs=warmup_epochs,
            gradient_accumulation_steps=gradient_accumulation_steps,
            use_amp=use_amp,
            mask_ratio=mask_ratio
            # Removed auxiliary criteria and weights
        )

        if epoch >= warmup_epochs:
             if hasattr(scheduler, 'step'):
                 scheduler.step()
             else:
                 print("Warning: Scheduler does not have a 'step' method.")

        current_lr = optimizer.param_groups[0]['lr']
        print(f"Epoch {epoch+1}/{epochs} completed.")
        print(f"  Avg Feature Loss: {avg_feature_loss:.6f}") # Log feature loss
        print(f"  Current LR: {current_lr:.6f}")

        writer.add_scalar('Loss/FeatureLoss_epoch', avg_feature_loss, epoch) # Log feature loss
        writer.add_scalar('LR_epoch', current_lr, epoch)

        is_best = avg_feature_loss < best_loss
        if is_best:
            best_loss = avg_feature_loss

        if (epoch + 1) % save_interval == 0 or is_best or (epoch + 1) == epochs:
             checkpoint_data = {
                 'epoch': epoch + 1,
                 'model_state_dict': model.state_dict(),
                 'optimizer_state_dict': optimizer.state_dict(),
                 'scheduler_state_dict': scheduler.state_dict(),
                 'scaler_state_dict': scaler.state_dict() if use_amp else None,
                 'loss': avg_feature_loss, # Save feature loss as primary 'loss'
                 'best_loss': best_loss, # Save best loss tracked
                 'args': { # Save key parameters
                     'img_size': img_size, 'patch_size': patch_size,
                     'model_embed_dim': model_embed_dim, 'model_depth': model_depth,
                     'decoder_embed_dim': decoder_embed_dim, 'decoder_depth': decoder_depth,
                     'lr': lr, 'warmup_epochs': warmup_epochs, 'epochs': epochs,
                     'norm_feat_loss': model.norm_feat_loss, # Save feature norm flag
                     'mask_ratio': mask_ratio,
                     'masking_strategy': masking_strategy, 'block_size': block_size,
                     'gradient_accumulation_steps': gradient_accumulation_steps,
                     'clip_min': clip_min, 'clip_max': clip_max
                 }
             }
             save_path = os.path.join(checkpoint_dir, f"checkpoint_epoch{epoch+1}.pth")
             torch.save(checkpoint_data, save_path)
             print(f"Saved checkpoint: {save_path}")
             if is_best:
                 best_path = os.path.join(checkpoint_dir, "best_model.pth")
                 torch.save(checkpoint_data, best_path)
                 print(f"Saved best model to: {best_path} with feature loss: {best_loss:.6f}")


    writer.close()
    print("MAE Feature Prediction Pretraining completed!")


# --- Main execution block (Example) ---
if __name__ == '__main__':
    import argparse
    parser = argparse.ArgumentParser('Improved MAE Feature Prediction script', add_help=False)

    # Training parameters
    parser.add_argument('--batch_size', default=32, type=int, help='Batch size per GPU')
    parser.add_argument('--epochs', default=400, type=int, help='Total training epochs')
    parser.add_argument('--lr', type=float, default=1.5e-4, help='Base learning rate')
    parser.add_argument('--warmup_epochs', type=int, default=40, help='Epochs to warmup LR')
    parser.add_argument('--weight_decay', type=float, default=0.05)
    parser.add_argument('--use_amp', action='store_true', default=True, help='Use mixed precision')
    parser.add_argument('--gradient_accumulation_steps', type=int, default=2, help='Gradient accumulation steps')

    # Model parameters
    parser.add_argument('--img_size', default=256, type=int, help='Images input size')
    parser.add_argument('--patch_size', default=16, type=int, help='Patch size')
    parser.add_argument('--mask_ratio', default=0.75, type=float, help='Masking ratio')
    parser.add_argument('--masking_strategy', type=str, default='random', choices=['random', 'block'], help='Masking strategy')
    parser.add_argument('--block_size', type=int, default=4, help='Block size for block masking')
    parser.add_argument('--model_embed_dim', default=768, type=int)
    parser.add_argument('--model_depth', default=12, type=int)
    parser.add_argument('--model_num_heads', default=12, type=int)
    parser.add_argument('--decoder_embed_dim', default=512, type=int)
    parser.add_argument('--decoder_depth', default=8, type=int)
    parser.add_argument('--decoder_num_heads', default=16, type=int)
    parser.add_argument('--norm_feat_loss', action='store_true', default=False, help='Normalize target features before loss calculation') # Changed flag name

    # Loss parameters (Removed auxiliary loss weights)
    # parser.add_argument('--mse_weight', type=float, default=1.0, help='Weight for MSE reconstruction loss')
    # parser.add_argument('--ssim_weight', type=float, default=0.1, help='Weight for SSIM loss (1-SSIM)')
    # parser.add_argument('--perceptual_weight', type=float, default=0.1, help='Weight for VGG Perceptual loss')
    # parser.add_argument('--gradient_loss_weight', type=float, default=0.0, help='Weight for Gradient (Sobel) loss')

    # Data parameters
    parser.add_argument('--data_dir', default='E:/vscode/2号CT数据', type=str, help='Dataset path')
    parser.add_argument('--num_workers', default=8, type=int)
    parser.add_argument('--clip_min', type=float, default=-1000.0, help='Minimum HU value for clipping')
    parser.add_argument('--clip_max', type=float, default=1000.0, help='Maximum HU value for clipping')

    # IO parameters
    parser.add_argument('--checkpoint_dir', default='checkpoints/mae_pretrain_featpred', help='Base path where to save checkpoints') # Updated default
    parser.add_argument('--log_dir', default='logs/mae_pretrain_featpred', help='Base path where to tensorboard log') # Updated default
    parser.add_argument('--save_interval', default=20, type=int, help='Save checkpoint every x epochs')
    parser.add_argument('--resume', default='', help='Resume from specific checkpoint file')

    args = parser.parse_args()

    pretrain_mae(
        data_dir=args.data_dir,
        batch_size=args.batch_size,
        num_workers=args.num_workers,
        epochs=args.epochs,
        lr=args.lr,
        warmup_epochs=args.warmup_epochs,
        weight_decay=args.weight_decay,
        use_amp=args.use_amp,
        gradient_accumulation_steps=args.gradient_accumulation_steps,
        img_size=args.img_size,
        patch_size=args.patch_size,
        mask_ratio=args.mask_ratio,
        masking_strategy=args.masking_strategy,
        block_size=args.block_size,
        model_embed_dim=args.model_embed_dim,
        model_depth=args.model_depth,
        model_num_heads=args.model_num_heads,
        decoder_embed_dim=args.decoder_embed_dim,
        decoder_depth=args.decoder_depth,
        decoder_num_heads=args.decoder_num_heads,
        clip_min=args.clip_min,
        clip_max=args.clip_max,
        norm_feat_loss=args.norm_feat_loss, # Pass feature norm flag
        # Removed auxiliary loss weights
        checkpoint_dir=args.checkpoint_dir,
        log_dir=args.log_dir,
        save_interval=args.save_interval,
        resume=args.resume
    )