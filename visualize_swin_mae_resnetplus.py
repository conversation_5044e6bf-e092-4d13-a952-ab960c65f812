import numpy as np
import torch
import matplotlib.pyplot as plt
from PIL import Image
import os
import argparse
import random
import cv2 # Added for image processing
import math # Added for calculations
from skimage.metrics import peak_signal_noise_ratio, structural_similarity # Added for metrics

# Import the model definition from the script used for training this checkpoint
from train_swin_mae_resnet_random_mask_perc_finalplus import MaskedAutoencoderSwinSimpleDecoder as MaskedAutoencoderSwin # Updated import
# from data.mae_loader import MAEDataset # Not strictly needed for visualization if we handle loading here
from torchvision import transforms
import torch.nn.functional as F # Needed for interpolate
import torch.nn as nn # Needed for nn.LayerNorm during model instantiation
import logging # Import logging

# Configure logging
logging.basicConfig(level=logging.INFO, format='%(asctime)s - %(levelname)s - %(message)s')


# --------------------------------------------------------
# Helper function for masking (copied from training script)
# --------------------------------------------------------
def random_masking(N, L, mask_ratio, device):
    """ Perform per-sample random masking by shuffling indices """
    len_keep = int(L * (1 - mask_ratio))
    noise = torch.rand(N, L, device=device)
    ids_shuffle = torch.argsort(noise, dim=1)
    ids_restore = torch.argsort(ids_shuffle, dim=1)
    # ids_keep = ids_shuffle[:, :len_keep] # Not needed by caller in this approach
    mask = torch.ones([N, L], device=device)
    mask[:, :len_keep] = 0
    # Unshuffle the mask to match the original patch order
    mask = torch.gather(mask, dim=1, index=ids_restore)
    return mask # mask is 1 for masked, 0 for visible


# --- Helper Functions from mae_loader.py (adapted) ---
# These are kept for potential future use or reference, but not used in the fixed patch extraction below.
def _find_valid_bbox(img_cv):
    """Finds the bounding box of the non-black region using OpenCV."""
    try:
        # Apply thresholding (Otsu's method or fixed)
        # Use a fixed threshold slightly above 0 for CT data if Otsu fails often
        ret, thresh = cv2.threshold(img_cv.astype(np.uint8), 1, 255, cv2.THRESH_BINARY) # Use fixed threshold > 0
        # thresh_value, thresh = cv2.threshold(img_cv, 0, 255, cv2.THRESH_BINARY + cv2.THRESH_OTSU)
        # logging.debug(f"Otsu threshold value: {thresh_value}")

        # Invert if the background is white (heuristic: check mean value) - Less likely for CT
        # if np.mean(thresh) > 128:
        #     logging.debug("Detected white background, inverting threshold.")
        #     thresh = cv2.bitwise_not(thresh)

        # Find contours
        contours, _ = cv2.findContours(thresh, cv2.RETR_EXTERNAL, cv2.CHAIN_APPROX_SIMPLE)

        if not contours:
            # Fallback: find non-zero pixels directly
            coords = np.column_stack(np.where(img_cv > img_cv.min())) # Find non-background pixels
            if coords.size == 0:
                logging.warning("Could not find contours or non-background pixels.")
                return None # Indicate failure
            y_min, x_min = coords.min(axis=0)
            y_max, x_max = coords.max(axis=0)
            logging.warning("No contours found, using pixel coordinate bounding box.")
        else:
            # Bounding box encompassing all contours
            all_points = np.concatenate(contours, axis=0)
            x_min, y_min, w, h = cv2.boundingRect(all_points)
            x_max = x_min + w
            y_max = y_min + h

        logging.debug(f"Detected bbox: x=[{x_min}, {x_max}], y=[{y_min}, {y_max}]")
        return x_min, y_min, x_max, y_max
    except Exception as e:
        logging.error(f"Error during OpenCV bounding box detection: {e}", exc_info=True)
        return None

def _get_random_crop_within_bbox(img_pil, bbox, crop_size, min_std_dev=5.0, max_crop_attempts=20):
    """Attempts to get a random crop within the bbox, checking std dev."""
    x_min, y_min, x_max, y_max = bbox
    valid_w, valid_h = x_max - x_min, y_max - y_min

    if valid_w < crop_size or valid_h < crop_size:
        logging.warning(f"Valid bbox ({valid_w}x{valid_h}) smaller than crop size ({crop_size}x{crop_size}). Skipping.")
        return None

    img_cropped_pil = None
    crop_success = False

    for attempt in range(max_crop_attempts):
        max_start_x = x_max - crop_size
        max_start_y = y_max - crop_size

        # Ensure start coordinates are within valid range (handle edge case max < min)
        start_x = random.randint(x_min, max(x_min, max_start_x))
        start_y = random.randint(y_min, max(y_min, max_start_y))

        left, top = start_x, start_y
        right, bottom = left + crop_size, top + crop_size

        img_cropped_pil_attempt = img_pil.crop((left, top, right, bottom))
        img_array = np.array(img_cropped_pil_attempt)
        std_val = np.std(img_array)

        if std_val > min_std_dev:
            logging.debug(f"Found good crop at attempt {attempt+1}, std={std_val:.2f}, coords=({left},{top})")
            img_cropped_pil = img_cropped_pil_attempt
            crop_success = True
            break
        else:
            logging.debug(f"Attempt {attempt+1}: Crop std dev too low ({std_val:.2f}) at ({left},{top}), retrying.")


    if not crop_success:
        logging.warning(f"Could not find crop with std > {min_std_dev} in {max_crop_attempts} attempts. Trying center crop of bbox.")
        # Fallback: Center crop the *valid bounding box*
        center_x = x_min + valid_w // 2
        center_y = y_min + valid_h // 2
        left = max(x_min, center_x - crop_size // 2)
        top = max(y_min, center_y - crop_size // 2)
        # Adjust if crop goes out of bounds (shouldn't happen if valid_w/h >= crop_size)
        if left + crop_size > x_max: left = x_max - crop_size
        if top + crop_size > y_max: top = y_max - crop_size
        right = left + crop_size
        bottom = top + crop_size
        logging.info(f"Falling back to center crop of valid area, coords: ({left},{top})")
        img_cropped_pil = img_pil.crop((left, top, right, bottom))
        # Final check on the fallback crop
        img_array = np.array(img_cropped_pil)
        fallback_std = np.std(img_array)
        if fallback_std <= min_std_dev:
             logging.warning(f"Center crop std dev ({fallback_std:.2f}) is still low. Visualization might be suboptimal.")

    return img_cropped_pil


# --- Original Visualization Functions ---

def show_image(image, title=''):
    """Helper function to display an image (handles 2D grayscale or 3D HWC), fixing display range."""
    # Ensure image is numpy array for consistent handling
    if isinstance(image, torch.Tensor):
        image = image.numpy()

    # Determine vmin and vmax based on dtype or content if needed, but default to [0, 1] for normalized float data
    vmin, vmax = 0, 1
    # Optional: Adjust for integer types if needed, e.g., if showing original uint8 patch directly
    # if image.dtype == np.uint8:
    #     vmin, vmax = 0, 255

    if image.ndim == 2:
        # Grayscale image (H, W)
        plt.imshow(image, cmap='gray', vmin=vmin, vmax=vmax)
    elif image.ndim == 3:
        # Assumes (H, W, C)
        assert image.shape[2] == 1 or image.shape[2] == 3, f"Expected 1 or 3 channels, got shape {image.shape}"
        # Squeeze potential single channel dimension for imshow
        img_to_show = image.squeeze()
        # If still 3D after squeeze (e.g., RGB), imshow handles it. If 2D, cmap applies.
        plt.imshow(img_to_show, cmap='gray', vmin=vmin, vmax=vmax)
    else:
        raise ValueError(f"Unsupported image shape: {image.shape}")

    plt.title(title, fontsize=10)
    plt.axis('off')
    return

# Modified function signature to accept patch_hu_min/max for denormalization
def run_one_image(img_tensor_normalized_minus1_1, model, mask_ratio, device, patch_hu_min, patch_hu_max): # Changed hu_min/max to patch_hu_min/max
    """
    Run Swin-MAE model on a single image tensor normalized to [-1, 1] based on ITS OWN patch range.
    Returns the reconstruction denormalized back to the PATCH HU range [patch_hu_min, patch_hu_max] # Updated docstring
    and the binary mask (1 = masked/removed, 0 = visible).
    """
    x = img_tensor_normalized_minus1_1.unsqueeze(0) # Add batch dimension [1, C, H, W]
    x = x.to(device, non_blocking=True)
    N, C_in, H_initial, W_initial = x.shape
    logging.info(f"run_one_image - Input tensor (x) shape: {x.shape}")
    logging.info(f"run_one_image - Input tensor (x) range (normalized [-1,1] using PATCH range): min={x.min():.4f}, max={x.max():.4f}") # Clarified normalization source

    # Run Swin-MAE forward pass to get prediction and mask
    with torch.no_grad():
        # 1. Encoder - Call forward_encoder (returns latent and mask in the new model)
        latent_full, mask_binary = model.forward_encoder(x, mask_ratio) # Shape: [N, L_final, D_encoder_output], [N, L_initial]

        # 2. No need to generate random mask as it's returned by forward_encoder
        # mask_binary is already [N, L_initial], 1 for masked, 0 for visible
        # For reference: model.num_patches_initial is the equivalent of model.num_patches in the old model

        # 3. Decoder - Project latent features and decode at low resolution
        # 直接使用forward_decoder，它内部会调用decoder_embed_proj进行维度转换
        pred_final = model.forward_decoder(latent_full) # Shape: [N, L_final, p*p*C]

        # 4. Upsample prediction to initial patch resolution (like original train_swin_mae.py)
        N, L_final, C_pred = pred_final.shape
        # In the new model, grid_final is called grid_final_encoder
        H_final, W_final = model.grid_final_encoder # e.g., (8, 8)
        p = model.patch_size
        C_pix = C_pred // (p**2) # Should be model.in_chans

        assert L_final == H_final * W_final, "L_final mismatch"
        assert C_pred == p**2 * C_pix, "Prediction channel mismatch"

        # Reshape to spatial: [N, H_final, W_final, p, p, C_pix]
        pred_spatial = pred_final.reshape(N, H_final, W_final, p, p, C_pix)
        # Permute to [N, C_pix, H_final, W_final, p, p]
        pred_spatial = pred_spatial.permute(0, 5, 1, 2, 3, 4)
        # Combine patch dims with spatial dims: [N, C_pix, H_final*p, W_final*p]
        pred_low_res_img = pred_spatial.reshape(N, C_pix, H_final * p, W_final * p)

        # Upsample to original image resolution H, W
        pred_img = F.interpolate(
            pred_low_res_img,
            size=(H_initial, W_initial),
            mode='bilinear', # or 'nearest'
            align_corners=False
        ) # Shape: [N, C_pix, H_initial, W_initial]

        # --- Post-processing for visualization ---
        # y_pred_norm is the predicted image [N, C, H, W] in the normalized space [-1, 1]
        y_pred_norm = pred_img.detach().cpu() # Use the upsampled image

        # We now have mask_binary generated above [N, L_initial]
        # We need to unpatchify it for visualization
        mask_img = mask_binary.unsqueeze(-1).repeat(1, 1, model.patch_size**2 * C_in) # (N, L, p*p*C)
        mask_img = model.unpatchify_from_initial_resolution(mask_img)  # Shape: [N, C, H, W], 1 is removing, 0 is keeping
        mask_img = mask_img[:, 0:1, :, :] # Shape: [N, 1, H, W]

    # y_pred_norm is the predicted image [N, C, H, W] in the normalized space [-1, 1]
    # y_pred_norm = y_pred_norm.detach().cpu() # Moved up
    logging.info(f"run_one_image - Reconstruction (y_pred_norm) shape: {y_pred_norm.shape}")
    logging.info(f"run_one_image - Reconstruction (y_pred_norm) range (normalized [-1,1]): min={y_pred_norm.min():.4f}, max={y_pred_norm.max():.4f}")

    # Denormalize reconstruction back to PATCH HU scale using provided patch_hu_min, patch_hu_max
    # Inverse of x_norm = (x_hu - patch_hu_mean) / patch_hu_half_range
    # x_hu = x_norm * patch_hu_half_range + patch_hu_mean
    patch_hu_mean = (patch_hu_max + patch_hu_min) / 2.0 # Use function arguments
    patch_hu_half_range = (patch_hu_max - patch_hu_min) / 2.0 # Use function arguments
    if patch_hu_half_range == 0: # Avoid division by zero if patch_hu_min == patch_hu_max
        logging.warning("Patch HU min and max are equal, denormalizing to patch_hu_min.")
        recon_hu = torch.full_like(y_pred_norm, patch_hu_min) # Use function argument
    else:
        recon_hu = y_pred_norm * patch_hu_half_range + patch_hu_mean

    logging.info(f"run_one_image - Denormalized Reconstruction (recon_hu) range (Patch HU scale [{patch_hu_min:.2f}, {patch_hu_max:.2f}]): min={recon_hu.min():.2f}, max={recon_hu.max():.2f}")

    # Prepare mask for return (binary: 1 = masked, 0 = visible)
    # mask_binary is already [N, L_initial]
    mask_binary = mask_binary.detach().cpu()
    # Unpatchify the mask to image shape [N, 1, H, W]
    mask_img = mask_binary.unsqueeze(-1).repeat(1, 1, model.patch_size**2 * C_in) # (N, L, p*p*C)
    mask_img = model.unpatchify_from_initial_resolution(mask_img)  # Shape: [N, C, H, W], 1 is removing, 0 is keeping
    # Keep only one channel for the mask image
    mask_img = mask_img[:, 0:1, :, :] # Shape: [N, 1, H, W]

    # Return reconstruction in HU scale and the binary mask image
    return recon_hu, mask_img # Shape: [1, C, H, W], [1, 1, H, W]

# Renamed function and updated docstring
def visualize_swin_mae_reconstruction(model_path, image_path, output_dir, hu_min, hu_max, mask_ratios, args):
    """
    Loads Swin-MAE model, runs inference on a center patch of an image for multiple mask ratios.
    Input to model is normalized using the PATCH's HU range.
    Output from model is denormalized using the GLOBAL HU range (hu_min, hu_max).
    Visualization is displayed using either the GLOBAL HU range or the PATCH HU range,
    controlled by the --vis_hu_mode argument.
    """
    device = torch.device('cuda' if torch.cuda.is_available() else 'cpu')
    logging.info(f"Using device: {device}")
    # Note: Denormalization uses PATCH HU range (passed to run_one_image).
    # Global hu_min/hu_max are only used for optional global visualization range.

    # --- Load Model ---
    logging.info(f"Loading model checkpoint from: {model_path}")
    if not os.path.exists(model_path):
        logging.error(f"Checkpoint file not found: {model_path}")
        return

    try:
        checkpoint = torch.load(model_path, map_location='cpu')
        # Try to get args from checkpoint, handle if 'args' key is missing
        if 'args' in checkpoint and checkpoint['args'] is not None:
             ckpt_args = checkpoint['args']
             # If ckpt_args is a Namespace object, convert it to dict
             if isinstance(ckpt_args, argparse.Namespace):
                 ckpt_args = vars(ckpt_args)
             elif not isinstance(ckpt_args, dict):
                 logging.warning(f"Checkpoint 'args' is of unexpected type: {type(ckpt_args)}. Trying to proceed.")
                 ckpt_args = {} # Fallback to empty dict
        else:
             logging.warning("Checkpoint does not contain 'args'. Using default model parameters.")
             ckpt_args = {} # Fallback to empty dict

        logging.info(f"Checkpoint args: {ckpt_args}")


        # --- Load Swin-MAE specific parameters from checkpoint ---
        # Provide default values for all expected parameters
        loaded_img_size = ckpt_args.get('img_size', 256)
        loaded_patch_size = ckpt_args.get('patch_size', 4) # Swin default
        swin_embed_dim = ckpt_args.get('swin_embed_dim', 96)
        swin_depths = ckpt_args.get('swin_depths', [2, 2, 6, 2])
        swin_num_heads = ckpt_args.get('swin_num_heads', [3, 6, 12, 24])
        swin_window_size = ckpt_args.get('swin_window_size', 7)
        swin_ape = ckpt_args.get('swin_ape', True) # Absolute Position Embedding flag
        decoder_embed_dim = ckpt_args.get('decoder_embed_dim', 512)
        decoder_depth = ckpt_args.get('decoder_depth', 8)
        decoder_num_heads = ckpt_args.get('decoder_num_heads', 16)
        norm_pix_loss = ckpt_args.get('norm_pix_loss', False)
        # Get masking_window_size from checkpoint args, fallback if not present
        # masking_window_size = ckpt_args.get('masking_window_size', 4) # Not needed for this model version
        perceptual_loss_weight = ckpt_args.get('perceptual_loss_weight', 0.0) # Default to 0 if not in ckpt


        # Use the parameters from the checkpoint for model definition.
        img_size = loaded_img_size
        patch_size = loaded_patch_size # Store for later use if needed
        logging.info(f"Using Swin-MAE parameters from checkpoint: img_size={img_size}, patch_size={patch_size}, swin_embed_dim={swin_embed_dim}, swin_depths={swin_depths}, swin_num_heads={swin_num_heads}, swin_window_size={swin_window_size}, swin_ape={swin_ape}, decoder_embed_dim={decoder_embed_dim}, decoder_depth={decoder_depth}, decoder_num_heads={decoder_num_heads}, norm_pix_loss={norm_pix_loss}, perceptual_loss_weight={perceptual_loss_weight}") # Removed masking_window_size

        # --- Instantiate Swin-MAE model ---
        # Ensure all necessary arguments for MaskedAutoencoderSwinSimpleDecoder are provided
        model = MaskedAutoencoderSwin(
            img_size=img_size,
            patch_size=patch_size,
            in_chans=1, # Assuming grayscale input
            swin_embed_dim=swin_embed_dim, # Changed from embed_dim to swin_embed_dim
            swin_depths=swin_depths, # Changed from depths to swin_depths
            swin_num_heads=swin_num_heads, # Changed from num_heads to swin_num_heads
            swin_window_size=swin_window_size, # Changed from window_size to swin_window_size
            mlp_ratio=4.0, # Standard MLP ratio
            norm_layer=nn.LayerNorm,
            swin_ape=swin_ape, # Changed from ape to swin_ape
            swin_patch_norm=True, # Changed from patch_norm to swin_patch_norm
            decoder_embed_dim=decoder_embed_dim,
            decoder_depth=decoder_depth,
            decoder_num_heads=decoder_num_heads,
            norm_pix_loss=norm_pix_loss,
            perceptual_loss_weight=perceptual_loss_weight # Pass perceptual loss weight
        ).to(device)

        # --- Load state dict carefully ---
        state_dict = checkpoint['model_state_dict']
        new_state_dict = {}
        for k, v in state_dict.items():
            name = k[7:] if k.startswith('module.') else k # remove module. prefix
            new_state_dict[name] = v
        msg = model.load_state_dict(new_state_dict, strict=False)
        logging.info(f"Load state dict message: {msg}")
        # If strict=False shows missing/unexpected keys, investigate further.

        model.eval()
        logging.info("Model loaded successfully.")
    except Exception as e:
        logging.error(f"Failed to load model checkpoint: {e}", exc_info=True)
        return

    # --- Load and Preprocess Image ---
    logging.info(f"Loading image from: {image_path}")
    if not os.path.exists(image_path):
        logging.error(f"Image file not found: {image_path}")
        return

    try: # Ensure this line has correct indentation relative to the function
        # --- Load TIF using cv2.imdecode to handle non-ASCII paths ---
        try: # Ensure this line has correct indentation relative to the outer try
            with open(image_path, 'rb') as f:
                file_bytes = np.frombuffer(f.read(), dtype=np.uint8)
            img_cv = cv2.imdecode(file_bytes, cv2.IMREAD_UNCHANGED)
            if img_cv is None:
                raise ValueError("cv2.imdecode returned None")
        except Exception as e_load:
            logging.error(f"Failed to load image using cv2.imdecode: {image_path}. Error: {e_load}", exc_info=True)
            return
        logging.info(f"Loaded image with cv2.imdecode: shape={img_cv.shape}, dtype={img_cv.dtype}")

        # Log original image HU range (before any processing)
        min_val_orig, max_val_orig = np.min(img_cv), np.max(img_cv)
        logging.info(f"Original full image HU range: min={min_val_orig}, max={max_val_orig}, dtype={img_cv.dtype}")

        # Handle potential multi-channel TIF (take first channel if grayscale)
        if img_cv.ndim == 3:
            logging.warning(f"Loaded TIF has multiple channels ({img_cv.shape[2]}), using the first channel.")
            img_cv = img_cv[:, :, 0]

        original_h, original_w = img_cv.shape[:2] # Get dimensions from cv2 image
        logging.info(f"Original image dimensions (H, W): {original_h}x{original_w}")


        # --- Extract Center Patch ---
        # Use img_size determined from the checkpoint
        center_x = original_w // 2
        center_y = original_h // 2
        logging.info(f"Image center: ({center_x}, {center_y})")

        # Calculate top-left corner for the center patch
        left = center_x - img_size // 2
        top = center_y - img_size // 2
        right = left + img_size
        bottom = top + img_size

        # Ensure coordinates are within bounds (important if img_size > original dimensions)
        left = max(0, left)
        top = max(0, top)
        right = min(original_w, right)
        bottom = min(original_h, bottom)
        # Adjust size if clipping occurred (though ideally img_size <= original dimensions)
        actual_crop_width = right - left
        actual_crop_height = bottom - top
        if actual_crop_width != img_size or actual_crop_height != img_size:
             logging.warning(f"Center patch extraction resulted in size {actual_crop_width}x{actual_crop_height}, expected {img_size}x{img_size}. Check if img_size is larger than image dimensions.")
             # Handle this case: maybe resize the extracted patch or raise an error
             # For now, proceed with the extracted patch, but it might cause issues downstream if size mismatch is critical
             # If resizing is needed:
             # img_cropped_pil = img_pil.crop((left, top, right, bottom)).resize((img_size, img_size), Image.Resampling.LANCZOS)
             # logging.info(f"Resized extracted patch to {img_size}x{img_size}")
        # else:
        # Extract patch from the OpenCV numpy array (still in original HU units)
        img_cropped_np = img_cv[top:bottom, left:right].astype(np.float32) # Ensure float32

        logging.info(f"Extracted center patch {img_size}x{img_size} from area: ({left}, {top}, {right}, {bottom})")

        # Log original patch HU values
        patch_min_hu, patch_max_hu = np.min(img_cropped_np), np.max(img_cropped_np)
        patch_min_hu, patch_max_hu = np.min(img_cropped_np), np.max(img_cropped_np)
        logging.info(f"Original Patch HU range: min={patch_min_hu:.2f}, max={patch_max_hu:.2f}, dtype={img_cropped_np.dtype}")
        # Log for denorm range moved inside run_one_image

        # --- Determine Visualization Range based on args.vis_hu_mode ---
        if args.vis_hu_mode == 'patch':
            display_vmin = patch_min_hu
            display_vmax = patch_max_hu
            vis_range_str = f"Patch HU: [{display_vmin:.0f}, {display_vmax:.0f}]"
            logging.info(f"Using PATCH HU range for visualization: [{display_vmin:.2f}, {display_vmax:.2f}]")
        elif args.vis_hu_mode == 'global':
            display_vmin = hu_min # Use global range passed as argument
            display_vmax = hu_max
            vis_range_str = f"Global HU: [{display_vmin:.0f}, {display_vmax:.0f}]"
            logging.info(f"Using GLOBAL HU range for visualization: [{display_vmin:.2f}, {display_vmax:.2f}]")
        else: # Should not happen with choices in argparse
             logging.error(f"Invalid vis_hu_mode: {args.vis_hu_mode}. Defaulting to global.")
             display_vmin = hu_min
             display_vmax = hu_max
             vis_range_str = f"Global HU: [{display_vmin:.0f}, {display_vmax:.0f}]"


        # --- Normalize patch to [-1, 1] FOR MODEL INPUT using PATCH HU range ---
        # Formula: x_norm = (x_hu - patch_hu_mean) / patch_hu_half_range
        patch_hu_mean = (patch_max_hu + patch_min_hu) / 2.0
        patch_hu_half_range = (patch_max_hu - patch_min_hu) / 2.0

        if patch_hu_half_range == 0:
            logging.warning("Patch HU min and max are equal. Normalizing input to 0.")
            img_tensor_minus1_1 = torch.zeros_like(torch.from_numpy(img_cropped_np)).unsqueeze(0) # Shape [1, H, W]
        else:
            img_normalized_for_model_np = (img_cropped_np - patch_hu_mean) / patch_hu_half_range
            # Clamp to [-1, 1] - should already be in this range if calculated correctly
            img_normalized_for_model_np = np.clip(img_normalized_for_model_np, -1.0, 1.0)
            img_tensor_minus1_1 = torch.from_numpy(img_normalized_for_model_np).unsqueeze(0) # Shape [1, H, W]

        logging.info(f"Patch range AFTER normalization using PATCH range to [-1,1] for model input: min={img_tensor_minus1_1.min():.4f}, max={img_tensor_minus1_1.max():.4f}")

        # NOTE: Visualization normalization uses display_vmin/vmax determined above
        # NOTE: Denormalization inside run_one_image uses global hu_min/hu_max

    except Exception as e:
        logging.error(f"Exception during image loading or processing: {e}", exc_info=True)
        return

    # --- Run Inference and Visualization for each mask ratio ---
    num_ratios = len(mask_ratios)
    num_cols = 5 # Original, Masked, Recon, Paste, Difference
    plt.style.use('seaborn-v0_8-whitegrid')
    plt.rcParams.update({
        'font.family': 'serif', 'font.serif': ['Times New Roman'],
        'figure.figsize': (num_cols * 2.5, num_ratios * 2.7), # Adjusted figsize
        'figure.dpi': 150,
        'font.size': 8, 'axes.titlesize': 10, # Slightly smaller fonts
    })
    fig = plt.figure()

    logging.info(f"Running MAE inference for mask ratios: {mask_ratios}")
    for i, mask_ratio in enumerate(mask_ratios):
        logging.info(f"  Processing mask ratio: {mask_ratio}")
        try:
            # Get reconstruction in HU scale (using patch range) and the binary mask
            # Pass the actual patch_min_hu and patch_max_hu calculated earlier
            recon_hu_tensor, mask_binary_tensor = run_one_image(img_tensor_minus1_1, model, mask_ratio, device, patch_min_hu, patch_max_hu) # Corrected variable names
            # Squeeze batch dimension, keep channel for recon, squeeze channel for mask
            recon_hu_np = recon_hu_tensor.squeeze(0).permute(1, 2, 0).numpy() # Shape [H, W, C] in HU scale
            mask_binary_np = mask_binary_tensor.squeeze(0).squeeze(0).numpy() # Shape [H, W], 1=masked
            # Ensure recon_hu_np is [H, W] if input was grayscale
            if recon_hu_np.shape[2] == 1:
                recon_hu_np = recon_hu_np.squeeze(axis=2) # Shape [H, W]
        except Exception as e:
            logging.error(f"Error during model inference for ratio {mask_ratio}: {e}", exc_info=True)
            continue # Skip this ratio if inference fails

        # --- Create derived images for visualization (using original HU values) ---
        # Masked image: Show original where visible, use display_vmin for masked areas for consistent visualization
        masked_display = img_cropped_np * (1 - mask_binary_np) + display_vmin * mask_binary_np

        # Paste image (Reconstruction + Visible) in HU scale
        paste_hu = img_cropped_np * (1 - mask_binary_np) + recon_hu_np * mask_binary_np

        # Difference map (Absolute difference in HU units in masked areas)
        diff_map_hu = np.abs(img_cropped_np - recon_hu_np) * mask_binary_np
        diff_max_val = np.max(diff_map_hu) if np.any(mask_binary_np) else 0 # Max difference in masked area
        logging.info(f"  Max absolute difference in masked area (HU): {diff_max_val:.2f}") # Corrected indentation

        # --- Calculate PSNR and SSIM --- # Corrected indentation block
        # Use the visualization range (display_vmin, display_vmax) as the data_range for consistency
        data_range_metrics = display_vmax - display_vmin
        if data_range_metrics <= 0:
            logging.warning(f"  Data range for metrics is non-positive ({data_range_metrics:.2f}), skipping PSNR/SSIM calculation.")
            psnr_val = float('nan')
            ssim_val = float('nan')
        else:
            try:
                # Ensure both images are float type for metrics calculation
                psnr_val = peak_signal_noise_ratio(img_cropped_np.astype(np.float32), recon_hu_np.astype(np.float32), data_range=data_range_metrics)
                ssim_val = structural_similarity(img_cropped_np.astype(np.float32), recon_hu_np.astype(np.float32), data_range=data_range_metrics)
                logging.info(f"  Metrics (data_range={data_range_metrics:.2f}): PSNR = {psnr_val:.4f}, SSIM = {ssim_val:.4f}")
            except Exception as e_metrics:
                logging.error(f"  Error calculating metrics for ratio {mask_ratio}: {e_metrics}", exc_info=True)
                psnr_val = float('nan')
                ssim_val = float('nan')

        # --- Plotting for this ratio ---
        plot_row_start = i * num_cols + 1

        # display_vmin, display_vmax determined earlier based on args.vis_hu_mode

        # 1. Original Patch (Displayed using determined HU range)
        plt.subplot(num_ratios, num_cols, plot_row_start)
        plt.imshow(img_cropped_np, cmap='gray', vmin=display_vmin, vmax=display_vmax)
        plt.title(f"Original Patch\n(Vis Range: {vis_range_str})")
        plt.axis('off')

        # 2. Masked Patch (Displayed using determined HU range, masked areas = display_vmin)
        # Note: masked_hu calculation still uses global hu_min for the fill value,
        # but the display range (vmin, vmax) is controlled by display_vmin/vmax.
        # If using patch range for display, masked areas might not be the minimum color if patch_min_hu > global hu_min.
        # Let's adjust masked_hu to use display_vmin as the fill value for consistency in visualization.
        # masked_display = img_cropped_np * (1 - mask_binary_np) + display_vmin * mask_binary_np # Moved calculation up
        plt.subplot(num_ratios, num_cols, plot_row_start + 1)
        plt.imshow(masked_display, cmap='gray', vmin=display_vmin, vmax=display_vmax)
        plt.title(f"Masked ({int(mask_ratio*100)}%)\n(Masked = {display_vmin:.0f} HU)")
        plt.axis('off')

        # 3. Reconstruction (Displayed using determined HU range)
        plt.subplot(num_ratios, num_cols, plot_row_start + 2)
        plt.imshow(recon_hu_np, cmap='gray', vmin=display_vmin, vmax=display_vmax)
        # Add metrics to the title
        plt.title(f"Reconstruction\n(Vis: {vis_range_str})\nPSNR: {psnr_val:.2f}, SSIM: {ssim_val:.3f}")
        plt.axis('off')

        # 4. Reconstruction + Visible (Paste) (Displayed using determined HU range)
        plt.subplot(num_ratios, num_cols, plot_row_start + 3)
        plt.imshow(paste_hu, cmap='gray', vmin=display_vmin, vmax=display_vmax)
        plt.title(f"Recon + Visible\n(Vis Range: {vis_range_str})")
        plt.axis('off')

        # 5. Difference Map (Absolute HU difference, scaled 0 to max_diff)
        plt.subplot(num_ratios, num_cols, plot_row_start + 4)
        # Use a different colormap and scale based on actual difference range
        im_diff = plt.imshow(diff_map_hu, cmap='viridis', vmin=0, vmax=max(1, diff_max_val)) # Avoid vmax=0
        plt.title(f"Abs Difference (HU)\n(Max: {diff_max_val:.0f})")
        plt.axis('off')
        # Add a colorbar for the difference map
        # fig.colorbar(im_diff, ax=plt.gca(), fraction=0.046, pad=0.04) # Optional colorbar


    plt.tight_layout(pad=0.5, h_pad=1.5) # Adjust padding

    # --- Save Visualization ---
    os.makedirs(output_dir, exist_ok=True)
    ratios_str = "_".join(map(str, mask_ratios)).replace('.', 'p')
    # Include visualization range type and values in filename
    vis_type_str = args.vis_hu_mode + "vis" # e.g., "patchvis" or "globalvis"
    output_filename = f"swin_mae_reconstruction_{vis_type_str}_{display_vmin:.0f}_{display_vmax:.0f}_ratios_{ratios_str}_{os.path.basename(image_path).split('.')[0]}.png" # Added swin_mae prefix
    output_path = os.path.join(output_dir, output_filename)
    try:
        plt.savefig(output_path, bbox_inches='tight')
        logging.info(f"Visualization saved to: {output_path}")
    except Exception as e:
        logging.error(f"Failed to save visualization: {e}", exc_info=True)
    finally:
        plt.close(fig)


if __name__ == '__main__':
    parser = argparse.ArgumentParser('Swin-MAE Visualization script (Patch Norm/Denorm, Configurable Vis)') # Simplified description
    parser.add_argument('--model_path', default='checkpoints/train_swin_mae_resnet_random_mask_perc_final0.05-masking/best_model.pth', type=str, help='Path to Swin-MAE pretrained model checkpoint') # Updated default
    parser.add_argument('--image_path', default='E:/vscode/2号CT数据/19p44um-2700x2700x2000-0750.tif', type=str, help='Path to the input image file') # Updated path
    parser.add_argument('--output_dir', default='plots/swin_mae_visualization', type=str, help='Directory to save visualization plots') # Updated default
    parser.add_argument('--hu_min', type=float, default=-1000.0, help='Minimum GLOBAL HU value used ONLY for global visualization range (if --vis_hu_mode global). Normalization/Denormalization always uses patch range.') # Clarified help
    parser.add_argument('--hu_max', type=float, default=1000.0, help='Maximum GLOBAL HU value used ONLY for global visualization range (if --vis_hu_mode global). Normalization/Denormalization always uses patch range.') # Clarified help
    # parser.add_argument('--use_patch_hu_range', action='store_true', help='If set, use the specific HU range of the extracted center patch for visualization instead of global hu_min/hu_max.') # Replaced by vis_hu_mode
    parser.add_argument('--vis_hu_mode', type=str, default='patch', choices=['global', 'patch'], help="Which HU range to use for visualization: 'global' (uses --hu_min/--hu_max) or 'patch' (uses the actual range of the extracted patch). Default is 'patch'.") # Changed default to patch
    # img_size and patch_size are determined from the checkpoint, no need for args here
    parser.add_argument('--mask_ratios', default=[0.75], type=float, nargs='+', help='List of masking ratios for visualization (e.g., 0.5 0.75 0.9)')


    args = parser.parse_args()

    # Determine output directory based on visualization range choice
    vis_type_str = args.vis_hu_mode + "vis" # e.g., "patchvis" or "globalvis"
    output_dir_final = os.path.join(args.output_dir, f"swin_mae_visualization_{vis_type_str}") # Added swin_mae prefix


    # img_size and patch_size will be read from the checkpoint inside the function
    # Pass the full args object to the function so it can access vis_hu_mode
    visualize_swin_mae_reconstruction( # Renamed function call
        model_path=args.model_path,
        image_path=args.image_path,
        output_dir=output_dir_final, # Use the adjusted output directory
        hu_min=args.hu_min, # Global range ONLY for optional global vis
        hu_max=args.hu_max, # Global range ONLY for optional global vis
        # img_size determined internally
        # patch_size determined internally
        # patch hu range for denorm determined internally
        mask_ratios=args.mask_ratios,
        args=args # Pass the args object
    )
    print("Visualization script finished.") # Added print statement to ensure file ends cleanly
