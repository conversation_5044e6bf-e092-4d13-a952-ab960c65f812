# Balanced Configuration for Conditional Diffusion Super-Resolution Training
# Fixed GAN training imbalance issues

# --- Data Configuration ---
data:
  lr_dir: 'data/2号CT数据'
  hr_dir: 'data/3号CT数据'
  patch_size_hr: 128
  scale_factor: 4
  
  # BSRGAN-style Degradation Parameters
  blur_prob: 1.0
  downsample_prob: 1.0
  noise_prob: 1.0
  shuffle_prob: 0.5
  blur_kernel_size: 21
  blur_sigma_range: [0.2, 3.0]
  aniso_prob: 0.5
  downsample_methods: ['bicubic', 'bilinear', 'nearest']
  noise_gaussian_sigma_range: [1, 25]

# --- Model Configuration ---
model:
  in_channels: 1
  out_channels: 1
  base_channels: 128
  channel_mults: [1, 2, 2, 4]
  attention_resolutions: [16, 8]
  num_res_blocks: 2
  dropout: 0.1
  
  # Pretrained Encoder Settings (disabled for baseline)
  use_pretrained_encoder: False
  encoder_type: "Swin-MAE"
  encoder_checkpoint: null
  condition_method: 'CrossAttention'
  num_heads: 8

# --- Discriminator Configuration (Balanced) ---
discriminator:
  ndf: 64
  n_layers: 3
  lr: 5.0e-5  # Reduced discriminator learning rate

# --- Diffusion Process Configuration ---
diffusion:
  schedule_name: 'cosine'
  timesteps: 1000

# --- Training Configuration (Balanced) ---
training:
  log_root: './logs/sr_diffusion'
  checkpoint_root: './checkpoints/sr_diffusion'
  learning_rate: 1.0e-4
  weight_decay: 1.0e-4
  batch_size: 8
  epochs: 200
  
  # Balanced Loss Weights
  diffusion_loss_type: 'l1'
  perceptual_loss_weight: 0.1
  perceptual_loss_type: 'l1'
  ssim_loss_weight: 0.2
  gradient_loss_weight: 0.1
  
  # Fixed GAN Settings
  use_gan: True
  gan_loss_weight: 0.005  # Much lower weight for stability
  
  # Training Optimization
  use_amp: True
  seed: 42
  num_workers: 4
  log_interval: 1
  save_interval: 10
  
  # Learning Rate Scheduling
  use_scheduler: True
  scheduler_type: 'cosine'
  warmup_epochs: 20
  
  # Gradient Optimization
  grad_clip_norm: 1.0
  gradient_accumulation_steps: 2

# --- Inference Configuration ---
inference:
  sampling_steps: 50
  eta: 0.0

# --- Experiment Tracking ---
experiment:
  name: "SwinMAE_Baseline_Balanced_v1"
  description: "Baseline diffusion SR with balanced GAN training"
  tags: ["diffusion", "super-resolution", "balanced-gan", "ct-images"]
