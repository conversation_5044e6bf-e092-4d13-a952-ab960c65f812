# Sandstone CT Configuration - 砂砾岩CT图像专用配置
# 针对岩石CT成像特性优化的降质和训练参数

# --- Data Configuration ---
data:
  lr_dir: 'data/2号CT数据'
  hr_dir: 'data/3号CT数据'
  patch_size_hr: 128
  scale_factor: 4

  # 砂砾岩CT专用降质方法（平衡速度和质量）
  degradation_method: 'balanced_sandstone_ct'

  # 降质强度控制
  degradation_intensity: 'medium'          # 选项: 'light', 'medium', 'heavy'
  use_gpu_acceleration: True               # 启用GPU加速降质

  # 砂砾岩CT特异性参数
  rock_noise_prob: 0.9                    # 高概率噪声（岩石CT噪声较多）
  rock_artifact_prob: 0.7                 # 高概率伪影（射束硬化、环形伪影等）
  rock_blur_prob: 0.5                     # 适中的模糊概率

  # 岩石CT噪声参数
  quantum_photon_range: [200, 1000]       # 量子噪声光子计数范围
  scatter_intensity_range: [0.02, 0.08]   # 散射噪声强度范围
  electronic_noise_range: [1.0, 6.0]      # 电子噪声范围

  # 岩石CT伪影参数
  beam_hardening_intensity: [0.08, 0.15]  # 射束硬化强度（比一般CT更强）
  ring_artifact_intensity: [0.03, 0.12]   # 环形伪影强度
  ring_frequency_range: [0.05, 0.4]       # 环形伪影频率范围
  partial_volume_intensity: [0.02, 0.08]  # 部分容积效应强度

  # 岩石CT模糊参数
  reconstruction_blur_sigma: [0.2, 0.8]   # 重建模糊范围
  partial_volume_blur_sigma: [0.3, 1.0]   # 部分容积模糊范围

# --- Model Configuration ---
model:
  in_channels: 1
  out_channels: 1
  base_channels: 128
  channel_mults: [1, 2, 2, 4]
  attention_resolutions: [16, 8]
  num_res_blocks: 2
  dropout: 0.1

  # Pretrained Encoder Settings (disabled for baseline)
  use_pretrained_encoder: False
  encoder_type: "Swin-MAE"
  encoder_checkpoint: null
  condition_method: 'CrossAttention'
  num_heads: 8

# --- Discriminator Configuration (Disabled) ---
discriminator:
  ndf: 64
  n_layers: 3
  lr: 1.0e-4

# --- Diffusion Process Configuration ---
diffusion:
  schedule_name: 'cosine'
  timesteps: 1000

# --- Training Configuration ---
training:
  log_root: './logs/sr_diffusion'
  checkpoint_root: './checkpoints/sr_diffusion'
  learning_rate: 8.0e-5                   # 降低学习率，基于预训练成功经验
  weight_decay: 1.0e-4
  batch_size: 8
  epochs: 200

  # 基于预训练最佳配置优化的损失权重
  diffusion_loss_type: 'l1'
  perceptual_loss_weight: 0.10            # 略减感知损失，避免过度约束
  perceptual_loss_type: 'l1'
  ssim_loss_weight: 0.35                  # 增加SSIM权重，重视结构保持
  gradient_loss_weight: 0.20              # 增加梯度损失，保持孔隙边界清晰

  # GAN Disabled
  use_gan: False
  gan_loss_weight: 0.0

  # Training Optimization (基于预训练成功经验 + 速度优化)
  use_amp: True
  seed: 42                                # 保持与预训练一致的随机种子
  num_workers: 8                          # 增加数据加载进程，提高GPU利用率
  log_interval: 25                        # 增加日志频率，更好监控
  save_interval: 5                        # 增加保存频率，防止丢失

  # Learning Rate Scheduling (参考预训练成功配置)
  use_scheduler: True
  scheduler_type: 'cosine'
  warmup_epochs: 30                       # 增加预热轮数，参考预训练40轮经验

  # Gradient Optimization (提高训练稳定性)
  grad_clip_norm: 1.0
  gradient_accumulation_steps: 4          # 增加累积步数，提高稳定性

# --- Inference Configuration ---
inference:
  sampling_steps: 50
  eta: 0.0

# --- Experiment Tracking ---
experiment:
  name: "Sandstone_CT_Adaptive_v1"
  description: "Diffusion SR with sandstone CT-specific degradation modeling"
  tags: ["diffusion", "super-resolution", "sandstone-ct", "rock-imaging", "adaptive-degradation"]

# --- Sandstone CT Specific Notes ---
# 砂砾岩CT图像特点：
# 1. 高对比度：矿物颗粒 vs 孔隙空间
# 2. 多尺度特征：微米级孔隙到毫米级颗粒
# 3. 复杂几何：不规则孔隙网络
# 4. 特殊伪影：射束硬化、环形伪影、部分容积效应
# 5. 噪声特性：量子噪声、散射噪声、电子噪声
