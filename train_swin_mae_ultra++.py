import torch
import torch.nn as nn
import torch.nn.functional as F
import torch.optim as optim
from torch.optim.lr_scheduler import CosineAnnealingLR
from data.mae_loader import get_mae_loader # Import loader
import timm # Import timm
assert timm.__version__ >= "0.3.2" # MAE requires timm support, assert version
# Use timm's Block for ViT-style decoder
from timm.models.vision_transformer import Block # Changed to ViT Block
# from timm.models.swin_transformer import SwinTransformerBlock # Removed Swin Block import
from tqdm import tqdm
import os
import numpy as np
import matplotlib.pyplot as plt
from torch.utils.tensorboard import SummaryWriter
import math
import random
from util.pos_embed import get_2d_sincos_pos_embed # Assuming pos embed helper is in util
# import util.lr_sched as lr_sched # Keep import for now, even if not directly used
import torchvision.models as models
import torchvision.transforms as transforms
import argparse
from pytorch_msssim import ssim, ms_ssim # Import SSIM and MS-SSIM
from timm.models.layers import DropPath, Mlp


# Helper Modules for Hierarchical Decoder with Cross-Attention

# Basic Cross Attention Block
class CrossAttention(nn.Module):
    def __init__(self, dim, num_heads=8, qkv_bias=False, attn_drop=0., proj_drop=0.):
        super().__init__()
        assert dim % num_heads == 0, 'dim should be divisible by num_heads'
        self.num_heads = num_heads
        head_dim = dim // num_heads
        self.scale = head_dim ** -0.5

        self.q = nn.Linear(dim, dim, bias=qkv_bias)
        # Project K and V together from encoder features (context)
        self.kv = nn.Linear(dim, dim * 2, bias=qkv_bias)
        self.attn_drop = nn.Dropout(attn_drop)
        self.proj = nn.Linear(dim, dim)
        self.proj_drop = nn.Dropout(proj_drop)

    def forward(self, x_query, x_kv):
        # x_query: [B, N_q, C] (Decoder features - Query)
        # x_kv: [B, N_kv, C] (Encoder features - Key/Value context)
        B, N_q, C = x_query.shape
        B_kv, N_kv, C_kv = x_kv.shape # Get shape of context
        assert C == C_kv, "Query and Key/Value dimensions must match"

        q = self.q(x_query).reshape(B, N_q, self.num_heads, C // self.num_heads).permute(0, 2, 1, 3) # B, h, N_q, C/h
        kv = self.kv(x_kv).reshape(B_kv, N_kv, 2, self.num_heads, C // self.num_heads).permute(2, 0, 3, 1, 4) # 2, B, h, N_kv, C/h
        k, v = kv.unbind(0) # B, h, N_kv, C/h

        attn = (q @ k.transpose(-2, -1)) * self.scale # B, h, N_q, N_kv
        attn = attn.softmax(dim=-1)
        attn = self.attn_drop(attn)

        x = (attn @ v).transpose(1, 2).reshape(B, N_q, C) # B, N_q, C
        x = self.proj(x)
        x = self.proj_drop(x)
        return x

# Combined Cross-Attention and Self-Attention Block (inspired by Transformer decoders)
class DecoderCrossAttentionBlock(nn.Module):
    def __init__(self, dim, num_heads, mlp_ratio=4., qkv_bias=False, drop=0., attn_drop=0.,
                 drop_path=0., act_layer=nn.GELU, norm_layer=nn.LayerNorm):
        super().__init__()
        self.norm1_q = norm_layer(dim)
        self.norm1_kv = norm_layer(dim) # Norm for encoder features (Key/Value)
        self.cross_attn = CrossAttention(
            dim, num_heads=num_heads, qkv_bias=qkv_bias, attn_drop=attn_drop, proj_drop=drop)
        self.drop_path1 = DropPath(drop_path) if drop_path > 0. else nn.Identity()

        # Standard Self-Attention Block (using timm's Block)
        # Note: timm's Block applies norm *before* attention/mlp and includes residual connection
        self.self_attn_block = Block(
            dim=dim, num_heads=num_heads, mlp_ratio=mlp_ratio, qkv_bias=qkv_bias,
            drop=drop, attn_drop=attn_drop, drop_path=drop_path, norm_layer=norm_layer, act_layer=act_layer
        )

    def forward(self, x_query, x_kv):
        # x_query: [B, N_q, C] (Decoder features)
        # x_kv: [B, N_kv, C] (Encoder features)

        # Cross-Attention part (Query attends to Key/Value from Encoder) + Residual
        x_query = x_query + self.drop_path1(self.cross_attn(self.norm1_q(x_query), self.norm1_kv(x_kv)))

        # Self-Attention + MLP part (using timm's Block, which includes residuals internally)
        x_query = self.self_attn_block(x_query)

        return x_query


class UpSampleBlock(nn.Module):
    """Upsampling block using ConvTranspose2d"""
    def __init__(self, in_channels, out_channels, scale_factor=2):
        super().__init__()
        # Ensure kernel_size and stride match scale_factor for simple upsampling
        self.conv_transpose = nn.ConvTranspose2d(in_channels, out_channels, kernel_size=scale_factor, stride=scale_factor)
        # Using GroupNorm as an alternative, potentially more stable than LayerNorm on spatial features
        self.norm = nn.GroupNorm(num_groups=max(1, out_channels // 32), num_channels=out_channels) # Example GroupNorm
        self.act = nn.GELU()

    def forward(self, x):
        # Input x expected shape: [N, C, H, W]
        x = self.conv_transpose(x)
        x = self.norm(x)
        x = self.act(x)
        return x

# Hierarchical Decoder (Inspired by U-Net and Hi-End-MAE concepts - Using Cross Attention)
class HierarchicalDecoder(nn.Module):
    def __init__(self, encoder_dims, decoder_embed_dim, decoder_depths, decoder_num_heads, mlp_ratio=4., qkv_bias=True, norm_layer=nn.LayerNorm, patch_size=4, in_chans=1, drop_path_rate=0.):
        super().__init__()
        self.patch_size = patch_size
        self.in_chans = in_chans
        self.num_stages = len(encoder_dims) # Number of encoder stages providing features

        # Upsampling layers
        self.upsample_blocks = nn.ModuleList()
        # Decoder blocks (CrossAttention + SelfAttention + MLP) for each stage
        self.decoder_stages = nn.ModuleList()
        # Linear projections for encoder features (Key/Value context) to match decoder dimension at each stage
        self.encoder_projections = nn.ModuleList()

        current_dim = decoder_embed_dim # Start with the dimension after initial projection from latent_full

        # Stochastic depth decay rule
        dpr = [x.item() for x in torch.linspace(0, drop_path_rate, sum(decoder_depths))]
        block_idx = 0

        # Build decoder stages from deep to shallow
        for i in range(self.num_stages - 1, -1, -1): # Iterate encoder stages in reverse
            encoder_dim = encoder_dims[i]
            stage_depth = decoder_depths[i] # Depth for this decoder stage
            stage_heads = decoder_num_heads[i] # Heads for this decoder stage

            # 1. Upsampling Block (except for the deepest stage)
            if i < self.num_stages - 1:
                # Upsample from previous decoder stage dim to current stage dim
                # Keep dimension consistent during upsampling
                upsample = UpSampleBlock(in_channels=current_dim, out_channels=current_dim, scale_factor=2)
                self.upsample_blocks.insert(0, upsample) # Prepend to list
            else:
                 self.upsample_blocks.insert(0, nn.Identity()) # No upsampling for the first stage

            # 2. Encoder Feature Projection (for Key/Value context)
            # Project encoder feature dim to match the current decoder dimension
            encoder_proj = nn.Linear(encoder_dim, current_dim, bias=True) if encoder_dim != current_dim else nn.Identity()
            self.encoder_projections.insert(0, encoder_proj) # Prepend

            # 3. Decoder Stage Blocks (CrossAttention + SelfAttention)
            stage_blocks = nn.ModuleList()
            for j in range(stage_depth):
                 stage_blocks.append(
                     DecoderCrossAttentionBlock(
                         dim=current_dim, num_heads=stage_heads, mlp_ratio=mlp_ratio, qkv_bias=qkv_bias,
                         drop=0., attn_drop=0., drop_path=dpr[block_idx + j], norm_layer=norm_layer
                     )
                 )
            self.decoder_stages.insert(0, stage_blocks) # Prepend
            block_idx += stage_depth

            # Note: current_dim remains the same across stages in this design

        # Final prediction head
        self.decoder_norm = norm_layer(current_dim) # Norm after last stage
        self.decoder_pred = nn.Linear(current_dim, patch_size**2 * in_chans, bias=True)
        self.final_dim = current_dim # Store final dimension for potential NCE projector

        print(f"Initialized HierarchicalDecoder (Cross-Attention) with {self.num_stages} stages.")
        print(f"Decoder stage depths: {decoder_depths}")
        print(f"Decoder stage heads: {decoder_num_heads}")


    def forward(self, x_decoder_input, encoder_features, initial_grid_size):
        # x_decoder_input: Full sequence of decoder tokens [N, L_initial, D_decoder] (incl. mask tokens + pos embed)
        # encoder_features: List of intermediate features [N, C, H, W] from encoder stages (shallow to deep)
        # initial_grid_size: (H_initial, W_initial)

        x = x_decoder_input # Start with the full sequence [N, L_initial, D_decoder]

        # Decoder path (from deep to shallow)
        for i in range(self.num_stages):
            stage_blks = self.decoder_stages[i]
            upsample_blk = self.upsample_blocks[i]
            encoder_proj_blk = self.encoder_projections[i]

            # 1. Upsample (if not the first stage) - Operates on spatial, so reshape needed
            if not isinstance(upsample_blk, nn.Identity):
                # Calculate H, W of the feature map *before* upsampling
                scale_factor = 2**(self.num_stages - 1 - i)
                H_prev = initial_grid_size[0] // (scale_factor * 2)
                W_prev = initial_grid_size[1] // (scale_factor * 2)
                C_prev = x.shape[-1]
                # Reshape sequence to spatial [N, C, H, W] before upsampling
                x = x.transpose(1, 2).reshape(x.shape[0], C_prev, H_prev, W_prev)
                x = upsample_blk(x) # Upsamples spatially, dim remains current_dim
                # Reshape back to sequence [N, L_new, C_curr]
                N, C_curr, H_curr, W_curr = x.shape
                x = x.permute(0, 2, 3, 1).reshape(N, H_curr * W_curr, C_curr)

            # 2. Prepare Encoder Skip Feature (Project and Reshape to Sequence)
            skip_feature = encoder_features[self.num_stages - 1 - i] # Get feature [N, C_enc, H_enc, W_enc]
            N_skip, C_enc, H_enc, W_enc = skip_feature.shape
            skip_feature_seq = skip_feature.permute(0, 2, 3, 1).reshape(N_skip, H_enc * W_enc, C_enc) # -> [N, L_enc, C_enc]
            if isinstance(encoder_proj_blk, nn.Linear):
                 skip_feature_seq = encoder_proj_blk(skip_feature_seq) # -> [N, L_enc, C_curr]

            # 3. Apply Decoder Stage Blocks (CrossAttention + SelfAttention)
            # x is the query (decoder state), skip_feature_seq provides key/value context
            for blk in stage_blks:
                x = blk(x, skip_feature_seq) # Pass both query and kv context

        # Final processing after last stage (output should be sequence [N, L_initial, C_final])
        # x should already be in sequence format [N, L_initial, current_dim] after the last stage

        # Apply final norm
        x_norm = self.decoder_norm(x)
        # Get final prediction
        pred = self.decoder_pred(x_norm) # [N, L_initial, patch_size^2 * C]

        # Return both prediction and features before prediction head (for PatchNCE)
        return pred, x_norm


# Window Masking (Based on Swin MAE paper / Zian-Xu implementation idea)
# This version returns visible tokens, mask, and restore indices
# --------------------------------------------------------
def window_masking(x: torch.Tensor, mask_token: nn.Parameter, window_size: int, mask_ratio: float):
    """
    Performs window-level masking.
    Returns visible tokens, the binary mask (0=keep, 1=mask), and indices to restore order.
    x: [N, L, D] input sequence after patch embedding + pos embedding
    mask_token: [1, 1, D] the learnable mask token (used for encoder input if needed, not returned here)
    window_size: int, size of the masking window (e.g., 4 or 7)
    mask_ratio: float, e.g., 0.75
    """
    N, L, D = x.shape
    H_patch = W_patch = int(L**0.5) # Grid size HxW
    assert H_patch * W_patch == L, "Input sequence length L must be a perfect square."
    if not isinstance(window_size, int) or window_size <= 0:
        raise ValueError(f"window_size must be a positive integer, got {window_size}")
    if H_patch % window_size != 0 or W_patch % window_size != 0:
         print(f"Warning: Patch grid size ({H_patch}x{W_patch}) not perfectly divisible by window size ({window_size}). Masking might be uneven.")
         num_windows_h = math.ceil(H_patch / window_size)
         num_windows_w = math.ceil(W_patch / window_size)
    else:
         num_windows_h = H_patch // window_size
         num_windows_w = W_patch // window_size

    num_windows = num_windows_h * num_windows_w

    len_keep_windows = int(num_windows * (1 - mask_ratio))
    noise = torch.rand(N, num_windows, device=x.device)
    ids_shuffle_windows = torch.argsort(noise, dim=1)
    ids_restore_windows = torch.argsort(ids_shuffle_windows, dim=1)

    mask_windows = torch.ones(N, num_windows, device=x.device)
    mask_windows[:, :len_keep_windows] = 0
    mask_windows = torch.gather(mask_windows, dim=1, index=ids_restore_windows)

    mask_windows_reshaped = mask_windows.reshape(N, num_windows_h, num_windows_w)
    mask_patches = mask_windows_reshaped.repeat_interleave(window_size, dim=1).repeat_interleave(window_size, dim=2)
    mask_patches = mask_patches[:, :H_patch, :W_patch]
    mask = mask_patches.reshape(N, L) # Binary mask: 0 is keep, 1 is mask

    # Get indices for keep/mask
    ids_keep = torch.where(mask == 0)[1].reshape(N, -1) # Indices of visible patches
    ids_mask = torch.where(mask == 1)[1].reshape(N, -1) # Indices of masked patches
    # Generate indices to restore original order from shuffled (keep + mask)
    ids_shuffle = torch.cat([ids_keep, ids_mask], dim=1)
    ids_restore = torch.argsort(ids_shuffle, dim=1)

    # Get visible tokens for encoder input
    x_visible = torch.gather(x, dim=1, index=ids_keep.unsqueeze(-1).repeat(1, 1, D))

    return x_visible, mask, ids_restore

# --------------------------------------------------------
# Enhanced Perceptual Loss using ResNet18 for CT images (Modified)
# --------------------------------------------------------
class ResNetPerceptualLoss(nn.Module):
    def __init__(self, feature_layer_names=['layer2', 'layer3'], use_ct_norm=True, requires_grad=False): # Default to shallower layers, add norm flag
        super().__init__()
        # 加载预训练的ResNet18
        resnet = models.resnet18(weights=models.ResNet18_Weights.IMAGENET1K_V1)
        self.use_ct_norm = use_ct_norm # Flag for custom CT normalization

        # 修改第一层卷积以接受单通道输入
        original_conv = resnet.conv1
        resnet.conv1 = nn.Conv2d(1, 64, kernel_size=7, stride=2, padding=3, bias=False)

        # 初始化新的卷积层权重 - 使用原始权重的平均值
        with torch.no_grad():
            resnet.conv1.weight.data = original_conv.weight.data.mean(dim=1, keepdim=True)

        # 定义要提取的特征层 (using names now)
        self.feature_layer_names = feature_layer_names
        # Define weights based on the chosen layers (example weights, might need tuning)
        default_weights = {'layer1': 1.0, 'layer2': 0.8, 'layer3': 0.6, 'layer4': 0.4}
        self.layer_weights = [default_weights.get(name, 0.5) for name in self.feature_layer_names] # Get weights for specified layers

        # 提取特征层
        self.features = nn.ModuleDict()
        current_model = nn.Sequential()

        # 添加初始层
        current_model.add_module('conv1', resnet.conv1)
        current_model.add_module('bn1', resnet.bn1)
        current_model.add_module('relu', resnet.relu)
        current_model.add_module('maxpool', resnet.maxpool)

        # 添加ResNet的各个层 based on feature_layer_names
        if 'layer1' in self.feature_layer_names:
            self.features['layer1'] = nn.Sequential(current_model, resnet.layer1)
        # Always advance current_model regardless of whether the layer is used for loss
        current_model = nn.Sequential(current_model, resnet.layer1)

        if 'layer2' in self.feature_layer_names:
            self.features['layer2'] = nn.Sequential(current_model, resnet.layer2)
        current_model = nn.Sequential(current_model, resnet.layer2)

        if 'layer3' in self.feature_layer_names:
            self.features['layer3'] = nn.Sequential(current_model, resnet.layer3)
        current_model = nn.Sequential(current_model, resnet.layer3)

        if 'layer4' in self.feature_layer_names:
            self.features['layer4'] = nn.Sequential(current_model, resnet.layer4)
        # current_model = nn.Sequential(current_model, resnet.layer4) # Not needed if layer4 is the last

        if not self.features:
             raise ValueError(f"No valid ResNet feature layers specified in {self.feature_layer_names}")

        # 冻结参数
        if not requires_grad:
            for param in self.parameters():
                param.requires_grad = False

        self.eval()  # 设置为评估模式
        self.criterion = nn.L1Loss(reduction='mean')
        print(f"Initialized ResNetPerceptualLoss using layers: {self.feature_layer_names} with weights {self.layer_weights}. CT norm: {self.use_ct_norm}")

    def _normalize_ct(self, x):
        """归一化CT图像以适应ResNet (Instance-wise MinMax after [0,1] scaling)
        Input: Tensor in range [-1, 1]
        Output: Tensor in range [0, 1] (instance normalized)
        """
        # 从[-1, 1]转换到[0, 1]
        x = (x + 1.0) / 2.0

        # 应用CT图像特定的对比度增强 (Instance-wise Min-Max scaling)
        x_min = x.min(dim=2, keepdim=True)[0].min(dim=3, keepdim=True)[0]
        x_max = x.max(dim=2, keepdim=True)[0].max(dim=3, keepdim=True)[0]
        denominator = x_max - x_min
        # Avoid division by zero for blank images/patches
        x_norm = torch.where(denominator > 1e-8, (x - x_min) / denominator, torch.zeros_like(x))

        return x_norm

    def forward(self, x, y, mask=None):
        """计算感知损失
        Args:
            x, y: 输入图像 [N, 1, H, W] in range [-1, 1]
            mask: 可选掩码 [N, 1, H, W]，1表示掩码区域
        """
        # Apply normalization if enabled
        if self.use_ct_norm:
            x = self._normalize_ct(x)
            y = self._normalize_ct(y)
        else:
            # If not using CT norm, just scale to [0, 1] as ResNet expects non-negative input
            x = (x + 1.0) / 2.0
            y = (y + 1.0) / 2.0

        total_loss = 0.0

        # 计算每个特征层的损失
        for i, layer_name in enumerate(self.feature_layer_names): # Use names here
            layer = self.features[layer_name]
            x_feat = layer(x)
            y_feat = layer(y)

            # 应用掩码（如果提供）
            if mask is not None:
                # 调整掩码大小以匹配特征图
                mask_resized = F.interpolate(mask, size=x_feat.shape[2:], mode='nearest')
                # 计算掩码区域的损失
                layer_loss = self.criterion(x_feat * mask_resized, y_feat * mask_resized)
            else:
                layer_loss = self.criterion(x_feat, y_feat)

            # 应用层权重
            weight = self.layer_weights[i]
            total_loss += weight * layer_loss

        # Normalize loss by the number of layers used
        if len(self.feature_layer_names) > 0:
            total_loss /= len(self.feature_layer_names)

        return total_loss

# --------------------------------------------------------
# PatchNCE Loss Implementation
# --------------------------------------------------------
class PatchNCELoss(nn.Module):
    def __init__(self, batch_size, nce_T=0.07):
        super().__init__()
        self.batch_size = batch_size
        self.nce_T = nce_T
        self.cross_entropy_loss = torch.nn.CrossEntropyLoss(reduction='mean')

    def forward(self, feat_q, feat_k):
        """
        Calculates PatchNCE loss using projected features.
        feat_q: Query features (projected from reconstructed patches) [Num_masked_total, C_proj]
        feat_k: Key features (projected from original patches) [Num_masked_total, C_proj]
        Assumes feat_q and feat_k correspond patch-wise and are already selected for masked regions.
        """
        if feat_q is None or feat_k is None or feat_q.numel() == 0 or feat_k.numel() == 0:
             return torch.tensor(0.0, device=feat_q.device if feat_q is not None else 'cpu')

        # Normalize features (already projected)
        q = F.normalize(feat_q, dim=1)
        k = F.normalize(feat_k, dim=1)

        # Positive logits: (N*L_masked) x 1
        l_pos = torch.einsum('nc,nc->n', [q, k]).unsqueeze(-1)

        # Negative logits: (N*L_masked) x (N*L_masked - 1)
        # Calculate all pairwise similarities between queries and keys
        logits_all = torch.mm(q, k.transpose(0, 1)) # Shape: [Num_masked_total, Num_masked_total]

        # Mask out positive samples (diagonal entries - corresponding query and key)
        identity = torch.eye(logits_all.shape[0], device=logits_all.device, dtype=torch.bool)
        l_neg = logits_all[~identity].view(logits_all.shape[0], -1) # Shape: [Num_masked_total, Num_masked_total - 1]

        # Combine positive and negative logits
        # Shape: [Num_masked_total, Num_masked_total]
        logits = torch.cat([l_pos, l_neg], dim=1)

        # Apply temperature scaling
        logits /= self.nce_T

        # Labels: positives are the 0-th class
        labels = torch.zeros(logits.shape[0], dtype=torch.long, device=logits.device)

        loss = self.cross_entropy_loss(logits, labels)
        return loss


# --------------------------------------------------------
# Based on MAE implementation by Meta Platforms, Inc. and Swin Transformer
# --------------------------------------------------------
class MaskedAutoencoderSwin(nn.Module):
    """ Masked Autoencoder with Swin Transformer backbone """
    def __init__(self, img_size=256, patch_size=4, in_chans=1, # Swin uses patch_size=4 typically
                 embed_dim=96, depths=[2, 2, 6, 2], num_heads=[3, 6, 12, 24], # Swin-T defaults
                 window_size=7, mlp_ratio=4., qkv_bias=True, qk_scale=None,
                 drop_rate=0., attn_drop_rate=0., drop_path_rate=0.1,
                 norm_layer=nn.LayerNorm, ape=False, patch_norm=True, # Swin specific params
                 decoder_embed_dim=512, # Base dimension for decoder stages
                 decoder_depths=[2, 2, 6, 2], # Depth per decoder stage (mirrors encoder default)
                 decoder_num_heads=[24, 12, 6, 3], # Heads per decoder stage (mirrors encoder default, reversed)
                 masking_window_size=4, # Size of window for masking (e.g., 4x4 patches)
                 decoder_mlp_ratio=4., decoder_norm_layer=nn.LayerNorm, # Decoder specific norm/mlp
                 norm_pix_loss=False,
                 perceptual_loss_weight=0.01, # Default weight lowered
                 perc_layers_resnet=['layer2', 'layer3'], # Configurable layers
                 perc_norm_ct_resnet=True, # Configurable normalization
                 ssim_loss_weight=0.05, # Add SSIM loss weight
                 patchnce_loss_weight=0.1, # Add PatchNCE loss weight (default 0.1)
                 nce_proj_dim=256): # Dimension for PatchNCE projector
        super().__init__()

        self.patch_size = patch_size # Store patch size
        self.in_chans = in_chans # Store in_chans
        self.masking_window_size = masking_window_size # Store masking window size
        self.perceptual_loss_weight = perceptual_loss_weight # Store perceptual loss weight
        self.ssim_loss_weight = ssim_loss_weight # Store SSIM weight
        self.patchnce_loss_weight = patchnce_loss_weight # Store PatchNCE weight

        # --------------------------------------------------------------------------
        # Swin MAE encoder specifics
        self.encoder = timm.create_model(
            'swin_tiny_patch4_window7_224', # Reverted to standard tiny model name
            pretrained=False,
            in_chans=in_chans,
            img_size=img_size, # Timm handles img_size mismatch from 224
            patch_size=patch_size, # Should be 4 for standard Swin
            embed_dim=embed_dim,
            depths=depths,
            num_heads=num_heads,
            window_size=window_size, # Attention window size
            mlp_ratio=mlp_ratio,
            qkv_bias=qkv_bias,
            qk_scale=qk_scale,
            drop_rate=drop_rate,
            attn_drop_rate=attn_drop_rate,
            drop_path_rate=drop_path_rate,
            norm_layer=norm_layer,
            ape=ape, # Absolute Position Embedding
            patch_norm=patch_norm,
            num_classes=0, # No head
            global_pool='' # No pooling
        )

        # --- MAE specific additions/modifications ---
        # Store encoder stage dimensions (needed for decoder skip connections)
        self.encoder_dims = [int(embed_dim * 2**i) for i in range(len(depths))]
        self.actual_encoder_output_dim = self.encoder_dims[-1]
        print(f"Encoder stage dims: {self.encoder_dims}")
        print(f"Final encoder output dim: {self.actual_encoder_output_dim}")


        self.patch_embed = self.encoder.patch_embed
        self.embed_dim = embed_dim # Store embed_dim

        # --- Use grid_size from patch_embed ---
        self.actual_grid_size = self.patch_embed.grid_size
        self.num_patches = self.actual_grid_size[0] * self.actual_grid_size[1]
        print(f"Using grid_size from patch_embed: {self.actual_grid_size}, num_patches: {self.num_patches}")

        # --- Calculate final grid size and patch count after encoder downsampling ---
        # This IS needed because the decoder receives features from the encoder's final resolution
        downsample_factor = 2**(len(depths) - 1) # Number of patch merging stages = len(depths) - 1
        self.grid_final = (self.actual_grid_size[0] // downsample_factor, self.actual_grid_size[1] // downsample_factor)
        self.num_patches_final = self.grid_final[0] * self.grid_final[1] # Calculate num_patches_final
        print(f"Calculated final grid size after encoder: {self.grid_final}, num_patches_final: {self.num_patches_final}") # Print both


        # --- Absolute Position Embedding (APE) for Encoder ---
        if ape:
             self.pos_embed = getattr(self.encoder, 'absolute_pos_embed', None)
             if self.pos_embed is not None:
                 print("Using absolute positional embedding from Swin config (handled internally by timm model).")
             else:
                 print("Warning: ape=True but Swin model doesn't seem to have 'absolute_pos_embed'. Creating custom APE.")
                 self.pos_embed = nn.Parameter(torch.zeros(1, self.num_patches, embed_dim))
        else:
            self.pos_embed = nn.Parameter(torch.zeros(1, self.num_patches, embed_dim)) # Use self.num_patches
            print("Created absolute positional embedding for Swin MAE (ape=False).")


        # --------------------------------------------------------------------------
        # MAE Decoder Selection
        # Initial projection from final encoder output dim to decoder base dim
        self.decoder_embed = nn.Linear(self.actual_encoder_output_dim, decoder_embed_dim, bias=True)

        # Instantiate the Hierarchical Decoder
        print("Using Hierarchical Decoder (Cross-Attention)")
        self.decoder = HierarchicalDecoder(
            encoder_dims=self.encoder_dims, # Pass encoder stage dims
            decoder_embed_dim=decoder_embed_dim,
            decoder_depths=decoder_depths, # Pass per-stage depths
            decoder_num_heads=decoder_num_heads, # Pass per-stage heads
            mlp_ratio=decoder_mlp_ratio,
            qkv_bias=qkv_bias,
            norm_layer=decoder_norm_layer,
            patch_size=patch_size,
            in_chans=in_chans,
            drop_path_rate=drop_path_rate # Pass drop path rate to decoder
        )

        # --- Mask Tokens ---
        # Mask token for the ENCODER (dimension: embed_dim) - used in window_masking
        self.encoder_mask_token = nn.Parameter(torch.zeros(1, 1, embed_dim))
        print(f"Initialized encoder mask_token with size: {self.encoder_mask_token.shape}")
        # Mask token for the DECODER (dimension: decoder_embed_dim) - needed for constructing decoder input
        self.decoder_mask_token = nn.Parameter(torch.zeros(1, 1, decoder_embed_dim))
        print(f"Initialized decoder mask_token with size: {self.decoder_mask_token.shape}")


        # --- Positional Embedding (for Decoder) ---
        # Needed for constructing the full input sequence to the decoder
        self.decoder_pos_embed = nn.Parameter(torch.zeros(1, self.num_patches, decoder_embed_dim), requires_grad=False) # Use self.num_patches
        print(f"Initialized decoder_pos_embed with size: {self.decoder_pos_embed.shape}")
        # Initialize decoder pos_embed using sin-cos
        decoder_pos_embed_data = get_2d_sincos_pos_embed(self.decoder_pos_embed.shape[-1], self.actual_grid_size[0], cls_token=False)
        self.decoder_pos_embed.data.copy_(torch.from_numpy(decoder_pos_embed_data).float().unsqueeze(0))
        print(f"Initialized decoder positional embedding using actual_grid_size: {self.actual_grid_size}")

        # --------------------------------------------------------------------------
        # PatchNCE Projector (Optional)
        if self.patchnce_loss_weight > 0:
            # Projector for Query (decoder features)
            nce_feat_dim_q = self.decoder.final_dim # Get final dim from decoder
            self.nce_projector_q = nn.Sequential(
                nn.Linear(nce_feat_dim_q, nce_proj_dim),
                nn.ReLU(),
                nn.Linear(nce_proj_dim, nce_proj_dim)
            )
            # Projector for Key (raw pixel patches)
            nce_feat_dim_k = patch_size * patch_size * in_chans
            self.nce_projector_k = nn.Sequential(
                nn.Linear(nce_feat_dim_k, nce_proj_dim),
                nn.ReLU(),
                nn.Linear(nce_proj_dim, nce_proj_dim)
            )
            self.patchnce_loss = PatchNCELoss(batch_size=0) # Batch size set dynamically later
            print(f"Initialized PatchNCE Loss with weight {self.patchnce_loss_weight} and projectors (Q_in:{nce_feat_dim_q}, K_in:{nce_feat_dim_k}, Proj_out:{nce_proj_dim}).")
        else:
            self.nce_projector_q = None
            self.nce_projector_k = None
            self.patchnce_loss = None
        # --------------------------------------------------------------------------

        self.norm_pix_loss = norm_pix_loss

        # Instantiate Enhanced Perceptual Loss using ResNet18 (ensure it's on the correct device later)
        if self.perceptual_loss_weight > 0:
             # Use ResNet18 for better CT image feature extraction
             self.perceptual_loss = ResNetPerceptualLoss(
                 feature_layer_names=perc_layers_resnet,
                 use_ct_norm=perc_norm_ct_resnet,
                 requires_grad=False
             )
             # Print statement moved inside ResNetPerceptualLoss __init__
        else:
             self.perceptual_loss = None

        self.ssim_loss_weight = ssim_loss_weight # Store SSIM weight
        if self.ssim_loss_weight > 0:
            print(f"Initialized SSIM Loss with weight {self.ssim_loss_weight}")


        self.initialize_weights() # Call init after defining all layers

    def initialize_weights(self):
        # Initialize custom APE if we created it
        if isinstance(self.pos_embed, nn.Parameter): # Check if it's the one we created
             pos_embed_data = get_2d_sincos_pos_embed(self.pos_embed.shape[-1], self.actual_grid_size[0], cls_token=False)
             self.pos_embed.data.copy_(torch.from_numpy(pos_embed_data).float().unsqueeze(0))
             print("Initialized custom absolute positional embedding for encoder.")

        # Initialize encoder mask_token
        torch.nn.init.normal_(self.encoder_mask_token, std=.02)
        # Initialize decoder mask_token
        torch.nn.init.normal_(self.decoder_mask_token, std=.02)
        print("Initialized mask tokens.")

        # Initialize linear layers and layer norms (excluding already initialized embeddings)
        self.apply(self._init_weights)

    def _init_weights(self, m):
        if isinstance(m, nn.Linear):
            # we use xavier_uniform following official JAX ViT:
            torch.nn.init.xavier_uniform_(m.weight)
            if isinstance(m, nn.Linear) and m.bias is not None:
                nn.init.constant_(m.bias, 0)
        elif isinstance(m, nn.LayerNorm):
            nn.init.constant_(m.bias, 0)
            nn.init.constant_(m.weight, 1.0)

    # --- Encoder following Swin MAE paper (modified to return intermediate features) ---
    def forward_encoder(self, x, mask_ratio):
        # x: [N, C, H, W]
        intermediate_features = [] # List to store features from each stage

        # 1. Patch Embedding
        x = self.patch_embed(x) # Output is likely [N, H', W', D]
        # Ensure output is [N, L, D] before unpacking shape
        if x.dim() == 4:
            x = x.flatten(1, 2) # -> [N, H'*W', D] = [N, L, D]
        N, L, D = x.shape # Now unpack the 3D shape

        # 2. Add Position Embedding (if applicable)
        if self.pos_embed is not None:
            if self.pos_embed.shape[1] != L:
                 print(f"Warning: Position embedding size mismatch (Expected {L}, Got {self.pos_embed.shape[1]}). Attempting interpolation.")
                 pos_embed_resized = F.interpolate(
                     self.pos_embed.reshape(1, int(self.pos_embed.shape[1]**0.5), int(self.pos_embed.shape[1]**0.5), D).permute(0, 3, 1, 2),
                     size=self.actual_grid_size, mode='bicubic', align_corners=False,
                 ).permute(0, 2, 3, 1).reshape(1, L, D)
                 x = x + pos_embed_resized.type_as(x)
            else:
                 x = x + self.pos_embed.type_as(x)

        # 3. Window Masking (Returns visible tokens, mask, and restore indices)
        # Use the encoder_mask_token (which has dimension D = embed_dim)
        mask_token_to_use = self.encoder_mask_token.type_as(x)

        # Use the masking_window_size defined in init
        # Now returns visible tokens, mask (0=keep, 1=mask), and restore indices
        x_visible, mask, ids_restore = window_masking(x, mask_token_to_use, self.masking_window_size, mask_ratio)
        # x_visible: [N, L_vis, D] (sequence of visible tokens)
        # mask: [N, L] (0 is keep, 1 is mask)

        # 4. Pass the *visible sequence* through Swin stages
        # Reshape visible sequence to spatial format expected by Swin layers
        # This requires knowing the grid size corresponding to visible tokens, which is tricky.
        # Let's revert to the less efficient approach of passing the full sequence with mask tokens through encoder for simplicity,
        # as done in the original Swin-MAE paper and our previous versions.
        # Re-apply masking to the original sequence 'x'
        x_masked = x.clone()
        mask_bool = mask.bool().unsqueeze(-1).expand_as(x)
        x_masked = torch.where(mask_bool, mask_token_to_use, x_masked) # Replace where mask is True

        H_grid, W_grid = self.actual_grid_size
        x_spatial_masked = x_masked.reshape(N, H_grid, W_grid, D)

        # Pass through Swin layers and store intermediate outputs
        for i, layer in enumerate(self.encoder.layers):
            x_spatial_masked = layer(x_spatial_masked) # Output: [N, H_out, W_out, C_out]
            # Store the feature map *before* the final norm of the encoder
            # Reshape to [N, C, H, W] for easier handling in decoder
            intermediate_features.append(x_spatial_masked.permute(0, 3, 1, 2).contiguous())
            # print(f"Encoder stage {i} output shape: {x_spatial_masked.shape}") # Reduce print frequency


        # Flatten back to sequence for final normalization
        N_out, H_out, W_out, C_out = x_spatial_masked.shape # Shape after last layer
        x_seq_masked = x_spatial_masked.view(N_out, H_out * W_out, C_out) # Flatten to [N, L_final, C_out]

        # Apply final encoder normalization
        latent_full = self.encoder.norm(x_seq_masked) # [N, L_final, D_encoder_output]


        # Return final latent, initial patch mask, intermediate features, and restore indices
        return latent_full, mask, intermediate_features, ids_restore

    # --- Decoder ---
    def forward_decoder(self, latent_full, intermediate_features, mask, ids_restore):
        # latent_full: Final encoder output (full sequence with mask tokens) [N, L_final, D_enc_out]
        # intermediate_features: List of features from encoder stages [N, C, H, W] (shallow to deep)
        # mask: Original patch mask [N, L_full] (0=keep, 1=mask)
        # ids_restore: Indices to restore original patch order [N, L_full]

        # 1. Project final encoder features to decoder dimension
        x = self.decoder_embed(latent_full) # [N, L_final, D_decoder]

        # 2. Construct full sequence for decoder input (Hi-End-MAE style)
        # We need to select the *projected* features corresponding to visible patches
        # and combine them with decoder mask tokens.
        N, L_final, D_dec = x.shape
        L_full = mask.shape[1]

        # Get indices of visible patches (mask == 0)
        ids_keep = torch.where(mask.flatten(1) == 0)[1].reshape(N, -1)
        # Select projected features of visible patches
        x_vis = torch.gather(x, dim=1, index=ids_keep.unsqueeze(-1).repeat(1, 1, D_dec)) # [N, L_vis, D_dec]

        # Append mask tokens for masked patches
        num_masked = L_full - x_vis.shape[1]
        mask_tokens = self.decoder_mask_token.repeat(N, num_masked, 1) # [N, L_mask, D_dec]

        # Concatenate visible tokens and mask tokens
        x_full = torch.cat([x_vis, mask_tokens], dim=1) # [N, L_full, D_dec]

        # Unshuffle tokens to original order using ids_restore
        x_full = torch.gather(x_full, dim=1, index=ids_restore.unsqueeze(-1).repeat(1, 1, D_dec))

        # 3. Add Decoder Position Embedding
        x_full = x_full + self.decoder_pos_embed.type_as(x_full) # [N, L_full, D_decoder]

        # 4. Pass the full sequence through Hierarchical Decoder
        # The decoder handles upsampling, fusion (via cross-attention), and final prediction internally.
        # It now returns prediction and features before the final head.
        pred_seq, final_decoder_features = self.decoder(x_full, intermediate_features, self.actual_grid_size) # Pass initial grid size

        return pred_seq, final_decoder_features # Return predictions and final features

    def patchify(self, imgs):
        """ imgs: (N, C, H, W) -> (N, L, patch_size**2 * C) """
        p = self.patch_size
        assert imgs.shape[2] == imgs.shape[3] and imgs.shape[2] % p == 0
        h = w = imgs.shape[2] // p
        c = self.in_chans # Use self.in_chans
        x = imgs.reshape(shape=(imgs.shape[0], c, h, p, w, p))
        x = torch.einsum('nchpwq->nhwpqc', x)
        x = x.reshape(shape=(imgs.shape[0], h * w, p**2 * c))
        return x

    def unpatchify(self, x):
        """ x: (N, L, patch_size**2 * C) -> (N, C, H, W) """
        p = self.patch_size
        h, w = self.actual_grid_size # Use initial grid size for unpatchify
        assert h * w == x.shape[1], f"h*w ({h*w}) from grid_size does not match L ({x.shape[1]})"
        c = self.in_chans # Use self.in_chans
        assert x.shape[2] == p**2 * c, f"Decoder prediction dim {x.shape[2]} != p*p*C ({p**2 * c})"

        x = x.reshape(shape=(x.shape[0], h, w, p, p, c))
        x = torch.einsum('nhwpqc->nchpwq', x)
        imgs = x.reshape(shape=(x.shape[0], c, h * p, w * p))
        return imgs

    def forward_loss(self, imgs, pred, final_decoder_features, mask):
        """
        imgs: [N, C, H, W] - Original images
        pred: [N, L_initial, p*p*C] - Predicted patch values from decoder
        final_decoder_features: [N, L_initial, D_decoder_final] - Features before prediction head
        mask: [N, L_initial], 1 is remove/masked
        """
        target = self.patchify(imgs) # Target shape: [N, L_initial, p*p*C]
        if self.norm_pix_loss:
            mean = target.mean(dim=-1, keepdim=True)
            var = target.var(dim=-1, keepdim=True)
            target = (target - mean) / (var + 1.e-6)**.5

        # --- Reconstruction Loss (MSE on masked patches) ---
        loss_recon = (pred - target) ** 2
        loss_recon = loss_recon.mean(dim=-1)  # [N, L_initial], mean loss per patch
        mask_sum = mask.sum()
        if mask_sum == 0:
             reconstruction_loss = torch.tensor(0.0, device=pred.device, requires_grad=True)
        else:
             reconstruction_loss = (loss_recon * mask).sum() / mask_sum # Mean loss on removed patches

        # --- Perceptual Loss ---
        perc_loss = torch.tensor(0.0, device=pred.device)
        pred_img = None # Initialize to avoid unpatchifying twice
        if self.perceptual_loss is not None and self.perceptual_loss_weight > 0:
            self.perceptual_loss = self.perceptual_loss.to(pred.device)
            pred_img = self.unpatchify(pred)
            mask_img = mask.reshape(-1, *self.actual_grid_size).unsqueeze(1).float()
            mask_img = F.interpolate(mask_img, size=(pred_img.shape[2], pred_img.shape[3]), mode='nearest')
            perc_loss = self.perceptual_loss(pred_img, imgs, mask=mask_img)

        # --- SSIM Loss ---
        struct_loss = torch.tensor(0.0, device=pred.device)
        if self.ssim_loss_weight > 0:
            if pred_img is None: # Unpatchify if not done for perceptual loss
                 pred_img = self.unpatchify(pred)
            pred_img_scaled = (pred_img + 1.0) / 2.0 # Scale [-1, 1] to [0, 1]
            imgs_scaled = (imgs + 1.0) / 2.0
            ssim_val = ssim(pred_img_scaled, imgs_scaled, data_range=1.0, size_average=True)
            struct_loss = 1.0 - ssim_val

        # --- PatchNCE Loss ---
        nce_loss = torch.tensor(0.0, device=pred.device)
        if self.patchnce_loss is not None and self.patchnce_loss_weight > 0:
            # Select features corresponding to masked patches
            masked_indices = mask.bool() # [N, L_initial] boolean mask
            feat_q = final_decoder_features[masked_indices] # [Num_masked_total, D_decoder_final]
            feat_k_raw = target[masked_indices] # [Num_masked_total, p*p*C]

            if feat_q.numel() > 0 and feat_k_raw.numel() > 0:
                 # Ensure projectors are on the correct device
                 self.nce_projector_q = self.nce_projector_q.to(pred.device)
                 self.nce_projector_k = self.nce_projector_k.to(pred.device)

                 # Project features
                 q_proj = self.nce_projector_q(feat_q)
                 k_proj = self.nce_projector_k(feat_k_raw.float()) # Ensure input is float

                 # Update batch size for loss calculation if needed
                 self.patchnce_loss.batch_size = imgs.shape[0]
                 nce_loss = self.patchnce_loss(q_proj, k_proj)
            else:
                 nce_loss = torch.tensor(0.0, device=pred.device)


        # Combine losses
        total_loss = reconstruction_loss \
                     + self.perceptual_loss_weight * perc_loss \
                     + self.ssim_loss_weight * struct_loss \
                     + self.patchnce_loss_weight * nce_loss # Add NCE loss

        # For logging purposes, maybe return individual losses too?
        # return total_loss, reconstruction_loss, perc_loss, struct_loss, nce_loss
        return total_loss


    def forward(self, imgs, mask_ratio=0.75):
        # Forward pass with Hierarchical Decoder
        # Encoder now returns latent_full (full sequence), mask, intermediate_features, ids_restore
        latent_full_enc, mask, intermediate_features, ids_restore = self.forward_encoder(imgs, mask_ratio)
        # Decoder now takes latent_full_enc, intermediate_features, mask, ids_restore
        # and returns prediction and final features before head
        pred, final_decoder_features = self.forward_decoder(latent_full_enc, intermediate_features, mask, ids_restore)
        # Pass original images `imgs`, prediction, final features, and mask to forward_loss
        loss = self.forward_loss(imgs, pred, final_decoder_features, mask)
        return loss, pred, mask


# --- Training Function ---
# (setup_mae_training and train_epoch_mae remain largely the same)

def setup_mae_training(model_params, optimizer_params, device):
    """设置 MAE 训练的优化器和调度器"""
    lr = optimizer_params.get('lr', 1.5e-4) # Base LR
    weight_decay = optimizer_params.get('weight_decay', 0.05)
    optimizer = optim.AdamW(model_params, lr=lr, betas=(0.9, 0.95), weight_decay=weight_decay)
    # Cosine scheduler T_max depends on total epochs *after* warmup
    scheduler = CosineAnnealingLR(optimizer, T_max=optimizer_params['epochs_after_warmup'], eta_min=lr/100)
    scaler = torch.cuda.amp.GradScaler(enabled=optimizer_params.get('use_amp', True))
    return optimizer, scheduler, scaler

def train_epoch_mae(model, train_loader, optimizer, scaler, device, writer, epoch, total_epochs, base_lr, warmup_epochs, gradient_accumulation_steps=1, log_interval=50, use_amp=True): # Added gradient_accumulation_steps
    """MAE 训练循环 with warmup and gradient accumulation"""
    model.train()
    total_loss = 0
    accumulated_loss = 0.0 # Track loss over accumulation steps
    num_batches = len(train_loader)
    processed_batches = 0

    with tqdm(train_loader, unit="batch") as pbar:
        for batch_idx, imgs in enumerate(pbar):
            if imgs is None or imgs.nelement() == 0:
                print(f"Skipping empty batch at index {batch_idx}")
                continue

            global_step = epoch * num_batches + batch_idx
            # Adjust learning rate using linear warmup
            if epoch < warmup_epochs:
                num_warmup_steps = warmup_epochs * num_batches
                if num_warmup_steps > 0:
                    current_step = global_step + 1
                    lr_scale = min(1.0, float(current_step) / num_warmup_steps)
                    new_lr = base_lr * lr_scale
                    for param_group in optimizer.param_groups:
                        param_group['lr'] = new_lr
            # After warmup, the scheduler (stepped per epoch) handles decay.

            imgs = imgs.to(device, non_blocking=True)
            # optimizer.zero_grad() # Moved to after accumulation step

            with torch.cuda.amp.autocast(enabled=use_amp):
                loss, _, _ = model(imgs, mask_ratio=0.75) # Default mask ratio
                # Normalize loss for accumulation
                loss = loss / gradient_accumulation_steps

            # Accumulate scaled loss
            scaler.scale(loss).backward()

            current_loss_value = loss.item() * gradient_accumulation_steps # Log the non-normalized loss

            if math.isnan(current_loss_value):
                print(f"Warning: NaN loss detected at epoch {epoch+1}, batch {batch_idx}. Skipping gradient step.")
                # Skip optimizer step if loss is NaN, but zero grad for next accumulation cycle
                if (batch_idx + 1) % gradient_accumulation_steps == 0 or (batch_idx + 1) == num_batches:
                    optimizer.zero_grad(set_to_none=True) # Use set_to_none=True for potential efficiency
                continue

            accumulated_loss += current_loss_value # Accumulate for average calculation

            # Perform optimizer step after accumulating gradients
            if (batch_idx + 1) % gradient_accumulation_steps == 0 or (batch_idx + 1) == num_batches:
                scaler.step(optimizer)
                scaler.update()
                optimizer.zero_grad(set_to_none=True) # Zero gradients after step

            processed_batches += 1
            # Display the loss for the current batch (non-accumulated)
            pbar.set_postfix(loss=f"{current_loss_value:.4f}", lr=f"{optimizer.param_groups[0]['lr']:.1e}")

            # Log less frequently if accumulating gradients
            if global_step % (log_interval * gradient_accumulation_steps) == 0:
                 writer.add_scalar('Loss/batch', current_loss_value, global_step)
                 writer.add_scalar('LR', optimizer.param_groups[0]['lr'], global_step)

    # Calculate average loss based on accumulated loss and number of batches processed
    avg_loss = accumulated_loss / processed_batches if processed_batches > 0 else 0
    return avg_loss


def pretrain_mae(args): # Pass args directly
    """Swin MAE 自监督预训练函数"""
    # Calculate effective batch size
    effective_batch_size = args.batch_size * args.gradient_accumulation_steps
    print(f"Effective batch size: {args.batch_size} * {args.gradient_accumulation_steps} = {effective_batch_size}")

    device = torch.device('cuda' if torch.cuda.is_available() else 'cpu')
    print(f"Using device: {device}")
    print(f"Swin MAE Pretraining Parameters: {args}")

    os.makedirs(args.checkpoint_dir, exist_ok=True)
    os.makedirs(args.log_dir, exist_ok=True)
    writer = SummaryWriter(args.log_dir)

    # Instantiate Swin MAE model
    model = MaskedAutoencoderSwin(
        img_size=args.img_size,
        patch_size=args.patch_size,
        in_chans=args.in_chans,
        embed_dim=args.swin_embed_dim, # Use Swin specific embed_dim
        depths=args.swin_depths,
        num_heads=args.swin_num_heads,
        window_size=args.swin_window_size, # Attention window size
        masking_window_size=args.masking_window_size, # Pass masking window size
        mlp_ratio=4.0, # Standard MLP ratio
        norm_layer=nn.LayerNorm,
        ape=args.swin_ape, # Absolute Position Embedding flag
        patch_norm=True, # Usually True for Swin
        decoder_embed_dim=args.decoder_embed_dim,
        decoder_depths=args.decoder_depths, # Pass per-stage depths
        decoder_num_heads=args.decoder_num_heads, # Pass per-stage heads
        norm_pix_loss=args.norm_pix_loss,
        perceptual_loss_weight=args.perceptual_loss_weight, # Pass perceptual loss weight
        perc_layers_resnet=args.perc_layers_resnet,
        perc_norm_ct_resnet=args.perc_norm_ct_resnet,
        ssim_loss_weight=args.ssim_loss_weight,
        patchnce_loss_weight=args.patchnce_loss_weight, # Pass PatchNCE weight
        nce_proj_dim=args.nce_proj_dim, # Pass NCE projector dimension
        drop_path_rate=args.drop_path_rate # Pass drop path rate
    ).to(device)

    print(f"Swin MAE Model Parameters: {sum(p.numel() for p in model.parameters() if p.requires_grad):,}")

    # Get MAE data loader
    train_loader = get_mae_loader(
        data_dir=args.data_dir,
        batch_size=args.batch_size,
        num_workers=args.num_workers,
        crop_size=args.img_size, # Crop size must match model img_size
        augment=True
        # Add clip_min/max here if needed by loader
    )

    # Setup optimizer and scheduler
    optimizer_params = {
        'lr': args.lr,
        'epochs_after_warmup': max(0, args.epochs - args.warmup_epochs), # Ensure non-negative T_max
        'weight_decay': args.weight_decay,
        'use_amp': args.use_amp
    }
    optimizer, scheduler, scaler = setup_mae_training(model.parameters(), optimizer_params, device)

    start_epoch = 0
    best_loss = float('inf')

    # Resume logic
    if args.resume and os.path.isfile(args.resume):
        print(f"Resuming from checkpoint: {args.resume}")
        checkpoint = torch.load(args.resume, map_location='cpu')
        # Use strict=False to handle potential mismatches if model structure changed slightly
        msg = model.load_state_dict(checkpoint['model_state_dict'], strict=False)
        print(f"Model load_state_dict message: {msg}")
        if 'optimizer_state_dict' in checkpoint:
             optimizer.load_state_dict(checkpoint['optimizer_state_dict'])
        if 'scheduler_state_dict' in checkpoint and hasattr(scheduler, 'load_state_dict'):
             # Check if T_max matches before loading scheduler state
             expected_T_max = max(0, args.epochs - args.warmup_epochs)
             if hasattr(scheduler, 'T_max') and scheduler.T_max == expected_T_max:
                 scheduler.load_state_dict(checkpoint['scheduler_state_dict'])
             else:
                 print(f"Warning: Scheduler T_max mismatch (expected {expected_T_max}, got {getattr(scheduler, 'T_max', 'N/A')}), not loading scheduler state.")
        start_epoch = checkpoint.get('epoch', 0)
        best_loss = checkpoint.get('loss', float('inf'))
        if args.use_amp and 'scaler_state_dict' in checkpoint and checkpoint['scaler_state_dict'] is not None:
            scaler.load_state_dict(checkpoint['scaler_state_dict'])
        print(f"Resumed from epoch {start_epoch}")
        # Adjust scheduler's last_epoch correctly after resuming
        if 'scheduler_state_dict' in checkpoint and hasattr(scheduler, 'load_state_dict') and hasattr(scheduler, 'T_max') and scheduler.T_max == expected_T_max:
             # Set last_epoch based on the resumed epoch, considering warmup
             scheduler.last_epoch = max(0, start_epoch - args.warmup_epochs) - 1


    # Training loop
    for epoch in range(start_epoch, args.epochs):
        print(f"\nEpoch {epoch+1}/{args.epochs}:")
        avg_loss = train_epoch_mae(
            model, train_loader, optimizer, scaler, device, writer, epoch,
            total_epochs=args.epochs, base_lr=args.lr, warmup_epochs=args.warmup_epochs,
            gradient_accumulation_steps=args.gradient_accumulation_steps, # Pass grad accum steps
            use_amp=args.use_amp
        )

        # Step the scheduler only *after* the warmup phase
        if epoch >= args.warmup_epochs:
             if hasattr(scheduler, 'step'):
                 scheduler.step()
             else:
                 print(f"Warning: Scheduler {type(scheduler)} does not have step method.")


        current_lr = optimizer.param_groups[0]['lr']
        print(f"Epoch {epoch+1}/{args.epochs} completed. Average Loss: {avg_loss:.6f}, Current LR: {current_lr:.6f}")
        writer.add_scalar('Loss/train_epoch', avg_loss, epoch)
        writer.add_scalar('LR_epoch', current_lr, epoch)

        is_best = avg_loss < best_loss
        if is_best:
            best_loss = avg_loss
            print(f"New best loss: {best_loss:.6f}")

        # Save checkpoint logic (consider saving args used for this run)
        if (epoch + 1) % args.save_interval == 0 or is_best:
             # Save args relevant to this specific model configuration
             saved_args = {k: v for k, v in vars(args).items()}
             checkpoint_data = {
                 'epoch': epoch + 1,
                 'model_state_dict': model.state_dict(),
                 'optimizer_state_dict': optimizer.state_dict(),
                 'scheduler_state_dict': scheduler.state_dict() if hasattr(scheduler, 'state_dict') else None,
                 'scaler_state_dict': scaler.state_dict() if args.use_amp else None,
                 'loss': avg_loss,
                 'args': saved_args # Save all args used for this run
             }
             save_path = f"{args.checkpoint_dir}/checkpoint_epoch{epoch+1}.pth"
             torch.save(checkpoint_data, save_path)
             print(f"Saved checkpoint: {save_path}")
             if is_best:
                 best_path = f"{args.checkpoint_dir}/best_model.pth"
                 torch.save(checkpoint_data, best_path)
                 print(f"Saved best model to: {best_path}")


    writer.close()
    print("Swin MAE Pretraining completed!")


# --- Main execution block ---
if __name__ == '__main__':
    import argparse
    parser = argparse.ArgumentParser('Swin MAE pretraining script', add_help=False)

    # Model Parameters (Encoder - Swin Specific)
    parser.add_argument('--img_size', default=256, type=int, help='images input size')
    parser.add_argument('--patch_size', default=4, type=int, help='Swin patch size (usually 4)')
    parser.add_argument('--in_chans', default=1, type=int, help='Input channels')
    parser.add_argument('--swin_embed_dim', default=96, type=int, help='Swin encoder embedding dimension (e.g., 96 for Tiny/Small, 128 for Base)')
    parser.add_argument('--swin_depths', type=int, nargs='+', default=[2, 2, 6, 2], help='Swin encoder depth of each stage')
    parser.add_argument('--swin_num_heads', type=int, nargs='+', default=[3, 6, 12, 24], help='Swin encoder number of attention heads in different stages')
    parser.add_argument('--swin_window_size', type=int, default=7, help='Swin attention window size')
    parser.add_argument('--masking_window_size', type=int, default=4, help='Window size for masking (e.g., 4x4 patches)') # Added masking window size arg
    parser.add_argument('--swin_ape', action='store_false', default=True, help='Disable absolute position embedding in Swin encoder (default uses APE)')
    parser.add_argument('--drop_path_rate', type=float, default=0.1, help='Stochastic depth rate for encoder and decoder') # Added drop path rate

    # Model Parameters (Decoder)
    parser.add_argument('--decoder_embed_dim', default=512, type=int, help='Decoder embedding dimension')
    # parser.add_argument('--decoder_depth', default=8, type=int, help='Overall decoder depth (REMOVED - use depths)')
    # parser.add_argument('--decoder_num_heads', default=16, type=int, help='Decoder heads (REMOVED - use heads)')
    parser.add_argument('--decoder_depths', type=int, nargs='+', default=[2, 2, 6, 2], help='Decoder depth per stage (shallow to deep)')
    parser.add_argument('--decoder_num_heads', type=int, nargs='+', default=[3, 6, 12, 24], help='Decoder heads per stage (shallow to deep)')
    parser.add_argument('--norm_pix_loss', action='store_true', default=False, help='Enable normalized pixel loss (default uses standard pixel loss)')
    parser.add_argument('--perceptual_loss_weight', type=float, default=0.01, help='Weight for ResNet18 perceptual loss (0 to disable)') # Default lowered
    parser.add_argument('--perc_layers_resnet', type=str, nargs='+', default=['layer2', 'layer3'], help='ResNet layers for perceptual loss (e.g., layer2 layer3)')
    parser.add_argument('--perc_norm_ct_resnet', action='store_true', default=True, help='Use custom CT normalization for ResNet perceptual loss')
    parser.add_argument('--no_perc_norm_ct_resnet', action='store_false', dest='perc_norm_ct_resnet', help='Disable custom CT normalization for ResNet perceptual loss') # Allows disabling via flag
    parser.add_argument('--ssim_loss_weight', type=float, default=0.05, help='Weight for SSIM loss (0 to disable)') # Add SSIM weight arg
    parser.add_argument('--patchnce_loss_weight', type=float, default=0.1, help='Weight for PatchNCE loss (0 to disable)') # Add PatchNCE weight arg
    parser.add_argument('--nce_proj_dim', type=int, default=256, help='Projection dimension for PatchNCE features') # Add NCE projector dim arg

    # Training Parameters
    parser.add_argument('--batch_size', default=64, type=int, help='Batch size per GPU')
    parser.add_argument('--epochs', default=400, type=int) # Increased epochs
    parser.add_argument('--lr', type=float, default=1.5e-4, help='Base learning rate')
    parser.add_argument('--warmup_epochs', type=int, default=80, help='Epochs to warmup LR') # Increased warmup epochs
    parser.add_argument('--weight_decay', type=float, default=0.05)
    parser.add_argument('--use_amp', action='store_true', help='Enable mixed precision training')
    parser.set_defaults(use_amp=False) # Disable AMP by default
    parser.add_argument('--gradient_accumulation_steps', type=int, default=1, help='Number of steps to accumulate gradients before updating weights') # Add grad accum arg

    # Dataset Parameters
    parser.add_argument('--data_dir', default='E:/vscode/2号CT数据', type=str, help='dataset path') # Updated path
    parser.add_argument('--num_workers', default=8, type=int)

    # Checkpoint/Log Parameters
    parser.add_argument('--checkpoint_dir', default='checkpoints/swin_mae_ultra_plus', help='path where to save checkpoints') # Updated default
    parser.add_argument('--log_dir', default='logs/swin_mae_ultra_plus', help='path where to tensorboard log') # Updated default
    parser.add_argument('--save_interval', default=20, type=int, help='Save checkpoint every x epochs')
    parser.add_argument('--resume', default='', help='resume from checkpoint')

    args = parser.parse_args()

    pretrain_mae(args)
