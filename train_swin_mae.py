import torch
import torch.nn as nn
import torch.nn.functional as F
import torch.optim as optim
from torch.optim.lr_scheduler import CosineAnnealingLR
from data.mae_loader import get_mae_loader # Import loader
import timm # Import timm
assert timm.__version__ >= "0.3.2" # MAE requires timm support, assert version
# Use timm's Block for ViT-style decoder
from timm.models.vision_transformer import Block # Changed to ViT Block
# from timm.models.swin_transformer import SwinTransformerBlock # Removed Swin Block import
from tqdm import tqdm
import os
import numpy as np
import matplotlib.pyplot as plt
from torch.utils.tensorboard import SummaryWriter
import math
import random
from util.pos_embed import get_2d_sincos_pos_embed # Assuming pos embed helper is in util
# import util.lr_sched as lr_sched # Keep import for now, even if not directly used


# --------------------------------------------------------
# Helper function for masking (moved outside class)
# --------------------------------------------------------
def random_masking(N, L, mask_ratio, device):
    """ Perform per-sample random masking by shuffling indices """
    len_keep = int(L * (1 - mask_ratio))
    noise = torch.rand(N, L, device=device)
    ids_shuffle = torch.argsort(noise, dim=1)
    ids_restore = torch.argsort(ids_shuffle, dim=1)
    # ids_keep = ids_shuffle[:, :len_keep] # Not needed by caller in this approach
    mask = torch.ones([N, L], device=device)
    mask[:, :len_keep] = 0
    # Unshuffle the mask to match the original patch order
    mask = torch.gather(mask, dim=1, index=ids_restore)
    return mask # mask is 1 for masked, 0 for visible


# --------------------------------------------------------
# Based on MAE implementation by Meta Platforms, Inc. and Swin Transformer
# --------------------------------------------------------
class MaskedAutoencoderSwin(nn.Module):
    """ Masked Autoencoder with Swin Transformer backbone
    """
    def __init__(self, img_size=256, patch_size=4, in_chans=1, # Swin uses patch_size=4 typically
                 embed_dim=96, depths=[2, 2, 6, 2], num_heads=[3, 6, 12, 24], # Swin-T defaults
                 window_size=7, mlp_ratio=4., qkv_bias=True, qk_scale=None,
                 drop_rate=0., attn_drop_rate=0., drop_path_rate=0.1,
                 norm_layer=nn.LayerNorm, ape=False, patch_norm=True, # Swin specific params
                 decoder_embed_dim=512, decoder_depth=8, decoder_num_heads=16, # Decoder params (ViT style for now)

                 decoder_mlp_ratio=4., decoder_norm_layer=nn.LayerNorm, # Decoder specific norm/mlp
                 norm_pix_loss=False):
        super().__init__()

        self.patch_size = patch_size # Store patch size
        self.in_chans = in_chans # Store in_chans

        # --------------------------------------------------------------------------
        # Swin MAE encoder specifics
        self.encoder = timm.create_model(
            'swin_tiny_patch4_window7_224', # Reverted to standard tiny model name
            pretrained=False,
            in_chans=in_chans,
            img_size=img_size, # Timm handles img_size mismatch from 224
            patch_size=patch_size, # Should be 4 for standard Swin
            embed_dim=embed_dim,
            depths=depths,
            num_heads=num_heads,
            window_size=window_size,
            mlp_ratio=mlp_ratio,
            qkv_bias=qkv_bias,
            qk_scale=qk_scale,
            drop_rate=drop_rate,
            attn_drop_rate=attn_drop_rate,
            drop_path_rate=drop_path_rate,
            norm_layer=norm_layer,
            ape=ape, # Absolute Position Embedding
            patch_norm=patch_norm,
            num_classes=0, # No head
            global_pool='' # No pooling
        )

        # --- MAE specific additions/modifications ---
        # Determine the actual output dimension from the encoder's final norm layer
        try:
            self.actual_encoder_output_dim = self.encoder.norm.normalized_shape[0]
            print(f"Dynamically determined encoder output dim: {self.actual_encoder_output_dim}")
        except AttributeError:
            print("Warning: Could not dynamically determine encoder output dim from norm layer. Calculating.")
            self.actual_encoder_output_dim = int(embed_dim * 2**(len(depths) - 1))
            print(f"Calculated encoder output dim: {self.actual_encoder_output_dim}")


        self.patch_embed = self.encoder.patch_embed
        self.embed_dim = embed_dim # Store embed_dim

        # --- Use grid_size from patch_embed ---
        self.actual_grid_size = self.patch_embed.grid_size
        self.num_patches = self.actual_grid_size[0] * self.actual_grid_size[1]
        print(f"Using grid_size from patch_embed: {self.actual_grid_size}, num_patches: {self.num_patches}")

        # --- Calculate final grid size and patch count after encoder downsampling ---
        downsample_factor = 2**(len(depths) - 1) # Number of patch merging stages = len(depths) - 1
        self.grid_final = (self.actual_grid_size[0] // downsample_factor, self.actual_grid_size[1] // downsample_factor)
        self.num_patches_final = self.grid_final[0] * self.grid_final[1]
        print(f"Calculated final grid size after encoder: {self.grid_final}, num_patches_final: {self.num_patches_final}")


        # --- Absolute Position Embedding (APE) ---
        if ape:
             self.pos_embed = None # Set to None, as timm model handles APE internally when ape=True
             print("Using absolute positional embedding from Swin config (handled internally by timm model).")
        else:
            # Create APE if not provided by Swin config
            self.pos_embed = nn.Parameter(torch.zeros(1, self.num_patches, embed_dim)) # Use self.num_patches
            print("Created absolute positional embedding for Swin MAE.")
            # Initialize APE using sin-cos, use self.actual_grid_size[0]
            pos_embed_data = get_2d_sincos_pos_embed(self.pos_embed.shape[-1], self.actual_grid_size[0], cls_token=False)
            self.pos_embed.data.copy_(torch.from_numpy(pos_embed_data).float().unsqueeze(0))
            print("Initialized custom absolute positional embedding.")


        # --------------------------------------------------------------------------
        # MAE decoder specifics
        # Project encoder features to decoder dimension using the *actual* encoder output dim
        self.decoder_embed = nn.Linear(self.actual_encoder_output_dim, decoder_embed_dim, bias=True)
        # Decoder positional embedding (sized for *final* number of patches like original)
        self.decoder_pos_embed = nn.Parameter(torch.zeros(1, self.num_patches_final, decoder_embed_dim), requires_grad=False) # Use self.num_patches_final
        print(f"Initialized decoder_pos_embed with size: {self.decoder_pos_embed.shape}")


        # --- REMOVED Encoder Mask Token ---

        # --- Using ViT Block for decoder ---
        self.decoder_blocks = nn.ModuleList([
            Block(
                dim=decoder_embed_dim,
                num_heads=decoder_num_heads,
                mlp_ratio=decoder_mlp_ratio, # Use decoder specific MLP ratio
                qkv_bias=qkv_bias, # Use encoder's qkv_bias setting
                attn_drop=attn_drop_rate, # Use encoder's attn_drop_rate
                drop_path=0.0, # Typically no stochastic depth in decoder
                norm_layer=decoder_norm_layer # Use decoder specific norm layer
                )
            for i in range(decoder_depth)])

        self.decoder_norm = decoder_norm_layer(decoder_embed_dim)
        self.decoder_pred = nn.Linear(decoder_embed_dim, patch_size**2 * in_chans, bias=True)
        # --------------------------------------------------------------------------

        self.norm_pix_loss = norm_pix_loss

        self.initialize_weights() # Call init after defining all layers

    def initialize_weights(self):
        # Initialize APE if we created it (already done in __init__)

        # Initialize decoder pos_embed using sin-cos, use self.grid_final[0] (corresponding to self.num_patches_final)
        decoder_pos_embed = get_2d_sincos_pos_embed(self.decoder_pos_embed.shape[-1], self.grid_final[0], cls_token=False) # Use grid_final
        self.decoder_pos_embed.data.copy_(torch.from_numpy(decoder_pos_embed).float().unsqueeze(0))
        print(f"Initialized decoder positional embedding using grid_final: {self.grid_final}") # Updated print

        # REMOVED: Initialize mask_token
        # REMOVED: Initialize encoder mask_token

        # Initialize linear layers and layer norms
        self.apply(self._init_weights)

    def _init_weights(self, m):
        if isinstance(m, nn.Linear):
            torch.nn.init.xavier_uniform_(m.weight)
            if isinstance(m, nn.Linear) and m.bias is not None:
                nn.init.constant_(m.bias, 0)
        elif isinstance(m, nn.LayerNorm):
            nn.init.constant_(m.bias, 0)
            nn.init.constant_(m.weight, 1.0)

    # Removed random_masking method from class

    # Modified forward_encoder: No masking here, just encode full image
    def forward_encoder(self, x): # Removed mask_ratio argument
        # x: [N, C, H, W]
        N, C, H, W = x.shape # Use N instead of B
        patch_size = self.patch_size # Use stored patch_size
        embed_dim = self.encoder.embed_dim # Use embed_dim from the loaded Swin model

        # 1. Patch Embedding
        x = self.patch_embed(x) # Output shape depends on timm version/config.
        # Ensure output is [N, L, D]
        if x.dim() == 4:
            x = x.flatten(1, 2) # -> [B, H*W, C] = [B, L, C]
        N, L, D = x.shape # Get L and D directly from the output shape

        # 2. Add Position Embedding (if applicable)
        if self.pos_embed is not None:
            if self.pos_embed.shape[1] != L:
                 raise ValueError(f"Position embedding length mismatch: Expected {L}, Got {self.pos_embed.shape[1]}")
            if self.pos_embed.shape[2] != D:
                 raise ValueError(f"Position embedding dim mismatch: Expected {D}, Got {self.pos_embed.shape[2]}")
            x = x + self.pos_embed # Add APE [N, L, D]

        # 3. Pass the *unmasked* full sequence through Swin stages
        H_grid, W_grid = self.actual_grid_size
        assert H_grid * W_grid == L, f"Stored grid size {H_grid}x{W_grid} does not match sequence length {L}"
        x_spatial = x.reshape(N, H_grid, W_grid, D) # Reshape to [N, H_grid, W_grid, D]

        for layer in self.encoder.layers:
            x_spatial = layer(x_spatial) # Output: [N, H_out, W_out, C_out]

        # Flatten back to sequence for final normalization
        N_out, H_out, W_out, C_out = x_spatial.shape
        x_seq = x_spatial.view(N_out, H_out * W_out, C_out) # Flatten to [N, L_final, C_out]

        # Apply final encoder normalization
        latent_full = self.encoder.norm(x_seq) # [N, L_final, D_encoder_output]

        assert latent_full.shape[1] == self.num_patches_final, \
            f"Encoder output sequence length mismatch: Expected {self.num_patches_final}, Got {latent_full.shape[1]}"
        assert latent_full.shape[-1] == self.actual_encoder_output_dim, \
             f"Encoder final output dim mismatch: Expected {self.actual_encoder_output_dim}, Got {latent_full.shape[-1]}"

        return latent_full # Return the full latent output [N, L_final, D_out]

    # Modified forward_decoder: No masking/unshuffling here, just decode
    def forward_decoder(self, x): # Input x: [N, L_final, D_decoder]
        # x: projected latent features from encoder [N, L_final, D_decoder]

        # Add decoder pos embed (sized for L_final)
        assert self.decoder_pos_embed.shape[1] == x.shape[1], \
            f"Decoder pos embed length mismatch: Expected {x.shape[1]}, Got {self.decoder_pos_embed.shape[1]}"
        assert self.decoder_pos_embed.shape[2] == x.shape[2], \
            f"Decoder pos embed dim mismatch: Expected {x.shape[2]}, Got {self.decoder_pos_embed.shape[2]}"
        x = x + self.decoder_pos_embed # [N, L_final, D_decoder]

        # Apply decoder blocks (ViT Block)
        for blk in self.decoder_blocks:
            x = blk(x)
        x = self.decoder_norm(x) # Output: [N, L_final, D_decoder]

        # Predictor projection
        pred_final = self.decoder_pred(x) # [N, L_final, patch_size^2 * C]

        return pred_final

    def patchify(self, imgs):
        """ imgs: (N, C, H, W) -> (N, L, patch_size**2 * C) """
        p = self.patch_size
        assert imgs.shape[2] == imgs.shape[3] and imgs.shape[2] % p == 0
        h = w = imgs.shape[2] // p
        c = imgs.shape[1]
        x = imgs.reshape(shape=(imgs.shape[0], c, h, p, w, p))
        x = torch.einsum('nchpwq->nhwpqc', x)
        x = x.reshape(shape=(imgs.shape[0], h * w, p**2 * c))
        return x

    def unpatchify(self, x):
        """ x: (N, L, patch_size**2 * C) -> (N, C, H, W) """
        p = self.patch_size
        h, w = self.actual_grid_size # Use initial grid size for unpatchify
        assert h * w == x.shape[1], f"h*w ({h*w}) from grid_size does not match L ({x.shape[1]})"
        c = x.shape[2] // (p**2)
        assert x.shape[2] == p**2 * c, f"Decoder prediction dim {x.shape[2]} != p*p*C ({p**2 * c})"

        x = x.reshape(shape=(x.shape[0], h, w, p, p, c))
        x = torch.einsum('nhwpqc->nchpwq', x)
        imgs = x.reshape(shape=(x.shape[0], c, h * p, w * p))
        return imgs

    def forward_loss(self, imgs, pred, mask):
        """
        imgs: [N, C, H, W] - Original images
        pred: [N, L_initial, p*p*C] - Predicted patch values (upsampled)
        mask: [N, L_initial], 1 is remove/masked
        """
        target = self.patchify(imgs) # Target shape: [N, L_initial, p*p*C]
        if self.norm_pix_loss:
            mean = target.mean(dim=-1, keepdim=True)
            var = target.var(dim=-1, keepdim=True)
            target = (target - mean) / (var + 1.e-6)**.5

        loss = (pred - target) ** 2
        loss = loss.mean(dim=-1)  # [N, L_initial], mean loss per patch

        mask_sum = mask.sum()
        loss = (loss * mask).sum() / (mask_sum + 1e-8) # Mean loss on removed patches
        return loss

    def forward(self, imgs, mask_ratio=0.75):
        N, C, H_initial, W_initial = imgs.shape

        # 1. Encoder: Process full image, get final latent features
        latent_full = self.forward_encoder(imgs) # Shape: [N, L_final, D_encoder_output]

        # 2. Decoder: Project latent features and decode
        x_proj = self.decoder_embed(latent_full) # Shape: [N, L_final, D_decoder]
        pred_final = self.forward_decoder(x_proj) # Shape: [N, L_final, p*p*C]

        # 3. Upsample prediction to initial patch resolution
        N, L_final, C_pred = pred_final.shape
        H_final, W_final = self.grid_final # e.g., (8, 8)
        p = self.patch_size
        C_pix = C_pred // (p**2) # Should be self.in_chans

        assert L_final == H_final * W_final, "L_final mismatch"
        assert C_pred == p**2 * C_pix, "Prediction channel mismatch"

        # Reshape to spatial: [N, H_final, W_final, p, p, C_pix]
        pred_spatial = pred_final.reshape(N, H_final, W_final, p, p, C_pix)
        # Permute to [N, C_pix, H_final, W_final, p, p]
        pred_spatial = pred_spatial.permute(0, 5, 1, 2, 3, 4)
        # Combine patch dims with spatial dims: [N, C_pix, H_final*p, W_final*p]
        pred_low_res_img = pred_spatial.reshape(N, C_pix, H_final * p, W_final * p)

        # Upsample to original image resolution H, W
        pred_img = F.interpolate(
            pred_low_res_img,
            size=(H_initial, W_initial),
            mode='bilinear', # or 'nearest'
            align_corners=False
        ) # Shape: [N, C_pix, H_initial, W_initial]

        # Patchify the upsampled prediction to get [N, L_initial, p*p*C]
        pred_initial = self.patchify(pred_img) # Shape: [N, L_initial, p*p*C]

        # 4. Masking (based on L_initial)
        mask = random_masking(N, self.num_patches, mask_ratio, imgs.device) # mask=1 for masked

        # 5. Loss Calculation
        loss = self.forward_loss(imgs, pred_initial, mask) # Compare upsampled pred with target

        return loss, pred_initial, mask # Return initial scale pred and mask


# --- Training Function ---
# (setup_mae_training and train_epoch_mae remain largely the same)

def setup_mae_training(model_params, optimizer_params, device):
    """设置 MAE 训练的优化器和调度器"""
    lr = optimizer_params.get('lr', 1.5e-4) # Base LR
    weight_decay = optimizer_params.get('weight_decay', 0.05)
    optimizer = optim.AdamW(model_params, lr=lr, betas=(0.9, 0.95), weight_decay=weight_decay)
    # Cosine scheduler T_max depends on total epochs *after* warmup
    scheduler = CosineAnnealingLR(optimizer, T_max=optimizer_params['epochs_after_warmup'], eta_min=lr/100)
    scaler = torch.cuda.amp.GradScaler(enabled=optimizer_params.get('use_amp', True))
    return optimizer, scheduler, scaler

def train_epoch_mae(model, train_loader, optimizer, scaler, device, writer, epoch, total_epochs, base_lr, warmup_epochs, log_interval=50, use_amp=True):
    """MAE 训练循环 with warmup"""
    model.train()
    total_loss = 0
    num_batches = len(train_loader)
    processed_batches = 0

    with tqdm(train_loader, unit="batch") as pbar:
        for batch_idx, imgs in enumerate(pbar):
            if imgs is None or imgs.nelement() == 0:
                print(f"Skipping empty batch at index {batch_idx}")
                continue

            global_step = epoch * num_batches + batch_idx
            # Adjust learning rate using linear warmup
            if epoch < warmup_epochs:
                num_warmup_steps = warmup_epochs * num_batches
                if num_warmup_steps > 0:
                    current_step = global_step + 1
                    lr_scale = min(1.0, float(current_step) / num_warmup_steps)
                    new_lr = base_lr * lr_scale
                    for param_group in optimizer.param_groups:
                        param_group['lr'] = new_lr
            # After warmup, the scheduler (stepped per epoch) handles decay.

            imgs = imgs.to(device, non_blocking=True)
            optimizer.zero_grad()

            with torch.cuda.amp.autocast(enabled=use_amp):
                loss, _, _ = model(imgs, mask_ratio=0.75) # Default mask ratio

            scaler.scale(loss).backward()
            scaler.step(optimizer)
            scaler.update()

            current_loss = loss.item()
            if math.isnan(current_loss):
                print(f"Warning: NaN loss detected at epoch {epoch+1}, batch {batch_idx}. Skipping batch.")
                optimizer.zero_grad()
                continue

            total_loss += current_loss
            processed_batches += 1
            pbar.set_postfix(loss=f"{current_loss:.4f}", lr=f"{optimizer.param_groups[0]['lr']:.1e}")

            if batch_idx % log_interval == 0:
                writer.add_scalar('Loss/batch', current_loss, global_step)
                writer.add_scalar('LR', optimizer.param_groups[0]['lr'], global_step)

    avg_loss = total_loss / processed_batches if processed_batches > 0 else 0
    return avg_loss


def pretrain_mae(args): # Pass args directly
    """Swin MAE 自监督预训练函数"""
    device = torch.device('cuda' if torch.cuda.is_available() else 'cpu')
    print(f"Using device: {device}")
    print(f"Swin MAE Pretraining Parameters: {args}")

    os.makedirs(args.checkpoint_dir, exist_ok=True)
    os.makedirs(args.log_dir, exist_ok=True)
    writer = SummaryWriter(args.log_dir)

    # Instantiate Swin MAE model
    model = MaskedAutoencoderSwin(
        img_size=args.img_size,
        patch_size=args.patch_size,
        in_chans=args.in_chans,
        embed_dim=args.swin_embed_dim, # Use Swin specific embed_dim
        depths=args.swin_depths,
        num_heads=args.swin_num_heads,
        window_size=args.swin_window_size,
        mlp_ratio=4.0, # Standard MLP ratio
        norm_layer=nn.LayerNorm,
        ape=args.swin_ape, # Absolute Position Embedding flag
        patch_norm=True, # Usually True for Swin
        decoder_embed_dim=args.decoder_embed_dim,
        decoder_depth=args.decoder_depth,
        decoder_num_heads=args.decoder_num_heads,
        norm_pix_loss=args.norm_pix_loss
    ).to(device)

    print(f"Swin MAE Model Parameters: {sum(p.numel() for p in model.parameters() if p.requires_grad):,}")

    # Get MAE data loader
    train_loader = get_mae_loader(
        data_dir=args.data_dir,
        batch_size=args.batch_size,
        num_workers=args.num_workers,
        crop_size=args.img_size, # Crop size must match model img_size
        augment=True
    )

    # Setup optimizer and scheduler
    optimizer_params = {
        'lr': args.lr,
        'epochs_after_warmup': max(0, args.epochs - args.warmup_epochs), # Ensure non-negative T_max
        'weight_decay': args.weight_decay,
        'use_amp': args.use_amp
    }
    optimizer, scheduler, scaler = setup_mae_training(model.parameters(), optimizer_params, device)

    start_epoch = 0
    best_loss = float('inf')

    # Resume logic
    if args.resume and os.path.isfile(args.resume):
        print(f"Resuming from checkpoint: {args.resume}")
        checkpoint = torch.load(args.resume, map_location='cpu')
        msg = model.load_state_dict(checkpoint['model_state_dict'], strict=False)
        print(f"Model load_state_dict message: {msg}")
        if 'optimizer_state_dict' in checkpoint:
             optimizer.load_state_dict(checkpoint['optimizer_state_dict'])
        if 'scheduler_state_dict' in checkpoint and hasattr(scheduler, 'load_state_dict'):
             if scheduler.T_max == optimizer_params['epochs_after_warmup']:
                 scheduler.load_state_dict(checkpoint['scheduler_state_dict'])
             else:
                 print("Warning: Scheduler T_max mismatch, not loading scheduler state.")
        start_epoch = checkpoint.get('epoch', 0)
        best_loss = checkpoint.get('loss', float('inf'))
        if args.use_amp and 'scaler_state_dict' in checkpoint and checkpoint['scaler_state_dict'] is not None:
            scaler.load_state_dict(checkpoint['scaler_state_dict'])
        print(f"Resumed from epoch {start_epoch}")
        if 'scheduler_state_dict' in checkpoint and hasattr(scheduler, 'load_state_dict') and scheduler.T_max == optimizer_params['epochs_after_warmup']:
             scheduler.last_epoch = max(0, start_epoch - args.warmup_epochs) -1


    # Training loop
    for epoch in range(start_epoch, args.epochs):
        print(f"\nEpoch {epoch+1}/{args.epochs}:")
        avg_loss = train_epoch_mae(
            model, train_loader, optimizer, scaler, device, writer, epoch,
            total_epochs=args.epochs, base_lr=args.lr, warmup_epochs=args.warmup_epochs,
            use_amp=args.use_amp
        )

        if epoch >= args.warmup_epochs:
             if hasattr(scheduler, 'step'):
                 scheduler.step()
             else:
                 print(f"Warning: Scheduler {type(scheduler)} does not have step method.")


        current_lr = optimizer.param_groups[0]['lr']
        print(f"Epoch {epoch+1}/{args.epochs} completed. Average Loss: {avg_loss:.6f}, Current LR: {current_lr:.6f}")
        writer.add_scalar('Loss/train_epoch', avg_loss, epoch)
        writer.add_scalar('LR_epoch', current_lr, epoch)

        is_best = avg_loss < best_loss
        if is_best:
            best_loss = avg_loss
            print(f"New best loss: {best_loss:.6f}")

        if (epoch + 1) % args.save_interval == 0 or is_best:
             saved_args = {k: v for k, v in vars(args).items() if 'swin' in k or k in ['img_size', 'patch_size', 'decoder_embed_dim', 'decoder_depth', 'decoder_num_heads', 'lr', 'warmup_epochs', 'epochs', 'norm_pix_loss']}
             checkpoint_data = {
                 'epoch': epoch + 1,
                 'model_state_dict': model.state_dict(),
                 'optimizer_state_dict': optimizer.state_dict(),
                 'scheduler_state_dict': scheduler.state_dict() if hasattr(scheduler, 'state_dict') else None,
                 'scaler_state_dict': scaler.state_dict() if args.use_amp else None,
                 'loss': avg_loss,
                 'args': saved_args
             }
             save_path = f"{args.checkpoint_dir}/checkpoint_epoch{epoch+1}.pth"
             torch.save(checkpoint_data, save_path)
             print(f"Saved checkpoint: {save_path}")
             if is_best:
                 best_path = f"{args.checkpoint_dir}/best_model.pth"
                 torch.save(checkpoint_data, best_path)
                 print(f"Saved best model to: {best_path}")


    writer.close()
    print("Swin MAE Pretraining completed!")


# --- Main execution block ---
if __name__ == '__main__':
    import argparse
    parser = argparse.ArgumentParser('Swin MAE pretraining script', add_help=False)

    # Model Parameters (Encoder - Swin Specific)
    parser.add_argument('--img_size', default=256, type=int, help='images input size')
    parser.add_argument('--patch_size', default=4, type=int, help='Swin patch size (usually 4)')
    parser.add_argument('--in_chans', default=1, type=int, help='Input channels')
    parser.add_argument('--swin_embed_dim', default=96, type=int, help='Swin encoder embedding dimension (e.g., 96 for Tiny/Small, 128 for Base)')
    parser.add_argument('--swin_depths', type=int, nargs='+', default=[2, 2, 6, 2], help='Swin encoder depth of each stage')
    parser.add_argument('--swin_num_heads', type=int, nargs='+', default=[3, 6, 12, 24], help='Swin encoder number of attention heads in different stages')
    parser.add_argument('--swin_window_size', type=int, default=7, help='Swin window size')
    parser.add_argument('--swin_ape', action='store_false', default=True, help='Disable absolute position embedding in Swin encoder (default uses APE)')

    # Model Parameters (Decoder)
    parser.add_argument('--decoder_embed_dim', default=512, type=int, help='Decoder embedding dimension')
    parser.add_argument('--decoder_depth', default=8, type=int, help='Decoder depth')
    parser.add_argument('--decoder_num_heads', default=16, type=int, help='Decoder number of attention heads')
    parser.add_argument('--norm_pix_loss', action='store_true', default=False, help='Enable normalized pixel loss (default uses standard pixel loss)')

    # Training Parameters
    parser.add_argument('--batch_size', default=64, type=int, help='Batch size per GPU')
    parser.add_argument('--epochs', default=200, type=int)
    parser.add_argument('--lr', type=float, default=1.5e-4, help='Base learning rate')
    parser.add_argument('--warmup_epochs', type=int, default=40, help='Epochs to warmup LR')
    parser.add_argument('--weight_decay', type=float, default=0.05)
    parser.add_argument('--use_amp', action='store_true', help='Enable mixed precision training')
    parser.set_defaults(use_amp=False) # Disable AMP by default

    # Dataset Parameters
    parser.add_argument('--data_dir', default='E:/vscode/2号CT数据', type=str, help='dataset path') # Updated path
    parser.add_argument('--num_workers', default=8, type=int)

    # Checkpoint/Log Parameters
    parser.add_argument('--checkpoint_dir', default='checkpoints/swin_mae_pretrain', help='path where to save checkpoints') # Updated default
    parser.add_argument('--log_dir', default='logs/swin_mae_pretrain', help='path where to tensorboard log') # Updated default
    parser.add_argument('--save_interval', default=20, type=int, help='Save checkpoint every x epochs')
    parser.add_argument('--resume', default='', help='resume from checkpoint')

    args = parser.parse_args()

    pretrain_mae(args)
