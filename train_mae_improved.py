import torch
import torch.nn as nn
import torch.nn.functional as F
import torch.optim as optim
from torch.optim.lr_scheduler import CosineAnnealingLR
from data.mae_loader import get_mae_loader # Import new loader
import timm # Import timm
assert timm.__version__ >= "0.3.2" # MAE requires timm support, assert version
from timm.models.vision_transformer import Block # Use timm's Block for decoder
import torchvision.models as models # Import the whole module
from torchvision.models import vgg16 # Keep specific import if used elsewhere, or remove if only models.vgg16 is used
from torchmetrics.image import StructuralSimilarityIndexMeasure # For SSIM Loss
try:
    import kornia.filters as K # For Sobel filter gradient loss
except ImportError:
    print("Warning: kornia not installed. Gradient loss will not be available.")
    print("Install with: pip install kornia")
    K = None
from tqdm import tqdm
import os
import numpy as np
import matplotlib.pyplot as plt
from torch.utils.tensorboard import SummaryWriter
import math
import random
from util.pos_embed import get_2d_sincos_pos_embed # Assuming pos embed helper is in util
import util.lr_sched as lr_sched # Import lr_sched for warmup adjustment
import argparse # Import argparse
from datetime import datetime # Import datetime

# --------------------------------------------------------
# Based on MAE implementation by Meta Platforms, Inc.
# --------------------------------------------------------

# --- Perceptual Loss Helper ---
class VGGPerceptualLoss(nn.Module):
    def __init__(self, resize=True, feature_layers=[2, 7, 16, 25]): # Default layers in VGG16 corresponding to relu1_2, relu2_2, relu3_3, relu4_3
        super(VGGPerceptualLoss, self).__init__()
        blocks = []
        vgg = vgg16(weights=models.VGG16_Weights.IMAGENET1K_V1).features # Use weights argument
        self.feature_layers = feature_layers
        # Correctly iterate up to the max layer index needed
        max_needed_layer = max(feature_layers)
        for i, block in enumerate(vgg):
            blocks.append(block)
            if i == max_needed_layer: # Stop after adding the last required layer
                break

        for bl in blocks:
            for p in bl.parameters():
                p.requires_grad = False # Freeze VGG parameters

        self.blocks = nn.ModuleList(blocks)
        self.transform = nn.functional.interpolate # For resizing input if needed
        self.resize = resize
        self.register_buffer("mean", torch.tensor([0.485, 0.456, 0.406]).view(1, 3, 1, 1))
        self.register_buffer("std", torch.tensor([0.229, 0.224, 0.225]).view(1, 3, 1, 1))

    def forward(self, input, target):
        # Input and target are expected to be in range [0, 1] (or similar normalized range)
        # Replicate single channel to 3 channels for VGG
        if input.shape[1] != 3:
            input = input.repeat(1, 3, 1, 1)
        if target.shape[1] != 3:
            target = target.repeat(1, 3, 1, 1)

        # Normalize for VGG
        input = (input - self.mean.to(input.device)) / self.std.to(input.device)
        target = (target - self.mean.to(target.device)) / self.std.to(target.device)


        if self.resize:
            input = self.transform(input, mode='bilinear', size=(224, 224), align_corners=False)
            target = self.transform(target, mode='bilinear', size=(224, 224), align_corners=False)

        loss = 0.0
        x = input
        y = target
        # Iterate through VGG blocks to extract features at specified layers
        for i, block in enumerate(self.blocks):
            x = block(x)
            y = block(y)
            if i in self.feature_layers:
                loss += torch.nn.functional.l1_loss(x, y) # L1 loss between feature maps

        return loss

# --- Gradient Loss Helper ---
def gradient_loss(pred, target, device='cuda'):
    """Calculates L1 loss between Sobel gradients of prediction and target."""
    if K is None:
        # print("Kornia not available, skipping gradient loss.")
        return torch.tensor(0.0, device=device) # Return zero loss if kornia not installed

    # Ensure input tensors are on the correct device
    pred = pred.to(device)
    target = target.to(device)

    # Calculate Sobel gradients
    # Kornia's Sobel filter returns magnitude and direction, we only need magnitude
    # It expects input shape (N, C, H, W)
    try:
        pred_grad_x = K.sobel(pred, direction='x')
        pred_grad_y = K.sobel(pred, direction='y')
        target_grad_x = K.sobel(target, direction='x')
        target_grad_y = K.sobel(target, direction='y')

        # Calculate L1 loss on gradients
        grad_loss = F.l1_loss(pred_grad_x, target_grad_x) + F.l1_loss(pred_grad_y, target_grad_y)
    except Exception as e:
        print(f"Error during gradient calculation: {e}. Returning 0 loss.")
        grad_loss = torch.tensor(0.0, device=device)

    return grad_loss

# --- Block Masking Function ---
def block_random_masking(x, mask_ratio, block_size, patch_grid_size, device):
    """
    Perform per-sample block-wise random masking.
    Returns visible patches, patch-level mask (0=keep, 1=mask), and patch-level restore indices.
    x: [N, L, D], sequence where L = H_patch * W_patch
    mask_ratio: float, target ratio of patches to mask
    block_size: int, size of the square block (e.g., 4 means 4x4 patches)
    patch_grid_size: tuple (H_patch, W_patch)
    """
    N, L, D = x.shape
    H_patch, W_patch = patch_grid_size
    assert H_patch * W_patch == L, "L does not match patch grid size"
    assert H_patch % block_size == 0 and W_patch % block_size == 0, "Patch grid must be divisible by block size"

    num_blocks_h = H_patch // block_size
    num_blocks_w = W_patch // block_size
    num_blocks = num_blocks_h * num_blocks_w
    num_patches_per_block = block_size * block_size

    # Calculate how many blocks to keep to approximate the mask_ratio at the patch level
    num_patches_to_keep = int(L * (1 - mask_ratio))
    num_blocks_to_keep = math.ceil(num_patches_to_keep / num_patches_per_block) # Keep enough blocks
    # Adjust len_keep if it exceeds total blocks
    num_blocks_to_keep = min(num_blocks_to_keep, num_blocks)

    # Generate noise per block and shuffle block indices
    noise = torch.rand(N, num_blocks, device=device)
    ids_shuffle_blocks = torch.argsort(noise, dim=1)
    ids_restore_blocks = torch.argsort(ids_shuffle_blocks, dim=1)

    # Keep the first `num_blocks_to_keep` blocks
    ids_keep_blocks = ids_shuffle_blocks[:, :num_blocks_to_keep] # [N, num_blocks_to_keep]

    # Generate the block-level mask (0 is keep, 1 is remove)
    mask_blocks = torch.ones(N, num_blocks, device=device)
    mask_blocks.scatter_(1, ids_keep_blocks, 0) # Set kept blocks to 0
    # Unshuffle the block mask
    mask_blocks = torch.gather(mask_blocks, dim=1, index=ids_restore_blocks) # [N, num_blocks]

    # Expand block mask to patch mask
    mask_patches = mask_blocks.reshape(N, num_blocks_h, num_blocks_w)
    mask_patches = mask_patches.repeat_interleave(block_size, dim=1).repeat_interleave(block_size, dim=2) # [N, H_patch, W_patch]
    mask_patches = mask_patches.reshape(N, L) # [N, L] (0 is keep, 1 is mask)

    # Generate ids_keep and ids_restore at the patch level
    # ids_keep: indices of patches that are *not* masked (mask_patches == 0)
    # ids_restore: indices to restore the original order from a shuffled sequence
    ids_shuffle_patches = torch.argsort(mask_patches + torch.rand_like(mask_patches) * 1e-6, dim=1) # Sort by mask (0 first), add noise for stable sort
    ids_restore_patches = torch.argsort(ids_shuffle_patches, dim=1)

    # Calculate the actual number of patches kept based on the block masking
    len_keep_patches = (mask_patches == 0).sum(dim=1).min().item() # Use min across batch just in case

    # Keep the first `len_keep_patches` from the shuffled indices
    ids_keep_patches = ids_shuffle_patches[:, :len_keep_patches]

    # Gather the visible patches
    x_visible = torch.gather(x, dim=1, index=ids_keep_patches.unsqueeze(-1).repeat(1, 1, D))

    # Return visible patches, the patch-level mask (0=keep, 1=mask), and patch-level restore indices
    return x_visible, mask_patches, ids_restore_patches


class MaskedAutoencoderViT(nn.Module):
    """ Masked Autoencoder with VisionTransformer backbone
    """
    def __init__(self, img_size=256, patch_size=16, in_chans=1, # Changed patch_size default to 16
                 embed_dim=768, depth=12, num_heads=12,
                 decoder_embed_dim=512, decoder_depth=8, decoder_num_heads=16,
                 mlp_ratio=4., norm_layer=nn.LayerNorm, norm_pix_loss=True, # Default norm_pix_loss to True
                 masking_strategy='random', block_size=4): # Add masking strategy options
        super().__init__()

        self.img_size = img_size
        self.patch_size = patch_size # Store patch size
        self.in_chans = in_chans
        self.masking_strategy = masking_strategy
        self.block_size = block_size # Store block size for block masking

        # --------------------------------------------------------------------------
        # MAE encoder specifics
        # Use timm's VisionTransformer for the encoder
        # Determine the correct ViT model name based on patch size
        vit_model_name = f'vit_base_patch{patch_size}_{img_size}'
        try:
            self.encoder = timm.create_model(
                vit_model_name,
                pretrained=False,
                in_chans=in_chans,
                img_size=img_size,
                # patch_size=patch_size, # timm uses patch_size from model name
                embed_dim=embed_dim,
                depth=depth,
                num_heads=num_heads,
                mlp_ratio=mlp_ratio,
                norm_layer=norm_layer,
                global_pool='' # Important: disable global pooling for MAE encoder
            )
            print(f"Using timm model: {vit_model_name}")
        except Exception as e:
            print(f"Warning: Could not create timm model {vit_model_name}. Error: {e}. Falling back to vit_base_patch16_224.")
            # Fallback if the specific model name isn't found (e.g., patch_size 8)
            self.encoder = timm.create_model(
                'vit_base_patch16_224', # Fallback
                pretrained=False,
                in_chans=in_chans,
                img_size=img_size, # Still pass img_size for potential pos embed interpolation
                patch_size=patch_size, # Explicitly pass patch_size for fallback
                embed_dim=embed_dim,
                depth=depth,
                num_heads=num_heads,
                mlp_ratio=mlp_ratio,
                norm_layer=norm_layer,
                global_pool=''
            )


        # Remove the final norm and head from timm's ViT as we only need features
        if hasattr(self.encoder, 'norm'): del self.encoder.norm
        if hasattr(self.encoder, 'head'): del self.encoder.head
        # Access patch_embed and pos_embed from the timm model
        self.patch_embed = self.encoder.patch_embed
        self.pos_embed = self.encoder.pos_embed # timm ViT includes pos_embed
        self.num_patches = self.patch_embed.num_patches
        self.patch_grid_size = self.patch_embed.grid_size # Store grid size
        # --------------------------------------------------------------------------

        # --------------------------------------------------------------------------
        # MAE decoder specifics
        self.decoder_embed = nn.Linear(embed_dim, decoder_embed_dim, bias=True)

        self.mask_token = nn.Parameter(torch.zeros(1, 1, decoder_embed_dim))

        # Create decoder positional embedding (needs num_patches)
        self.decoder_pos_embed = nn.Parameter(torch.zeros(1, self.num_patches + 1, decoder_embed_dim), requires_grad=False)  # fixed sin-cos embedding

        # Create decoder blocks using timm's Block definition
        self.decoder_blocks = nn.ModuleList([
            Block(decoder_embed_dim, decoder_num_heads, mlp_ratio, qkv_bias=True, norm_layer=norm_layer)
            for i in range(decoder_depth)])

        self.decoder_norm = norm_layer(decoder_embed_dim)
        self.decoder_pred = nn.Linear(decoder_embed_dim, patch_size**2 * in_chans, bias=True) # decoder to patch
        # --------------------------------------------------------------------------

        self.norm_pix_loss = norm_pix_loss


        self.initialize_weights()

    def initialize_weights(self):
        # Initialize (and freeze) pos_embed by sin-cos embedding
        # Ensure grid size calculation is robust
        grid_size = self.patch_embed.grid_size
        pos_embed = get_2d_sincos_pos_embed(self.pos_embed.shape[-1], grid_size, cls_token=True)
        self.pos_embed.data.copy_(torch.from_numpy(pos_embed).float().unsqueeze(0))

        decoder_pos_embed = get_2d_sincos_pos_embed(self.decoder_pos_embed.shape[-1], grid_size, cls_token=True)
        self.decoder_pos_embed.data.copy_(torch.from_numpy(decoder_pos_embed).float().unsqueeze(0))

        # Initialize patch_embed like nn.Linear (instead of nn.Conv2d)
        w = self.patch_embed.proj.weight.data
        torch.nn.init.xavier_uniform_(w.view([w.shape[0], -1]))

        # Initialize cls_token and mask_token
        if hasattr(self.encoder, 'cls_token'):
             torch.nn.init.normal_(self.encoder.cls_token, std=.02)
        torch.nn.init.normal_(self.mask_token, std=.02)

        # Initialize nn.Linear and nn.LayerNorm
        self.apply(self._init_weights)

    def _init_weights(self, m):
        if isinstance(m, nn.Linear):
            torch.nn.init.xavier_uniform_(m.weight)
            if isinstance(m, nn.Linear) and m.bias is not None:
                nn.init.constant_(m.bias, 0)
        elif isinstance(m, nn.LayerNorm):
            nn.init.constant_(m.bias, 0)
            nn.init.constant_(m.weight, 1.0)

    # Keep original random masking for comparison/fallback
    def _random_masking_original(self, x, mask_ratio):
        """ Perform per-sample random masking by shuffling :: x: [N, L, D] """
        N, L, D = x.shape
        len_keep = int(L * (1 - mask_ratio))
        noise = torch.rand(N, L, device=x.device)
        ids_shuffle = torch.argsort(noise, dim=1)
        ids_restore = torch.argsort(ids_shuffle, dim=1)
        ids_keep = ids_shuffle[:, :len_keep]
        x_visible = torch.gather(x, dim=1, index=ids_keep.unsqueeze(-1).repeat(1, 1, D))
        mask = torch.ones([N, L], device=x.device)
        mask[:, :len_keep] = 0
        mask = torch.gather(mask, dim=1, index=ids_restore)
        return x_visible, mask, ids_restore

    # Add block masking method
    def _block_random_masking(self, x, mask_ratio):
        """ Perform per-sample block-wise random masking. """
        return block_random_masking(x, mask_ratio, self.block_size, self.patch_grid_size, x.device)


    def forward_encoder(self, x, mask_ratio):
        # embed patches
        x = self.patch_embed(x) # Uses timm's patch_embed [N, L, D]

        # add pos embed w/o cls token
        x = x + self.pos_embed[:, 1:, :] # Access pos_embed from timm model

        # masking: length -> length * mask_ratio
        if self.masking_strategy == 'random':
            # print("Using random masking strategy.") # Reduce print frequency
            x_visible, mask, ids_restore = self._random_masking_original(x, mask_ratio)
        elif self.masking_strategy == 'block':
            # print(f"Using block masking strategy with block size {self.block_size}.") # Reduce print frequency
            # Note: block_random_masking returns 4 values now
            x_visible, mask, ids_restore = self._block_random_masking(x, mask_ratio)
        else:
            raise ValueError(f"Unknown masking strategy: {self.masking_strategy}")


        # append cls token
        cls_token = self.encoder.cls_token + self.pos_embed[:, :1, :] # Access cls_token from timm model
        cls_tokens = cls_token.expand(x_visible.shape[0], -1, -1)
        x = torch.cat((cls_tokens, x_visible), dim=1) # Pass only visible tokens + cls token

        # apply Transformer blocks (from timm model)
        for blk in self.encoder.blocks:
             x = blk(x)
        # Note: We removed the final norm from the timm encoder instance

        return x, mask, ids_restore

    def forward_decoder(self, x, ids_restore):
        # embed tokens
        x = self.decoder_embed(x)

        # append mask tokens to sequence
        mask_tokens = self.mask_token.repeat(x.shape[0], ids_restore.shape[1] + 1 - x.shape[1], 1)
        x_ = torch.cat([x[:, 1:, :], mask_tokens], dim=1)  # no cls token
        x_ = torch.gather(x_, dim=1, index=ids_restore.unsqueeze(-1).repeat(1, 1, x.shape[2]))  # unshuffle
        x = torch.cat([x[:, :1, :], x_], dim=1)  # append cls token

        # add pos embed
        x = x + self.decoder_pos_embed

        # apply Transformer blocks
        for blk in self.decoder_blocks:
            x = blk(x)
        x = self.decoder_norm(x)

        # predictor projection
        x = self.decoder_pred(x)

        # remove cls token
        x = x[:, 1:, :]

        return x

    def patchify(self, imgs):
        """ imgs: (N, C, H, W) -> (N, L, patch_size**2 * C) """
        p = self.patch_size # Use stored patch size
        # p = self.patch_embed.patch_size[0] # Get patch size from timm's patch_embed
        assert imgs.shape[2] == imgs.shape[3] and imgs.shape[2] % p == 0, f"Image size ({imgs.shape[2]}) must be divisible by patch size ({p})"
        h = w = imgs.shape[2] // p
        c = imgs.shape[1]
        x = imgs.reshape(shape=(imgs.shape[0], c, h, p, w, p))
        x = torch.einsum('nchpwq->nhwpqc', x)
        x = x.reshape(shape=(imgs.shape[0], h * w, p**2 * c))
        return x

    def unpatchify(self, x):
        """ x: (N, L, patch_size**2 * C) -> (N, C, H, W) """
        p = self.patch_size # Use stored patch size
        # p = self.patch_embed.patch_size[0]
        h = w = int(x.shape[1]**0.5)
        assert h * w == x.shape[1], f"Input shape {x.shape} not compatible with grid size {h}x{w}"
        # Calculate C based on the actual channel dimension size
        c = x.shape[2] // (p * p)
        assert c * p * p == x.shape[2], f"Input channels {x.shape[2]} not compatible with patch size {p}"

        x = x.reshape(shape=(x.shape[0], h, w, p, p, c))
        x = torch.einsum('nhwpqc->nchpwq', x)
        imgs = x.reshape(shape=(x.shape[0], c, h * p, w * p))
        return imgs

    def forward_loss(self, imgs, pred_patches, mask):
        """
        Calculate the reconstruction loss (MSE) on masked patches.

        Args:
            imgs (torch.Tensor): Input images [N, C, H, W].
            pred_patches (torch.Tensor): Predicted patches [N, L, p*p*C].
            mask (torch.Tensor): Binary mask [N, L], 0 is keep, 1 is remove.

        Returns:
            torch.Tensor: MSE loss computed only on the masked patches.
        """
        target_patches = self.patchify(imgs)

        if self.norm_pix_loss:
            # Normalize target patches per-patch
            mean = target_patches.mean(dim=-1, keepdim=True)
            var = target_patches.var(dim=-1, keepdim=True)
            target_patches = (target_patches - mean) / (var + 1.e-6)**.5 # Normalize target

        # Calculate MSE loss
        loss = (pred_patches - target_patches) ** 2
        loss = loss.mean(dim=-1)  # [N, L], mean loss per patch

        # Apply mask and calculate mean loss only on removed patches
        mask_sum = mask.sum()
        mse_loss = (loss * mask).sum() / (mask_sum + 1e-8) # Add epsilon for stability

        return mse_loss

    def forward(self, imgs, mask_ratio=0.75):
        """
        Forward pass including encoder, decoder, and loss calculation.

        Args:
            imgs (torch.Tensor): Input images [N, C, H, W].
            mask_ratio (float): Ratio of patches to mask.

        Returns:
            tuple: Contains:
                - torch.Tensor: The primary loss value (MSE on masked patches).
                - torch.Tensor: Predicted patches [N, L, p*p*C].
                - torch.Tensor: Binary mask [N, L].
        """
        latent, mask, ids_restore = self.forward_encoder(imgs, mask_ratio)
        pred = self.forward_decoder(latent, ids_restore)  # [N, L, p*p*C]
        mse_loss = self.forward_loss(imgs, pred, mask)
        return mse_loss, pred, mask # Return MSE loss as the primary loss


# --- Training Function ---
def setup_mae_training(model_params, optimizer_params, device):
    """设置 MAE 训练的优化器和调度器"""
    # MAE often uses AdamW with higher LR and different WD than contrastive
    lr = optimizer_params.get('lr', 1.5e-4)
    weight_decay = optimizer_params.get('weight_decay', 0.05)
    optimizer = optim.AdamW(model_params, lr=lr, betas=(0.9, 0.95), weight_decay=weight_decay)

    # Cosine scheduler is common
    scheduler = CosineAnnealingLR(optimizer, T_max=optimizer_params['epochs'], eta_min=lr/100) # Adjust eta_min if needed

    # Warmup can be added manually or via a wrapper if needed
    # scaler = torch.cuda.amp.GradScaler(enabled=optimizer_params.get('use_amp', True))
    # MAE might be sensitive to AMP, start without it or test carefully
    scaler = torch.cuda.amp.GradScaler(enabled=optimizer_params.get('use_amp', False)) # Allow enabling AMP via params

    return optimizer, scheduler, scaler

# --- Training Function ---
def train_epoch_mae(model, train_loader, optimizer, scaler, device, writer, epoch, total_epochs, base_lr, warmup_epochs,
                    gradient_accumulation_steps=1, log_interval=50, use_amp=False,
                    ssim_criterion=None, perceptual_criterion=None, # Loss criteria
                    mse_weight=1.0, ssim_weight=0.0, perceptual_weight=0.0, gradient_loss_weight=0.0, # Loss weights, added gradient
                    mask_ratio=0.75): # Added mask_ratio
    """MAE 训练循环 with warmup, gradient accumulation, and potentially SSIM/Perceptual/Gradient losses"""
    model.train()
    total_mse_loss = 0
    total_ssim_loss = 0
    total_perceptual_loss = 0
    total_gradient_loss = 0 # Added gradient loss tracking
    total_combined_loss = 0
    num_batches = len(train_loader)
    processed_batches = 0 # Count batches actually processed
    optimizer.zero_grad() # Zero gradients at the beginning of the epoch

    with tqdm(train_loader, unit="batch") as pbar:
        for batch_idx, imgs in enumerate(pbar):
            if imgs is None or imgs.nelement() == 0: # Skip empty batches from filter_none
                print(f"Skipping empty batch at index {batch_idx}")
                continue

            global_step = epoch * num_batches + batch_idx
            # --- Adjust learning rate using linear warmup ---
            # We use a simplified approach here, adjusting LR *before* the step.
            # A more robust way might involve lr_sched.adjust_learning_rate helper if available.
            if epoch < warmup_epochs:
                # Calculate warmup factor based on global step
                num_warmup_steps = warmup_epochs * num_batches
                if num_warmup_steps > 0:
                    lr_scale = min(1.0, float(global_step + 1) / num_warmup_steps)
                    new_lr = base_lr * lr_scale
                    for param_group in optimizer.param_groups:
                        param_group['lr'] = new_lr
            # After warmup, the scheduler (stepped per epoch) handles decay.
            # --- End LR adjustment ---


            imgs = imgs.to(device, non_blocking=True)

            # optimizer.zero_grad() # Moved outside the loop for accumulation

            with torch.cuda.amp.autocast(enabled=use_amp): # Enable AMP context
                # Use the passed mask_ratio
                mse_loss, pred_patches, mask = model(imgs, mask_ratio=mask_ratio)
                mse_loss_value = mse_loss.item() # Store original MSE loss

                # --- Calculate additional losses ---
                ssim_loss_value = 0.0
                perceptual_loss_value = 0.0
                gradient_loss_value = 0.0 # Initialize gradient loss value
                combined_loss = mse_loss * mse_weight # Start with weighted MSE

                # Reconstruct the full image once if needed for multiple losses
                pred_imgs_clamped = None
                imgs_clamped = None
                # Only unpatchify if needed by auxiliary losses
                if ssim_weight > 0 or perceptual_weight > 0 or gradient_loss_weight > 0:
                    pred_imgs = model.unpatchify(pred_patches)
                    # Clamp images to [0, 1] range, assuming input was [-1, 1] and normalized
                    # Adjust this clamping if your input normalization is different
                    pred_imgs_clamped = torch.clamp((pred_imgs + 1.0) / 2.0, 0.0, 1.0)
                    imgs_clamped = torch.clamp((imgs + 1.0) / 2.0, 0.0, 1.0)


                # Calculate SSIM loss if criterion and weight are provided
                if ssim_criterion is not None and ssim_weight > 0:
                    # Using 1.0 as data_range assuming input is normalized to [0, 1]
                    ssim_val = ssim_criterion(pred_imgs_clamped, imgs_clamped) # Use pre-calculated clamped images
                    ssim_loss = 1.0 - ssim_val # SSIM loss is 1 - SSIM index
                    ssim_loss_value = ssim_loss.item()
                    combined_loss += ssim_loss * ssim_weight

                # Calculate Perceptual loss if criterion and weight are provided
                if perceptual_criterion is not None and perceptual_weight > 0:
                    # Perceptual loss expects normalized images in [0, 1] range
                    perceptual_loss = perceptual_criterion(pred_imgs_clamped, imgs_clamped) # Use pre-calculated clamped images
                    perceptual_loss_value = perceptual_loss.item()
                    combined_loss += perceptual_loss * perceptual_weight

                # Calculate Gradient loss if weight is provided
                if gradient_loss_weight > 0 and K is not None:
                    grad_loss = gradient_loss(pred_imgs_clamped, imgs_clamped, device=device) # Use pre-calculated clamped images
                    gradient_loss_value = grad_loss.item()
                    combined_loss += grad_loss * gradient_loss_weight
                # --- End additional losses ---

                combined_loss_value = combined_loss.item() # Store combined loss for logging

                # Scale combined loss for gradient accumulation
                combined_loss = combined_loss / gradient_accumulation_steps

            # Check for NaN loss *before* backward pass
            if math.isnan(combined_loss_value):
                print(f"Warning: NaN combined_loss detected at epoch {epoch+1}, batch {batch_idx}. Skipping batch gradient.")
                continue

            scaler.scale(combined_loss).backward()

            # --- Gradient Accumulation Step ---
            if (batch_idx + 1) % gradient_accumulation_steps == 0 or (batch_idx + 1) == num_batches:
                scaler.step(optimizer)
                scaler.update()
                optimizer.zero_grad() # Zero gradients only after stepping

            # Use individual loss values for logging and tracking
            if math.isnan(combined_loss_value): # Double check after potential scaling issues
                print(f"Warning: NaN combined_loss detected after scaling at epoch {epoch+1}, batch {batch_idx}. Skipping batch.")
                optimizer.zero_grad() # Reset gradients if loss is NaN
                continue

            total_mse_loss += mse_loss_value
            total_ssim_loss += ssim_loss_value
            total_perceptual_loss += perceptual_loss_value
            total_gradient_loss += gradient_loss_value # Accumulate gradient loss
            total_combined_loss += combined_loss_value
            processed_batches += 1

            # Display the combined loss and individual components in the progress bar
            postfix_dict = {
                "CombLoss": f"{combined_loss_value:.4f}",
                "MSE": f"{mse_loss_value:.4f}",
                "LR": f"{optimizer.param_groups[0]['lr']:.1e}"
            }
            if ssim_weight > 0: postfix_dict["SSIM"] = f"{1.0 - ssim_loss_value:.4f}" # Display SSIM index
            if perceptual_weight > 0: postfix_dict["Percpt"] = f"{perceptual_loss_value:.4f}"
            if gradient_loss_weight > 0: postfix_dict["Grad"] = f"{gradient_loss_value:.4f}" # Display gradient loss
            pbar.set_postfix(postfix_dict)


            # Log batch losses based on the original values, potentially less frequently if accumulating
            if (batch_idx + 1) % (log_interval * gradient_accumulation_steps) == 0:
                writer.add_scalar('Loss/Combined_batch', combined_loss_value, global_step)
                writer.add_scalar('Loss/MSE_batch', mse_loss_value, global_step)
                if ssim_weight > 0:
                    writer.add_scalar('Loss/SSIM_batch', ssim_loss_value, global_step)
                    writer.add_scalar('Metric/SSIM_Index_batch', 1.0 - ssim_loss_value, global_step)
                if perceptual_weight > 0:
                    writer.add_scalar('Loss/Perceptual_batch', perceptual_loss_value, global_step)
                if gradient_loss_weight > 0:
                    writer.add_scalar('Loss/Gradient_batch', gradient_loss_value, global_step) # Log gradient loss
                writer.add_scalar('LR', optimizer.param_groups[0]['lr'], global_step)

    avg_combined_loss = total_combined_loss / processed_batches if processed_batches > 0 else 0
    avg_mse_loss = total_mse_loss / processed_batches if processed_batches > 0 else 0
    avg_ssim_loss = total_ssim_loss / processed_batches if processed_batches > 0 else 0
    avg_perceptual_loss = total_perceptual_loss / processed_batches if processed_batches > 0 else 0
    avg_gradient_loss = total_gradient_loss / processed_batches if processed_batches > 0 else 0

    # Return average combined loss and individual components for epoch logging
    return avg_combined_loss, avg_mse_loss, avg_ssim_loss, avg_perceptual_loss, avg_gradient_loss


def pretrain_mae(data_dir='E:/vscode/2号CT数据', batch_size=64, num_workers=4, # Updated path
                 epochs=200, lr=1.5e-4, warmup_epochs=40, weight_decay=0.05, use_amp=True,
                 gradient_accumulation_steps=1, # Add gradient accumulation steps
                 img_size=256, patch_size=16, # Changed patch_size default
                 mask_ratio=0.75, # Added mask_ratio parameter
                 masking_strategy='random', block_size=4, # Added masking strategy args
                 model_embed_dim=768, model_depth=12, model_num_heads=12, # Encoder params
                 decoder_embed_dim=512, decoder_depth=8, decoder_num_heads=16, # Decoder params
                 clip_min=-1000.0, clip_max=1000.0, # Add clip parameters
                 mse_weight=1.0, ssim_weight=0.0, perceptual_weight=0.0, gradient_loss_weight=0.0, # Add loss weights
                 device=None, save_interval=20, resume=None,
                 checkpoint_dir='checkpoints/mae_pretrain_improved', log_dir='logs/mae_pretrain_improved'): # Changed default dirs
    """MAE 自监督预训练函数 (改进版, 支持多损失和不同掩码策略)"""
    # Ensure Kornia is available if gradient loss is used
    if gradient_loss_weight > 0 and K is None:
        raise ImportError("Kornia is required for gradient loss but not installed. Please install with 'pip install kornia'.")

    device = torch.device('cuda' if torch.cuda.is_available() else 'cpu')
    print(f"Using device: {device}")
    # Calculate effective batch size
    effective_batch_size = batch_size * gradient_accumulation_steps
    print(f"Improved MAE Pretraining Parameters:")
    print(f"  - Training: epochs={epochs}, batch_size={batch_size}, grad_accum={gradient_accumulation_steps}, effective_batch_size={effective_batch_size}, use_amp={use_amp}")
    print(f"  - Model: img_size={img_size}, patch_size={patch_size}, mask_ratio={mask_ratio}, masking_strategy={masking_strategy}, block_size={block_size if masking_strategy=='block' else 'N/A'}, norm_pix_loss={True}, decoder_depth={decoder_depth}") # Added masking info
    print(f"  - Losses: MSE={mse_weight}, SSIM={ssim_weight}, Perceptual={perceptual_weight}, Gradient={gradient_loss_weight}") # Added gradient loss weight
    print(f"  - Data: clip_min={clip_min}, clip_max={clip_max}")

    # Create unique subdirectories for this run based on key parameters
    run_name = f"ps{patch_size}_dd{decoder_depth}_mr{mask_ratio:.2f}_mask{masking_strategy}{block_size if masking_strategy=='block' else ''}_mw{mse_weight:.2f}_sw{ssim_weight:.2f}_pw{perceptual_weight:.2f}_gw{gradient_loss_weight:.2f}_{datetime.now().strftime('%Y%m%d_%H%M')}" # Added masking info
    checkpoint_dir = os.path.join(checkpoint_dir, run_name)
    log_dir = os.path.join(log_dir, run_name)

    os.makedirs(checkpoint_dir, exist_ok=True)
    os.makedirs(log_dir, exist_ok=True)
    writer = SummaryWriter(log_dir)

    # Instantiate MAE model
    # Note: norm_pix_loss is now True by default in the class definition
    model = MaskedAutoencoderViT(
        img_size=img_size, patch_size=patch_size, in_chans=1,
        embed_dim=model_embed_dim, depth=model_depth, num_heads=model_num_heads,
        decoder_embed_dim=decoder_embed_dim, decoder_depth=decoder_depth, decoder_num_heads=decoder_num_heads,
        mlp_ratio=4.0, norm_pix_loss=True, # Explicitly set True here for clarity
        masking_strategy=masking_strategy, block_size=block_size # Pass masking args
    ).to(device)

    print(f"MAE Model Parameters: {sum(p.numel() for p in model.parameters() if p.requires_grad):,}")

    # --- Instantiate Loss Criteria ---
    ssim_criterion = None
    if ssim_weight > 0:
        # Assuming input images are normalized to [0, 1] range for SSIM
        ssim_criterion = StructuralSimilarityIndexMeasure(data_range=1.0).to(device)
        print("Using SSIM Loss")

    perceptual_criterion = None
    if perceptual_weight > 0:
        perceptual_criterion = VGGPerceptualLoss().to(device)
        print("Using Perceptual Loss (VGG16)")
    # --- End Loss Criteria ---

    # Get MAE data loader
    # Note: crop_size in loader should match img_size for the model
    train_loader = get_mae_loader(
        data_dir=data_dir,
        batch_size=batch_size,
        num_workers=num_workers,
         crop_size=img_size, # Loader provides crops of the size model expects
         augment=True,
         clip_min=clip_min, # Pass clip parameters
         clip_max=clip_max
     )

    # Setup optimizer and scheduler for MAE
    # Adjust T_max for scheduler to account for warmup
    # Note: The number of epochs passed to the scheduler should be the total epochs *after* warmup
    scheduler_epochs = epochs - warmup_epochs
    optimizer_params = {'lr': lr, 'epochs': scheduler_epochs, 'weight_decay': weight_decay, 'use_amp': use_amp}
    optimizer, scheduler, scaler = setup_mae_training(model.parameters(), optimizer_params, device)

    start_epoch = 0
    best_combined_loss = float('inf') # Track best *combined* loss

    # Resume logic (simplified, adjust as needed)
    if resume and os.path.isfile(resume):
        print(f"Resuming from checkpoint: {resume}")
        checkpoint = torch.load(resume, map_location='cpu')
        # Strict=False allows resuming even if model architecture changed slightly
        # We might need more careful loading if loss weights changed between runs
        try:
            model.load_state_dict(checkpoint['model_state_dict'], strict=False)
            optimizer.load_state_dict(checkpoint['optimizer_state_dict'])
            scheduler.load_state_dict(checkpoint['scheduler_state_dict'])
            start_epoch = checkpoint['epoch']
            # Load the combined loss if available, otherwise default to inf
            best_combined_loss = checkpoint.get('best_combined_loss', checkpoint.get('loss', float('inf')))
            # Load scaler state if resuming and using AMP
            if use_amp and 'scaler_state_dict' in checkpoint and checkpoint['scaler_state_dict'] is not None:
                 scaler.load_state_dict(checkpoint['scaler_state_dict'])
            print(f"Resumed from epoch {start_epoch} with best combined loss {best_combined_loss:.6f}")
        except Exception as e:
            print(f"Error loading checkpoint, starting from scratch: {e}")
            start_epoch = 0
            best_combined_loss = float('inf')

    # Training loop
    for epoch in range(start_epoch, epochs):
        print(f"\nEpoch {epoch+1}/{epochs}:")
        # Pass loss criteria and weights to the training function
        avg_combined_loss, avg_mse_loss, avg_ssim_loss, avg_perceptual_loss, avg_gradient_loss = train_epoch_mae(
            model, train_loader, optimizer, scaler, device, writer, epoch,
            total_epochs=epochs, base_lr=lr, warmup_epochs=warmup_epochs,
            gradient_accumulation_steps=gradient_accumulation_steps,
            use_amp=use_amp,
            ssim_criterion=ssim_criterion, perceptual_criterion=perceptual_criterion,
            mse_weight=mse_weight, ssim_weight=ssim_weight, perceptual_weight=perceptual_weight, gradient_loss_weight=gradient_loss_weight,
            mask_ratio=mask_ratio
        )

        # Step the scheduler only *after* the warmup phase
        if epoch >= warmup_epochs:
             # Check if scheduler has 'step' method (like CosineAnnealingLR)
             if hasattr(scheduler, 'step'):
                 scheduler.step()
             else:
                 print("Warning: Scheduler does not have a 'step' method.")


        # Log epoch losses
        current_lr = optimizer.param_groups[0]['lr'] # Get current LR after potential step
        print(f"Epoch {epoch+1}/{epochs} completed.")
        print(f"  Avg Combined Loss: {avg_combined_loss:.6f}")
        print(f"  Avg MSE Loss: {avg_mse_loss:.6f}")
        if ssim_weight > 0: print(f"  Avg SSIM Loss: {avg_ssim_loss:.6f} (Avg SSIM Index: {1.0 - avg_ssim_loss:.4f})")
        if perceptual_weight > 0: print(f"  Avg Perceptual Loss: {avg_perceptual_loss:.6f}")
        if gradient_loss_weight > 0: print(f"  Avg Gradient Loss: {avg_gradient_loss:.6f}") # Print gradient loss
        print(f"  Current LR: {current_lr:.6f}")

        writer.add_scalar('Loss/Combined_epoch', avg_combined_loss, epoch)
        writer.add_scalar('Loss/MSE_epoch', avg_mse_loss, epoch)
        if ssim_weight > 0:
            writer.add_scalar('Loss/SSIM_epoch', avg_ssim_loss, epoch)
            writer.add_scalar('Metric/SSIM_Index_epoch', 1.0 - avg_ssim_loss, epoch)
        if perceptual_weight > 0:
            writer.add_scalar('Loss/Perceptual_epoch', avg_perceptual_loss, epoch)
        if gradient_loss_weight > 0:
            writer.add_scalar('Loss/Gradient_epoch', avg_gradient_loss, epoch) # Log gradient loss
        writer.add_scalar('LR_epoch', current_lr, epoch)

        # Save checkpoint logic based on combined loss
        is_best = avg_combined_loss < best_combined_loss
        if is_best:
            best_combined_loss = avg_combined_loss

        if (epoch + 1) % save_interval == 0 or is_best or (epoch + 1) == epochs: # Save last epoch too
             checkpoint_data = {
                 'epoch': epoch + 1,
                 'model_state_dict': model.state_dict(),
                 'optimizer_state_dict': optimizer.state_dict(),
                 'scheduler_state_dict': scheduler.state_dict(),
                 'scaler_state_dict': scaler.state_dict() if use_amp else None,
                 'loss': avg_combined_loss, # Save combined loss as primary 'loss'
                 'mse_loss': avg_mse_loss,
                 'ssim_loss': avg_ssim_loss if ssim_weight > 0 else None,
                 'perceptual_loss': avg_perceptual_loss if perceptual_weight > 0 else None,
                 'gradient_loss': avg_gradient_loss if gradient_loss_weight > 0 else None, # Save gradient loss
                 'best_combined_loss': best_combined_loss, # Keep track of the best loss so far
                 'args': { # Save key parameters including loss weights
                     'img_size': img_size, 'patch_size': patch_size,
                     'model_embed_dim': model_embed_dim, 'model_depth': model_depth,
                     'decoder_embed_dim': decoder_embed_dim, 'decoder_depth': decoder_depth, # Save decoder_depth
                     'lr': lr, 'warmup_epochs': warmup_epochs, 'epochs': epochs,
                     'norm_pix_loss': model.norm_pix_loss,
                     'mask_ratio': mask_ratio,
                     'masking_strategy': masking_strategy, 'block_size': block_size, # Save masking strategy
                     'mse_weight': mse_weight, 'ssim_weight': ssim_weight, 'perceptual_weight': perceptual_weight, 'gradient_loss_weight': gradient_loss_weight, # Save gradient weight
                     'gradient_accumulation_steps': gradient_accumulation_steps,
                     'clip_min': clip_min, 'clip_max': clip_max
                 }
             }
             save_path = os.path.join(checkpoint_dir, f"checkpoint_epoch{epoch+1}.pth")
             torch.save(checkpoint_data, save_path)
             print(f"Saved checkpoint: {save_path}")
             if is_best:
                 best_path = os.path.join(checkpoint_dir, "best_model.pth")
                 torch.save(checkpoint_data, best_path)
                 print(f"Saved best model to: {best_path} with combined loss: {best_combined_loss:.6f}")


    writer.close()
    print("MAE Pretraining completed!")


# --- Main execution block (Example) ---
if __name__ == '__main__':
    # Example usage for MAE pretraining
    # Adjust parameters as needed based on GPU memory and desired setup
    # Define arguments using argparse for better control
    import argparse
    parser = argparse.ArgumentParser('Improved MAE pretraining script', add_help=False)

    # Training parameters
    parser.add_argument('--batch_size', default=32, type=int, help='Batch size per GPU (adjusted default)')
    parser.add_argument('--epochs', default=400, type=int, help='Total training epochs (increased default)')
    parser.add_argument('--lr', type=float, default=1.5e-4, help='Base learning rate')
    parser.add_argument('--warmup_epochs', type=int, default=40, help='Epochs to warmup LR')
    parser.add_argument('--weight_decay', type=float, default=0.05)
    parser.add_argument('--use_amp', action='store_true', default=True, help='Use mixed precision')
    parser.add_argument('--gradient_accumulation_steps', type=int, default=2, help='Gradient accumulation steps (adjusted default)')

    # Model parameters
    parser.add_argument('--img_size', default=256, type=int, help='Images input size')
    parser.add_argument('--patch_size', default=16, type=int, help='Patch size (adjusted default)')
    parser.add_argument('--mask_ratio', default=0.75, type=float, help='Masking ratio (percentage of removed patches)') # Added mask_ratio arg
    parser.add_argument('--masking_strategy', type=str, default='random', choices=['random', 'block'], help='Masking strategy: random or block') # Added masking strategy arg
    parser.add_argument('--block_size', type=int, default=4, help='Block size (patches per side) for block masking') # Added block size arg
    parser.add_argument('--model_embed_dim', default=768, type=int)
    parser.add_argument('--model_depth', default=12, type=int)
    parser.add_argument('--model_num_heads', default=12, type=int)
    parser.add_argument('--decoder_embed_dim', default=512, type=int)
    parser.add_argument('--decoder_depth', default=8, type=int, help='Depth of the decoder transformer') # Added decoder_depth arg
    parser.add_argument('--decoder_num_heads', default=16, type=int)
    # norm_pix_loss is now default True in the model

    # Loss parameters
    parser.add_argument('--mse_weight', type=float, default=1.0, help='Weight for MSE reconstruction loss')
    parser.add_argument('--ssim_weight', type=float, default=0.1, help='Weight for SSIM loss (1-SSIM)')
    parser.add_argument('--perceptual_weight', type=float, default=0.1, help='Weight for VGG Perceptual loss')
    parser.add_argument('--gradient_loss_weight', type=float, default=0.0, help='Weight for Gradient (Sobel) loss') # Added gradient loss weight arg

    # Data parameters
    parser.add_argument('--data_dir', default='E:/vscode/2号CT数据', type=str, help='Dataset path')
    parser.add_argument('--num_workers', default=8, type=int)
    parser.add_argument('--clip_min', type=float, default=-1000.0, help='Minimum HU value for clipping')
    parser.add_argument('--clip_max', type=float, default=1000.0, help='Maximum HU value for clipping')

    # IO parameters
    parser.add_argument('--checkpoint_dir', default='checkpoints/mae_pretrain_improved', help='Base path where to save checkpoints')
    parser.add_argument('--log_dir', default='logs/mae_pretrain_improved', help='Base path where to tensorboard log')
    parser.add_argument('--save_interval', default=20, type=int, help='Save checkpoint every x epochs')
    parser.add_argument('--resume', default='', help='Resume from specific checkpoint file')

    args = parser.parse_args()

    pretrain_mae(
        data_dir=args.data_dir,
        batch_size=args.batch_size,
        num_workers=args.num_workers,
        epochs=args.epochs,
        lr=args.lr,
        warmup_epochs=args.warmup_epochs,
        weight_decay=args.weight_decay,
        use_amp=args.use_amp,
        gradient_accumulation_steps=args.gradient_accumulation_steps,
        img_size=args.img_size,
        patch_size=args.patch_size,
        mask_ratio=args.mask_ratio, # Pass mask_ratio
        masking_strategy=args.masking_strategy, # Pass masking strategy
        block_size=args.block_size, # Pass block size
        model_embed_dim=args.model_embed_dim,
        model_depth=args.model_depth,
        model_num_heads=args.model_num_heads,
        decoder_embed_dim=args.decoder_embed_dim,
        decoder_depth=args.decoder_depth, # Pass decoder_depth
        decoder_num_heads=args.decoder_num_heads,
        clip_min=args.clip_min,
        clip_max=args.clip_max,
        mse_weight=args.mse_weight,
        ssim_weight=args.ssim_weight,
        perceptual_weight=args.perceptual_weight,
        gradient_loss_weight=args.gradient_loss_weight, # Pass gradient loss weight
        checkpoint_dir=args.checkpoint_dir,
        log_dir=args.log_dir,
        save_interval=args.save_interval,
        resume=args.resume
    )

    # --- Old code (Contrastive/Supervised) is removed below this line ---
    # (Keep the old functions if needed for reference, but the main block now calls MAE)
