import os
import numpy as np
from PIL import Image
import glob

def check_dataset(data_dir, expected_resolution=(2700, 2700)):
    """检查数据集格式和分辨率"""
    print(f"检查数据集: {data_dir}")
    
    # 检查目录是否存在
    if not os.path.exists(data_dir):
        print(f"错误: 目录 {data_dir} 不存在!")
        return False
    
    # 获取所有tif文件
    files = glob.glob(os.path.join(data_dir, "*.tif"))
    if not files:
        print(f"错误: 在 {data_dir} 中未找到.tif文件!")
        return False
    
    print(f"找到 {len(files)} 个.tif文件")
    
    # 检查第一个文件的分辨率
    try:
        img = Image.open(files[0])
        resolution = img.size
        print(f"图像分辨率: {resolution}")
        
        if resolution != expected_resolution:
            print(f"警告: 分辨率 {resolution} 与预期 {expected_resolution} 不符!")
        else:
            print("分辨率检查通过!")
            
        # 检查位深度
        bit_depth = "未知"
        if img.mode == "I;16":
            bit_depth = "16位"
        elif img.mode == "L":
            bit_depth = "8位"
        print(f"图像位深度: {bit_depth}")
        
        return True
    except Exception as e:
        print(f"错误: 无法读取图像: {e}")
        return False

# 检查两个数据集
check_dataset("E:/vscode/2号CT数据") # Updated path
print("\n" + "-"*50 + "\n")
check_dataset("E:/vscode/3号CT数据") # Updated path
