# Enhanced Configuration for Conditional Diffusion Super-Resolution Training
# With Pretrained Swin-MAE Encoder Integration

# --- Data Configuration ---
data:
  # Using relative paths from the project root (e:/vscode/非配位超分辨)
  lr_dir: 'data/2号CT数据' # Relative path to Low-Resolution CT images (Dataset #2)
  hr_dir: 'data/3号CT数据' # Relative path to High-Resolution CT images (Dataset #3)
  patch_size_hr: 128          # Patch size for High-Resolution images during training
  scale_factor: 4             # Upscaling factor (HR_size / LR_size)
  # patch_size_lr will be calculated as patch_size_hr // scale_factor

  # --- Parameters for BSRGAN-style Degradation ---
  # Probabilities
  blur_prob: 1.0              # Probability of applying blur
  downsample_prob: 1.0        # Probability of applying downsampling (should usually be 1.0 for SR)
  noise_prob: 1.0             # Probability of adding noise
  shuffle_prob: 0.5           # Probability of shuffling degradation order (blur, downsample, noise)

  # Blur Settings
  blur_kernel_size: 21        # Kernel size for Gaussian blur
  blur_sigma_range: [0.2, 3.0] # Range for isotropic/anisotropic sigma
  aniso_prob: 0.5             # Probability of using anisotropic vs isotropic Gaussian blur

  # Downsampling Settings
  downsample_methods: ['bicubic', 'bilinear', 'nearest'] # Methods to randomly choose from

  # Noise Settings
  noise_gaussian_sigma_range: [1, 25] # Range for Gaussian noise std dev (applied to [0, 255] range conceptually)

# --- Model Configuration ---
model:
  in_channels: 1              # Number of input channels (1 for grayscale CT)
  out_channels: 1             # Number of output channels (1 for grayscale CT)
  base_channels: 128          # Base number of channels in the U-Net
  channel_mults: [1, 2, 2, 4] # Channel multipliers for each U-Net level (e.g., 128, 256, 256, 512)
  attention_resolutions: [16, 8] # Resolutions at which to use self-attention blocks
  num_res_blocks: 2           # Number of residual blocks per U-Net level
  dropout: 0.1                # Dropout rate

  # --- Pretrained Encoder Settings ---
  use_pretrained_encoder: True # Enable pretrained encoder for enhanced training
  encoder_type: "Swin-MAE"     # Type of pretrained encoder
  # Using the best performing hierarchical model with PSNR=21.51, SSIM=0.626
  encoder_checkpoint: "checkpoints/swin_mae_hierarchical_random_ssim_nce_w002.pth"
  freeze_encoder: True         # Start with frozen encoder, can be unfrozen later for fine-tuning
  condition_method: 'CrossAttention' # How to inject condition ('CrossAttention', 'Concat', None)
  encoder_injection_levels: [0, 1, 2, 3] # Inject features from all 4 stages of Swin-T
  use_adaptive_gate: True     # Enable adaptive gating for better feature fusion
  num_heads: 8                # Number of heads for attention blocks
  # head_dim: 64              # Optional: Dimension per head

# --- Discriminator Configuration (for GAN training) ---
discriminator:
  # Settings for the NLayerDiscriminator (PatchGAN)
  ndf: 64                     # Number of filters in the first conv layer of D
  n_layers: 3                 # Number of conv layers in the D
  lr: 1.0e-4                  # Learning rate for the discriminator optimizer

# --- Diffusion Process Configuration ---
diffusion:
  schedule_name: 'cosine'     # Noise schedule type ('linear', 'cosine')
  timesteps: 1000             # Total number of diffusion timesteps

# --- Training Configuration ---
training:
  # Enhanced training settings
  log_root: './logs/sr_diffusion'          # Root directory for TensorBoard logs
  checkpoint_root: './checkpoints/sr_diffusion' # Root directory for saving model checkpoints
  learning_rate: 5.0e-5       # Lower initial learning rate for stability with pretrained encoder
  weight_decay: 1.0e-4        # Small weight decay for regularization
  batch_size: 6               # Slightly reduced batch size due to encoder overhead
  epochs: 150                 # Reduced epochs since we start with pretrained features

  # Loss function weights (optimized for enhanced training)
  diffusion_loss_type: 'l1'   # Loss for comparing predicted noise and actual noise
  perceptual_loss_weight: 0.05 # Reduced perceptual loss weight
  perceptual_loss_type: 'l1'  # Type of loss for comparing VGG features
  ssim_loss_weight: 0.1       # SSIM loss weight
  gradient_loss_weight: 0.05  # Gradient loss weight
  use_gan: True               # Enable GAN loss
  gan_loss_weight: 0.02       # Reduced GAN loss weight for stability

  # Encoder-specific settings
  encoder_lr: 1.0e-5          # Much lower LR for the pretrained encoder (if unfrozen)

  # Training optimization
  use_amp: True               # Use Automatic Mixed Precision
  seed: 42                    # Random seed for reproducibility
  num_workers: 4              # Number of workers for DataLoader
  log_interval: 1             # Log batch loss every N steps
  save_interval: 10           # Save checkpoint every N epochs

  # Learning rate scheduling
  use_scheduler: True         # Enable learning rate scheduling
  scheduler_type: 'cosine'    # Type of scheduler ('cosine', 'step', 'plateau')
  warmup_epochs: 10           # Number of warmup epochs

  # Gradient optimization
  grad_clip_norm: 1.0         # Gradient clipping norm value
  gradient_accumulation_steps: 2 # Accumulate gradients over multiple steps

# --- Inference Configuration ---
inference:
  sampling_steps: 50          # Reduced sampling steps for faster inference
  eta: 0.0                    # DDIM eta parameter (0.0 for deterministic)

# --- Experiment Tracking ---
experiment:
  name: "SwinMAE_Enhanced_v1"
  description: "Enhanced diffusion SR with pretrained Swin-MAE encoder (PSNR=21.51, SSIM=0.626)"
  tags: ["diffusion", "super-resolution", "swin-mae", "ct-images", "enhanced"]
