import numpy as np
import torch
import matplotlib.pyplot as plt
from PIL import Image
import os
import argparse
import random
import cv2
import math
import time
from skimage.metrics import peak_signal_noise_ratio, structural_similarity
import matplotlib.gridspec as gridspec
from matplotlib.colors import LinearSegmentedColormap
from scipy import ndimage

# Import the model definition from the script used for training this checkpoint
from train_swin_mae_resnet_random_mask_hierarchical import MaskedAutoencoderSwin, random_masking, HierarchicalDecoder
from torchvision import transforms
import torch.nn.functional as F
import torch.nn as nn
import logging

# Configure logging
logging.basicConfig(level=logging.INFO, format='%(asctime)s - %(levelname)s - %(message)s')

# Create a custom colormap for feature visualization
def create_feature_colormap():
    # Create a custom colormap that goes from dark blue to white to dark red
    colors = [(0, 0, 0.5), (1, 1, 1), (0.5, 0, 0)]
    return LinearSegmentedColormap.from_list('feature_map', colors, N=256)

# Helper function to display images
def show_image(image, title='', ax=None):
    if ax is None:
        plt.figure(figsize=(6, 6))
        ax = plt.gca()

    # Determine vmin and vmax based on dtype or content if needed, but default to [0, 1] for normalized float data
    vmin, vmax = 0, 1

    if image.ndim == 2:
        # Grayscale image (H, W)
        ax.imshow(image, cmap='gray', vmin=vmin, vmax=vmax)
    elif image.ndim == 3:
        # Assumes (H, W, C)
        assert image.shape[2] == 1 or image.shape[2] == 3, f"Expected 1 or 3 channels, got shape {image.shape}"
        # Squeeze potential single channel dimension for imshow
        img_to_show = image.squeeze()
        # If still 3D after squeeze (e.g., RGB), imshow handles it. If 2D, cmap applies.
        ax.imshow(img_to_show, cmap='gray', vmin=vmin, vmax=vmax)
    else:
        raise ValueError(f"Unsupported image shape: {image.shape}")

    ax.set_title(title, fontsize=10)
    ax.axis('off')
    return ax

# Function to visualize feature maps
def visualize_feature_maps(feature_tensor, title='Feature Maps', max_features=16):
    """
    Visualizes feature maps from a tensor.
    Args:
        feature_tensor: Tensor of shape [N, C, H, W]
        title: Title for the plot
        max_features: Maximum number of feature maps to display
    """
    # Take the first batch item
    features = feature_tensor[0].detach().cpu().numpy()

    # Determine number of features to show (up to max_features)
    n_features = min(features.shape[0], max_features)

    # Calculate grid dimensions
    grid_size = int(np.ceil(np.sqrt(n_features)))

    # Create figure
    fig, axes = plt.subplots(grid_size, grid_size, figsize=(12, 12))
    fig.suptitle(title, fontsize=16)

    # Custom colormap for feature visualization
    cmap = create_feature_colormap()

    # Plot each feature map
    for i in range(grid_size * grid_size):
        ax = axes[i // grid_size, i % grid_size]
        if i < n_features:
            # Normalize the feature map for better visualization
            feature = features[i]
            if feature.max() > feature.min():
                feature = (feature - feature.min()) / (feature.max() - feature.min())

            ax.imshow(feature, cmap=cmap)
            ax.set_title(f"Channel {i}", fontsize=8)
        ax.axis('off')

    plt.tight_layout()
    plt.subplots_adjust(top=0.9)
    return fig

# Function to compute frequency domain representation
def compute_frequency_domain(image):
    """
    Compute the frequency domain representation of an image.
    Args:
        image: 2D numpy array
    Returns:
        magnitude_spectrum: Log-scaled magnitude spectrum
    """
    # Apply FFT
    f_transform = np.fft.fft2(image)
    f_shift = np.fft.fftshift(f_transform)

    # Compute magnitude spectrum (log scale for better visualization)
    magnitude_spectrum = np.log1p(np.abs(f_shift))

    # Normalize for visualization
    if magnitude_spectrum.max() > magnitude_spectrum.min():
        magnitude_spectrum = (magnitude_spectrum - magnitude_spectrum.min()) / (magnitude_spectrum.max() - magnitude_spectrum.min())

    return magnitude_spectrum

# Function to compute edge map
def compute_edge_map(image, sigma=1.0):
    """
    Compute edge map using Sobel filters.
    Args:
        image: 2D numpy array
        sigma: Gaussian blur sigma for pre-processing
    Returns:
        edge_magnitude: Edge magnitude map
    """
    # Apply Gaussian blur to reduce noise
    blurred = ndimage.gaussian_filter(image, sigma=sigma)

    # Apply Sobel filters
    sobel_h = ndimage.sobel(blurred, axis=0)
    sobel_v = ndimage.sobel(blurred, axis=1)

    # Compute edge magnitude
    edge_magnitude = np.sqrt(sobel_h**2 + sobel_v**2)

    # Normalize for visualization
    if edge_magnitude.max() > edge_magnitude.min():
        edge_magnitude = (edge_magnitude - edge_magnitude.min()) / (edge_magnitude.max() - edge_magnitude.min())

    return edge_magnitude

# Modified function to run inference and return all necessary data
def run_one_image(img_tensor_normalized_minus1_1, model, mask_ratio, device, patch_hu_min, patch_hu_max):
    """
    Run Swin-MAE model on a single image tensor normalized to [-1, 1] based on ITS OWN patch range.
    Returns the reconstruction denormalized back to the PATCH HU range [patch_hu_min, patch_hu_max],
    the binary mask (1 = masked/removed, 0 = visible), and intermediate features.
    """
    x = img_tensor_normalized_minus1_1.unsqueeze(0)  # Add batch dimension [1, C, H, W]
    x = x.to(device, non_blocking=True)
    N, C_in, H_initial, W_initial = x.shape
    logging.info(f"run_one_image - Input tensor (x) shape: {x.shape}")

    # Run Swin-MAE forward pass to get prediction and mask
    with torch.no_grad():
        # 1. Encoder - Call forward_encoder (returns latent_full and intermediate_features)
        latent_full, intermediate_features = model.forward_encoder(x)

        # 2. Generate mask for visualization (using random_masking)
        mask_binary = random_masking(N, model.num_patches, mask_ratio, x.device)

        # 3. Decoder - Project latent features and decode using hierarchical decoder
        x_proj = model.decoder_embed(latent_full)  # Shape: [N, L_final, D_decoder]
        pred = model.forward_decoder(latent_full, intermediate_features)

        # For hierarchical decoder, pred is already at the initial patch resolution [N, L_initial, p*p*C]
        N, L_initial, C_pred = pred.shape
        p = model.patch_size
        C_pix = model.in_chans

        # Verify dimensions
        assert L_initial == model.num_patches, f"Prediction sequence length mismatch: {L_initial} vs {model.num_patches}"
        assert C_pred == p**2 * C_pix, f"Prediction channel mismatch: {C_pred} vs {p**2 * C_pix}"

        # Convert sequence back to image format
        pred_img = model.unpatchify(pred)  # Shape: [N, C, H, W]

        # --- Post-processing for visualization ---
        # y_pred_norm is the predicted image [N, C, H, W] in the normalized space [-1, 1]
        y_pred_norm = pred_img.detach().cpu()

    # Denormalize reconstruction back to PATCH HU scale using provided patch_hu_min, patch_hu_max
    patch_hu_mean = (patch_hu_max + patch_hu_min) / 2.0
    patch_hu_half_range = (patch_hu_max - patch_hu_min) / 2.0
    if patch_hu_half_range == 0:  # Avoid division by zero if patch_hu_min == patch_hu_max
        logging.warning("Patch HU min and max are equal, denormalizing to patch_hu_min.")
        recon_hu = torch.full_like(y_pred_norm, patch_hu_min)
    else:
        recon_hu = y_pred_norm * patch_hu_half_range + patch_hu_mean

    logging.info(f"run_one_image - Denormalized Reconstruction (recon_hu) range (Patch HU scale [{patch_hu_min:.2f}, {patch_hu_max:.2f}]): min={recon_hu.min():.2f}, max={recon_hu.max():.2f}")

    # Prepare mask for return (binary: 1 = masked, 0 = visible)
    mask_binary = mask_binary.detach().cpu()
    # Unpatchify the mask to image shape [N, 1, H, W]
    mask_img = mask_binary.unsqueeze(-1).repeat(1, 1, model.patch_size**2 * C_in)  # (N, L, p*p*C)
    mask_img = model.unpatchify(mask_img)  # Shape: [N, C, H, W], 1 is removing, 0 is keeping
    # Keep only one channel for the mask image
    mask_img = mask_img[:, 0:1, :, :]  # Shape: [N, 1, H, W]

    # Return reconstruction in HU scale, the binary mask image, and intermediate features
    return recon_hu, mask_img, intermediate_features


# Enhanced visualization function
def visualize_swin_mae_hierarchical_reconstruction_enhanced(model_path, image_path, output_dir, hu_min, hu_max, mask_ratios, args):
    """
    Enhanced visualization of Swin-MAE Hierarchical model with random masking.
    Includes feature map visualization, frequency domain analysis, and edge detection.
    """
    device = torch.device('cuda' if torch.cuda.is_available() else 'cpu')
    logging.info(f"Using device: {device}")

    try:
        # --- Load Model ---
        logging.info(f"Loading model from {model_path}")
        checkpoint = torch.load(model_path, map_location=device)

        # Extract model arguments from checkpoint
        model_args = checkpoint.get('args', None)

        # Default configuration
        patch_size = 4
        swin_embed_dim = 96
        swin_depths = [2, 2, 6, 2]
        swin_num_heads = [3, 6, 12, 24]
        swin_window_size = 7
        swin_ape = False
        decoder_embed_dim = 128
        decoder_depths = [1, 1, 1, 1]
        decoder_num_heads = [8, 8, 4, 2]
        norm_pix_loss = False
        perceptual_loss_weight = 0.005
        img_size = 256

        if model_args is None:
            logging.warning("No args found in checkpoint, using default model configuration")
        else:
            # Check if model_args is a Namespace or dict
            if hasattr(model_args, '__dict__'):  # It's a Namespace
                logging.info("Loading args from Namespace")
                if hasattr(model_args, 'patch_size'):
                    patch_size = model_args.patch_size
                if hasattr(model_args, 'swin_embed_dim'):
                    swin_embed_dim = model_args.swin_embed_dim
                if hasattr(model_args, 'swin_depths'):
                    swin_depths = model_args.swin_depths
                if hasattr(model_args, 'swin_num_heads'):
                    swin_num_heads = model_args.swin_num_heads
                if hasattr(model_args, 'swin_window_size'):
                    swin_window_size = model_args.swin_window_size
                if hasattr(model_args, 'swin_ape'):
                    swin_ape = model_args.swin_ape
                if hasattr(model_args, 'decoder_embed_dim'):
                    decoder_embed_dim = model_args.decoder_embed_dim
                if hasattr(model_args, 'decoder_depths'):
                    decoder_depths = model_args.decoder_depths
                if hasattr(model_args, 'decoder_num_heads'):
                    decoder_num_heads = model_args.decoder_num_heads
                if hasattr(model_args, 'norm_pix_loss'):
                    norm_pix_loss = model_args.norm_pix_loss
                if hasattr(model_args, 'perceptual_loss_weight'):
                    perceptual_loss_weight = model_args.perceptual_loss_weight
                if hasattr(model_args, 'img_size'):
                    img_size = model_args.img_size
            elif isinstance(model_args, dict):  # It's a dict
                logging.info("Loading args from dictionary")
                patch_size = model_args.get('patch_size', patch_size)
                swin_embed_dim = model_args.get('swin_embed_dim', swin_embed_dim)
                swin_depths = model_args.get('swin_depths', swin_depths)
                swin_num_heads = model_args.get('swin_num_heads', swin_num_heads)
                swin_window_size = model_args.get('swin_window_size', swin_window_size)
                swin_ape = model_args.get('swin_ape', swin_ape)
                decoder_embed_dim = model_args.get('decoder_embed_dim', decoder_embed_dim)
                decoder_depths = model_args.get('decoder_depths', decoder_depths)
                decoder_num_heads = model_args.get('decoder_num_heads', decoder_num_heads)
                norm_pix_loss = model_args.get('norm_pix_loss', norm_pix_loss)
                perceptual_loss_weight = model_args.get('perceptual_loss_weight', perceptual_loss_weight)
                img_size = model_args.get('img_size', img_size)
            else:
                logging.warning(f"model_args is of unexpected type: {type(model_args)}")

        logging.info(f"Using model configuration: patch_size={patch_size}, decoder_embed_dim={decoder_embed_dim}, img_size={img_size}")

        # --- Instantiate Swin-MAE model ---
        model = MaskedAutoencoderSwin(
            img_size=img_size,
            patch_size=patch_size,
            in_chans=1,  # Assuming grayscale input
            embed_dim=swin_embed_dim,
            depths=swin_depths,
            num_heads=swin_num_heads,
            window_size=swin_window_size,
            mlp_ratio=4.0,  # Standard MLP ratio
            norm_layer=nn.LayerNorm,
            ape=swin_ape,
            patch_norm=True,  # Usually True for Swin
            decoder_embed_dim=decoder_embed_dim,
            decoder_depths=decoder_depths,
            decoder_num_heads=decoder_num_heads,
            norm_pix_loss=norm_pix_loss,
            perceptual_loss_weight=perceptual_loss_weight
        ).to(device)

        # Load model weights - handle different checkpoint formats
        if 'model' in checkpoint:
            model.load_state_dict(checkpoint['model'], strict=False)
            logging.info("Loaded weights from checkpoint['model']")
        elif 'state_dict' in checkpoint:
            model.load_state_dict(checkpoint['state_dict'], strict=False)
            logging.info("Loaded weights from checkpoint['state_dict']")
        elif isinstance(checkpoint, dict) and all(k.startswith('module.') or '.' in k for k in checkpoint.keys()):
            # It's a raw state dict
            model.load_state_dict(checkpoint, strict=False)
            logging.info("Loaded weights directly from checkpoint dict")
        else:
            # Try to find any key that might contain the model weights
            model_keys = [k for k in checkpoint.keys() if isinstance(checkpoint[k], dict) or isinstance(checkpoint[k], torch.Tensor)]
            if model_keys:
                logging.info(f"Attempting to load from key: {model_keys[0]}")
                model.load_state_dict(checkpoint[model_keys[0]], strict=False)
            else:
                logging.error(f"Could not find model weights in checkpoint. Keys: {list(checkpoint.keys())}")
                raise ValueError("No model weights found in checkpoint")

        model.eval()  # Set model to evaluation mode
        logging.info("Model loaded successfully")

        # --- Load and Process Image ---
        logging.info(f"Loading image from {image_path}")
        # Load image using PIL
        img = Image.open(image_path)
        img_np = np.array(img)

        # Check if image is grayscale or RGB
        if len(img_np.shape) == 3 and img_np.shape[2] == 3:
            # Convert RGB to grayscale
            img_np = cv2.cvtColor(img_np, cv2.COLOR_RGB2GRAY)
            logging.info("Converted RGB image to grayscale")

        # Extract center crop of size img_size x img_size
        h, w = img_np.shape
        crop_size = min(h, w, img_size)
        start_h = (h - crop_size) // 2
        start_w = (w - crop_size) // 2
        img_cropped_np = img_np[start_h:start_h+crop_size, start_w:start_w+crop_size]

        # Determine HU range of the patch
        patch_min_hu = float(img_cropped_np.min())
        patch_max_hu = float(img_cropped_np.max())
        logging.info(f"Patch HU range: [{patch_min_hu:.2f}, {patch_max_hu:.2f}]")

        # --- Determine Visualization Range based on args.vis_hu_mode ---
        if args.vis_hu_mode == 'patch':
            display_vmin = patch_min_hu
            display_vmax = patch_max_hu
            vis_range_str = f"Patch HU: [{display_vmin:.0f}, {display_vmax:.0f}]"
            logging.info(f"Using PATCH HU range for visualization: [{display_vmin:.2f}, {display_vmax:.2f}]")
        elif args.vis_hu_mode == 'global':
            display_vmin = hu_min  # Use global range passed as argument
            display_vmax = hu_max
            vis_range_str = f"Global HU: [{display_vmin:.0f}, {display_vmax:.0f}]"
            logging.info(f"Using GLOBAL HU range for visualization: [{display_vmin:.2f}, {display_vmax:.2f}]")
        else:  # Should not happen with choices in argparse
            logging.error(f"Invalid vis_hu_mode: {args.vis_hu_mode}. Defaulting to global.")
            display_vmin = hu_min
            display_vmax = hu_max
            vis_range_str = f"Global HU: [{display_vmin:.0f}, {display_vmax:.0f}]"

        # --- Normalize patch to [-1, 1] FOR MODEL INPUT using PATCH HU range ---
        patch_hu_mean = (patch_max_hu + patch_min_hu) / 2.0
        patch_hu_half_range = (patch_max_hu - patch_min_hu) / 2.0

        if patch_hu_half_range == 0:  # Avoid division by zero
            img_tensor_minus1_1 = torch.zeros((1, crop_size, crop_size), dtype=torch.float32)
            logging.warning("Patch has uniform value, using zero tensor for model input")
        else:
            # Normalize to [-1, 1] using patch range
            img_normalized = (img_cropped_np - patch_hu_mean) / patch_hu_half_range
            img_tensor_minus1_1 = torch.from_numpy(img_normalized).float().unsqueeze(0)  # Add channel dim [C, H, W]

    except Exception as e:
        logging.error(f"Exception during image loading or processing: {e}", exc_info=True)
        return

    # --- Run Inference and Visualization for each mask ratio ---
    num_ratios = len(mask_ratios)

    # Use a compatible matplotlib style
    try:
        plt.style.use('seaborn-v0_8-whitegrid')
    except:
        try:
            plt.style.use('seaborn-whitegrid')  # For newer matplotlib versions
        except:
            logging.warning("Could not set seaborn style, using default style")

    plt.rcParams.update({
        'font.family': 'serif',
        'figure.dpi': 150,
        'font.size': 8,
        'axes.titlesize': 10,
    })

    logging.info(f"Running MAE inference for mask ratios: {mask_ratios}")

    # Create output directory
    os.makedirs(output_dir, exist_ok=True)

    # Process each mask ratio
    for mask_ratio in mask_ratios:
        logging.info(f"Processing mask ratio: {mask_ratio}")
        try:
            # Get reconstruction, mask, and intermediate features
            recon_hu_tensor, mask_binary_tensor, intermediate_features = run_one_image(
                img_tensor_minus1_1, model, mask_ratio, device, patch_min_hu, patch_max_hu
            )

            # Convert tensors to numpy arrays
            recon_hu_np = recon_hu_tensor.squeeze(0).permute(1, 2, 0).numpy()  # Shape [H, W, C] in HU scale
            mask_binary_np = mask_binary_tensor.squeeze(0).squeeze(0).numpy()  # Shape [H, W], 1=masked

            # Calculate metrics (PSNR and SSIM)
            # Ensure data range matches the visualization range
            data_range = display_vmax - display_vmin
            psnr_val = peak_signal_noise_ratio(
                img_cropped_np, recon_hu_np.squeeze(), data_range=data_range
            )
            ssim_val = structural_similarity(
                img_cropped_np, recon_hu_np.squeeze(), data_range=data_range
            )
            logging.info(f"Mask ratio {mask_ratio}: PSNR = {psnr_val:.2f}, SSIM = {ssim_val:.3f}")

            # Create derived images for visualization
            # Masked image: Show original where visible, use display_vmin for masked areas
            masked_display = img_cropped_np * (1 - mask_binary_np) + display_vmin * mask_binary_np

            # Paste image (Reconstruction + Visible) in HU scale
            paste_hu = img_cropped_np * (1 - mask_binary_np) + recon_hu_np.squeeze() * mask_binary_np

            # Difference map (Absolute difference in HU units in masked areas)
            diff_map_hu = np.abs(img_cropped_np - recon_hu_np.squeeze()) * mask_binary_np
            diff_max_val = np.max(diff_map_hu) if np.any(mask_binary_np) else 0

            # --- Create Enhanced Visualizations ---

            # 1. Basic Reconstruction Visualization
            fig_basic = plt.figure(figsize=(15, 10))
            gs = gridspec.GridSpec(2, 5, height_ratios=[1, 1])

            # Row 1: Original visualizations
            ax1 = plt.subplot(gs[0, 0])
            ax1.imshow(img_cropped_np, cmap='gray', vmin=display_vmin, vmax=display_vmax)
            ax1.set_title(f"Original Patch\n(Vis Range: {vis_range_str})")
            ax1.axis('off')

            ax2 = plt.subplot(gs[0, 1])
            ax2.imshow(masked_display, cmap='gray', vmin=display_vmin, vmax=display_vmax)
            ax2.set_title(f"Masked ({int(mask_ratio*100)}%)\n(Masked = {display_vmin:.0f} HU)")
            ax2.axis('off')

            ax3 = plt.subplot(gs[0, 2])
            ax3.imshow(recon_hu_np.squeeze(), cmap='gray', vmin=display_vmin, vmax=display_vmax)
            ax3.set_title(f"Reconstruction\nPSNR: {psnr_val:.2f}, SSIM: {ssim_val:.3f}")
            ax3.axis('off')

            ax4 = plt.subplot(gs[0, 3])
            ax4.imshow(paste_hu, cmap='gray', vmin=display_vmin, vmax=display_vmax)
            ax4.set_title(f"Recon + Visible\n(Vis Range: {vis_range_str})")
            ax4.axis('off')

            ax5 = plt.subplot(gs[0, 4])
            im_diff = ax5.imshow(diff_map_hu, cmap='viridis', vmin=0, vmax=max(1, diff_max_val))
            ax5.set_title(f"Abs Difference (HU)\n(Max: {diff_max_val:.0f})")
            ax5.axis('off')
            plt.colorbar(im_diff, ax=ax5, fraction=0.046, pad=0.04)

            # Row 2: Advanced visualizations

            # Compute edge maps
            orig_edge = compute_edge_map(img_cropped_np)
            recon_edge = compute_edge_map(recon_hu_np.squeeze())
            edge_diff = np.abs(orig_edge - recon_edge) * mask_binary_np

            # Compute frequency domain representations
            orig_freq = compute_frequency_domain(img_cropped_np)
            recon_freq = compute_frequency_domain(recon_hu_np.squeeze())
            freq_diff = np.abs(orig_freq - recon_freq)

            ax6 = plt.subplot(gs[1, 0])
            ax6.imshow(orig_edge, cmap='gray')
            ax6.set_title("Original Edge Map")
            ax6.axis('off')

            ax7 = plt.subplot(gs[1, 1])
            ax7.imshow(recon_edge, cmap='gray')
            ax7.set_title("Reconstruction Edge Map")
            ax7.axis('off')

            ax8 = plt.subplot(gs[1, 2])
            im_edge_diff = ax8.imshow(edge_diff, cmap='viridis')
            ax8.set_title("Edge Difference (Masked)")
            ax8.axis('off')
            plt.colorbar(im_edge_diff, ax=ax8, fraction=0.046, pad=0.04)

            ax9 = plt.subplot(gs[1, 3])
            ax9.imshow(orig_freq, cmap='viridis')
            ax9.set_title("Original Frequency Domain")
            ax9.axis('off')

            ax10 = plt.subplot(gs[1, 4])
            ax10.imshow(recon_freq, cmap='viridis')
            ax10.set_title("Recon Frequency Domain")
            ax10.axis('off')

            plt.tight_layout()

            # Save basic visualization
            basic_vis_filename = f"swin_mae_hierarchical_enhanced_basic_ratio_{mask_ratio:.2f}.png"
            basic_vis_path = os.path.join(output_dir, basic_vis_filename)
            plt.savefig(basic_vis_path, bbox_inches='tight')
            plt.close(fig_basic)
            logging.info(f"Basic visualization saved to: {basic_vis_path}")

            # 2. Feature Map Visualization (for selected intermediate features)
            if args.visualize_features:
                # Visualize a subset of intermediate features
                for i, feature in enumerate(intermediate_features):
                    if i >= len(model.encoder_dims):  # Safety check
                        continue

                    # Create feature visualization
                    fig_feature = visualize_feature_maps(
                        feature,
                        title=f"Encoder Stage {i+1} Features (Mask Ratio: {mask_ratio:.2f})",
                        max_features=min(16, feature.shape[1])  # Show at most 16 features
                    )

                    # Save feature visualization
                    feature_vis_filename = f"swin_mae_hierarchical_features_stage{i+1}_ratio_{mask_ratio:.2f}.png"
                    feature_vis_path = os.path.join(output_dir, feature_vis_filename)
                    fig_feature.savefig(feature_vis_path, bbox_inches='tight')
                    plt.close(fig_feature)
                    logging.info(f"Feature visualization for stage {i+1} saved to: {feature_vis_path}")

        except Exception as e:
            logging.error(f"Error processing mask ratio {mask_ratio}: {e}", exc_info=True)
            continue

    logging.info("Enhanced visualization completed successfully")


if __name__ == '__main__':
    parser = argparse.ArgumentParser('Swin-MAE Hierarchical Random Masking Enhanced Visualization')

    # Model and data paths
    parser.add_argument('--model_path', default='checkpoints/swin_mae_hierarchical_random_ssim_w005/best_model.pth',
                        type=str, help='Path to Swin-MAE Hierarchical pretrained model checkpoint')
    parser.add_argument('--image_path', default='E:/vscode/2号CT数据/19p44um-2700x2700x2000-0750.tif',
                        type=str, help='Path to the input image file')
    parser.add_argument('--output_dir', default='plots/swin_mae_hierarchical_enhanced_visualization',
                        type=str, help='Directory to save visualization plots')

    # Visualization parameters
    parser.add_argument('--hu_min', type=float, default=-1000.0,
                        help='Minimum GLOBAL HU value used for global visualization range')
    parser.add_argument('--hu_max', type=float, default=1000.0,
                        help='Maximum GLOBAL HU value used for global visualization range')
    parser.add_argument('--vis_hu_mode', type=str, default='patch', choices=['global', 'patch'],
                        help="Which HU range to use for visualization: 'global' or 'patch'")
    parser.add_argument('--mask_ratios', default=[0.25, 0.5, 0.75, 0.9], type=float, nargs='+',
                        help='List of masking ratios for visualization (e.g., 0.5 0.75 0.9)')

    # Enhanced visualization options
    parser.add_argument('--visualize_features', action='store_true',
                        help='Enable visualization of intermediate feature maps')
    parser.add_argument('--max_features', type=int, default=16,
                        help='Maximum number of feature maps to visualize per layer')

    args = parser.parse_args()

    # Create output directory with timestamp
    timestamp = time.strftime("%Y%m%d_%H%M%S")
    output_dir_final = os.path.join(args.output_dir, f"swin_mae_hierarchical_enhanced_{timestamp}")

    # Run enhanced visualization
    visualize_swin_mae_hierarchical_reconstruction_enhanced(
        model_path=args.model_path,
        image_path=args.image_path,
        output_dir=output_dir_final,
        hu_min=args.hu_min,
        hu_max=args.hu_max,
        mask_ratios=args.mask_ratios,
        args=args
    )

    print(f"Enhanced visualization completed. Results saved to: {output_dir_final}")
