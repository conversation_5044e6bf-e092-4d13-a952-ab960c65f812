# Copyright (c) Meta Platforms, Inc. and affiliates.
# All rights reserved.

# This source code is licensed under the license found in the
# LICENSE file in the root directory of this source tree.

import math

def adjust_learning_rate(optimizer, epoch, args):
    """Decay the learning rate with half-cycle cosine after warmup"""
    if epoch < args.warmup_epochs:
        lr = args.lr * (epoch + 1) / args.warmup_epochs # Use epoch directly for epoch-level warmup adjustment
    else:
        lr = args.min_lr + (args.lr - args.min_lr) * 0.5 * \
            (1. + math.cos(math.pi * (epoch - args.warmup_epochs) / (args.epochs - args.warmup_epochs)))
    for param_group in optimizer.param_groups:
        if "lr_scale" in param_group:
            param_group["lr"] = lr * param_group["lr_scale"]
        else:
            param_group["lr"] = lr
    return lr

# Note: The train_epoch_mae function currently implements a step-based linear warmup.
# The adjust_learning_rate function above provides an epoch-based warmup + cosine decay.
# To use the function above, the training loop logic would need to be adjusted
# to call this function once per epoch instead of adjusting LR per step during warmup,
# and the CosineAnnealingLR scheduler should be removed.
# For simplicity, we will keep the current step-based linear warmup in train_epoch_mae
# and the CosineAnnealingLR scheduler which starts after warmup.
# This file is created primarily to resolve the ModuleNotFoundError,
# even though its main function isn't directly called by the current train_epoch_mae logic.
