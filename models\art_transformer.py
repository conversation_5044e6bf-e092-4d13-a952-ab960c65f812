import torch
import torch.nn as nn
import torch.nn.functional as F
from modules.attention import ShiftWindowAttention, DualAttention

class SplitAttention(nn.Module):
    """分组注意力"""
    def __init__(self, dim, groups=4):
        super().__init__()
        self.groups = groups
        self.group_dim = dim // groups
        self.qkv = nn.Linear(dim, dim * 3)
        self.proj = nn.Linear(dim, dim)

    def forward(self, x):
        B, C, H, W = x.shape
        x = x.view(B, self.groups, self.group_dim, H, W).permute(0, 1, 3, 4, 2) # B, G, H, W, C//G

        qkv = self.qkv(x).reshape(B, self.groups, H, W, 3, self.group_dim).permute(4, 0, 1, 2, 3, 5)
        q, k, v = qkv[0], qkv[1], qkv[2]  # B, G, H, W, C//G

        attn = (q @ k.transpose(-2, -1)) * (self.group_dim ** -0.5)
        attn = attn.softmax(dim=-1)

        x = (attn @ v).transpose(2, 3).reshape(B, H, W, C)
        x = x.permute(0, 3, 1, 2).contiguous() # B, C, H, W
        x = self.proj(x)
        return x

class FeedForward(nn.Module):
    """前馈网络"""
    def __init__(self, dim, expansion=4, dropout=0.):
        super().__init__()
        hidden_dim = int(dim * expansion)
        self.fc1 = nn.Conv2d(dim, hidden_dim, 1)
        self.act = nn.GELU()
        self.fc2 = nn.Conv2d(hidden_dim, dim, 1)
        self.dropout = nn.Dropout(dropout)

    def forward(self, x):
        x = self.fc1(x)
        x = self.act(x)
        x = self.dropout(x)
        x = self.fc2(x)
        x = self.dropout(x)
        return x

class DenseConnector(nn.Module):
    """密集连接"""
    def __init__(self, dim):
        super().__init__()
        self.conv1 = nn.Conv2d(dim, dim, 1)
        self.conv2 = nn.Conv2d(dim*2, dim, 1)
        self.conv3 = nn.Conv2d(dim*3, dim, 1)

    def forward(self, x1, x2, x3):
        x12 = torch.cat([x1, x2], dim=1)
        x12 = F.relu(self.conv2(x12))
        
        x123 = torch.cat([x1, x12, x3], dim=1)
        x123 = F.relu(self.conv3(x123))
        
        return x123

class ARTBlock(nn.Module):
    """交替残差变换块"""
    def __init__(self, dim, window_size=8, num_heads=8, shift_size=4, ffn_expansion=4, drop_path=0.1):
        super().__init__()
        self.dim = dim
        # 分支1: 自注意力路径
        self.norm1 = nn.LayerNorm(dim)
        self.attn = ShiftWindowAttention(dim, window_size, shift_size, num_heads)
        
        # 分支2: 前馈网络路径
        self.norm2 = nn.LayerNorm(dim)
        self.mlp = FeedForward(dim, expansion=ffn_expansion)
        
        # 密集连接
        self.dense_connect = DenseConnector(dim)
        
        # 通道注意力
        self.channel_attn = DualAttention(dim)
        
        # 深度可分离卷积
        self.dwconv = nn.Conv2d(dim, dim, kernel_size=7, padding=3, groups=dim)
        
        # Drop path
        self.drop_path = nn.Dropout(drop_path) if drop_path > 0. else nn.Identity()

    def forward(self, x):
        # 确保输入张量维度正确
        if len(x.shape) == 3:
            B, H, W = x.shape
            x = x.unsqueeze(1)  # 添加通道维度 [B,1,H,W]
        elif len(x.shape) != 4:
            raise ValueError(f"Expected 3D or 4D input tensor, got shape {x.shape}")
            
        B, C, H, W = x.shape
        
        # 自注意力分支
        x_attn = x.permute(0, 2, 3, 1)  # [B,H,W,C]
        x_attn = self.norm1(x_attn.reshape(-1, self.dim)).reshape(B, H, W, -1)
        x_attn = x_attn.permute(0, 3, 1, 2)  # [B,C,H,W]
        x_attn = self.attn(x_attn)
        
        # 前馈网络分支
        x_mlp = x.permute(0, 2, 3, 1)  # [B,H,W,C]
        x_mlp = self.norm2(x_mlp.reshape(-1, self.dim)).reshape(B, H, W, -1)
        x_mlp = x_mlp.permute(0, 3, 1, 2)  # [B,C,H,W]
        x_mlp = self.mlp(x_mlp)
        
        # 密集连接
        x_dense = self.dense_connect(x, x_attn, x_mlp)  # [B,C,H,W]
        
        # 通道注意力
        x_ca = self.channel_attn(x_dense)  # [B,C,H,W]
        
        # 深度可分离卷积
        x_dw = self.dwconv(x_ca)  # [B,C,H,W]
        
        # 残差连接与Drop Path
        return x + self.drop_path(x_dw)  # [B,C,H,W]

class TransformerEncoder(nn.Module):
    """Transformer编码器"""
    def __init__(self, dim=64, depth=3, window_size=8, num_heads=4, shift_size=4, ffn_expansion=2, drop_path_rate=0.1):
        super().__init__()
        self.blocks = nn.ModuleList([
            ARTBlock(
                dim=dim,
                window_size=window_size,
                num_heads=num_heads,
                shift_size=shift_size,
                ffn_expansion=ffn_expansion,
                drop_path=drop_path_rate * i / (depth - 1)
            ) for i in range(depth)
        ])

    def forward(self, x):
        for block in self.blocks:
            x = block(x)
        return x
