#!/usr/bin/env python3
"""
Evaluation script for Super-Resolution models
Calculates PSNR, SSIM, LPIPS and other metrics
"""

import os
import sys
import argparse
import torch
import numpy as np
from PIL import Image
import cv2
from skimage.metrics import peak_signal_noise_ratio, structural_similarity
from tqdm import tqdm
import matplotlib.pyplot as plt
import seaborn as sns

# Add project root to path
PROJECT_ROOT = os.path.abspath(os.path.join(os.path.dirname(__file__), '..'))
if PROJECT_ROOT not in sys.path:
    sys.path.insert(0, PROJECT_ROOT)

try:
    import lpips
    LPIPS_AVAILABLE = True
except ImportError:
    LPIPS_AVAILABLE = False
    print("Warning: LPIPS not available. Install with: pip install lpips")

def load_image_pair(lr_path, hr_path, sr_path):
    """Load LR, HR, and SR images"""
    lr_img = np.array(Image.open(lr_path).convert('L'), dtype=np.float32) / 65535.0
    hr_img = np.array(Image.open(hr_path).convert('L'), dtype=np.float32) / 65535.0
    sr_img = np.array(Image.open(sr_path).convert('L'), dtype=np.float32) / 65535.0
    
    return lr_img, hr_img, sr_img

def get_circle_mask(image_shape, radius_ratio=0.45):
    """Generate circular mask for CT images"""
    h, w = image_shape
    center_y, center_x = h // 2, w // 2
    radius = int(min(h, w) * radius_ratio)
    Y, X = np.ogrid[:h, :w]
    dist_from_center = np.sqrt((X - center_x)**2 + (Y - center_y)**2)
    return dist_from_center <= radius

def calculate_psnr(img1, img2, mask=None):
    """Calculate PSNR between two images"""
    if mask is not None:
        img1 = img1[mask]
        img2 = img2[mask]
    
    mse = np.mean((img1 - img2) ** 2)
    if mse == 0:
        return float('inf')
    
    return 20 * np.log10(1.0 / np.sqrt(mse))

def calculate_ssim(img1, img2, mask=None):
    """Calculate SSIM between two images"""
    if mask is not None:
        # For masked SSIM, we need to work with the full images
        # but weight the calculation
        return structural_similarity(img1, img2, data_range=1.0)
    else:
        return structural_similarity(img1, img2, data_range=1.0)

def calculate_lpips(img1, img2, lpips_model, device):
    """Calculate LPIPS between two images"""
    if not LPIPS_AVAILABLE:
        return None
    
    # Convert to 3-channel and tensor
    img1_tensor = torch.from_numpy(img1).unsqueeze(0).unsqueeze(0).repeat(1, 3, 1, 1)
    img2_tensor = torch.from_numpy(img2).unsqueeze(0).unsqueeze(0).repeat(1, 3, 1, 1)
    
    # Normalize to [-1, 1]
    img1_tensor = (img1_tensor - 0.5) * 2.0
    img2_tensor = (img2_tensor - 0.5) * 2.0
    
    img1_tensor = img1_tensor.to(device)
    img2_tensor = img2_tensor.to(device)
    
    with torch.no_grad():
        lpips_score = lpips_model(img1_tensor, img2_tensor)
    
    return lpips_score.item()

def calculate_edge_preservation(img1, img2, mask=None):
    """Calculate edge preservation metric"""
    # Calculate gradients
    grad1_x = cv2.Sobel(img1, cv2.CV_64F, 1, 0, ksize=3)
    grad1_y = cv2.Sobel(img1, cv2.CV_64F, 0, 1, ksize=3)
    grad1_mag = np.sqrt(grad1_x**2 + grad1_y**2)
    
    grad2_x = cv2.Sobel(img2, cv2.CV_64F, 1, 0, ksize=3)
    grad2_y = cv2.Sobel(img2, cv2.CV_64F, 0, 1, ksize=3)
    grad2_mag = np.sqrt(grad2_x**2 + grad2_y**2)
    
    if mask is not None:
        grad1_mag = grad1_mag[mask]
        grad2_mag = grad2_mag[mask]
    
    # Calculate correlation
    correlation = np.corrcoef(grad1_mag.flatten(), grad2_mag.flatten())[0, 1]
    return correlation if not np.isnan(correlation) else 0.0

def evaluate_model(lr_dir, hr_dir, sr_dir, output_dir):
    """Evaluate super-resolution model performance"""
    
    # Setup device and LPIPS model
    device = torch.device('cuda' if torch.cuda.is_available() else 'cpu')
    lpips_model = None
    if LPIPS_AVAILABLE:
        lpips_model = lpips.LPIPS(net='alex').to(device)
    
    # Get file lists
    lr_files = sorted([f for f in os.listdir(lr_dir) if f.endswith('.tif')])
    hr_files = sorted([f for f in os.listdir(hr_dir) if f.endswith('.tif')])
    sr_files = sorted([f for f in os.listdir(sr_dir) if f.endswith('.tif')])
    
    # Find matching files
    common_files = []
    for lr_file in lr_files:
        base_name = lr_file.replace('.tif', '')
        
        # Look for corresponding HR file
        hr_file = None
        for hf in hr_files:
            if base_name in hf:
                hr_file = hf
                break
        
        # Look for corresponding SR file
        sr_file = None
        for sf in sr_files:
            if base_name in sf or f"sr_{base_name}" in sf:
                sr_file = sf
                break
        
        if hr_file and sr_file:
            common_files.append((lr_file, hr_file, sr_file))
    
    print(f"Found {len(common_files)} matching file triplets")
    
    if len(common_files) == 0:
        print("No matching files found!")
        return
    
    # Initialize metrics storage
    metrics = {
        'psnr_sr_hr': [],
        'ssim_sr_hr': [],
        'lpips_sr_hr': [],
        'edge_preservation': [],
        'psnr_lr_hr': [],  # Baseline comparison
        'ssim_lr_hr': [],
        'filenames': []
    }
    
    # Process each file triplet
    for lr_file, hr_file, sr_file in tqdm(common_files, desc="Evaluating"):
        try:
            # Load images
            lr_path = os.path.join(lr_dir, lr_file)
            hr_path = os.path.join(hr_dir, hr_file)
            sr_path = os.path.join(sr_dir, sr_file)
            
            lr_img, hr_img, sr_img = load_image_pair(lr_path, hr_path, sr_path)
            
            # Resize LR to match HR for fair comparison
            lr_img_resized = cv2.resize(lr_img, (hr_img.shape[1], hr_img.shape[0]), 
                                      interpolation=cv2.INTER_CUBIC)
            
            # Generate circular mask
            mask = get_circle_mask(hr_img.shape)
            
            # Calculate metrics
            psnr_sr = calculate_psnr(sr_img, hr_img, mask)
            ssim_sr = calculate_ssim(sr_img, hr_img, mask)
            edge_pres = calculate_edge_preservation(sr_img, hr_img, mask)
            
            # Baseline metrics (LR vs HR)
            psnr_lr = calculate_psnr(lr_img_resized, hr_img, mask)
            ssim_lr = calculate_ssim(lr_img_resized, hr_img, mask)
            
            # LPIPS (if available)
            lpips_score = None
            if lpips_model:
                lpips_score = calculate_lpips(sr_img, hr_img, lpips_model, device)
            
            # Store metrics
            metrics['psnr_sr_hr'].append(psnr_sr)
            metrics['ssim_sr_hr'].append(ssim_sr)
            metrics['lpips_sr_hr'].append(lpips_score)
            metrics['edge_preservation'].append(edge_pres)
            metrics['psnr_lr_hr'].append(psnr_lr)
            metrics['ssim_lr_hr'].append(ssim_lr)
            metrics['filenames'].append(lr_file)
            
        except Exception as e:
            print(f"Error processing {lr_file}: {e}")
            continue
    
    # Calculate summary statistics
    print("\n" + "="*50)
    print("EVALUATION RESULTS")
    print("="*50)
    
    print(f"\nSuper-Resolution vs Ground Truth:")
    print(f"PSNR: {np.mean(metrics['psnr_sr_hr']):.2f} ± {np.std(metrics['psnr_sr_hr']):.2f} dB")
    print(f"SSIM: {np.mean(metrics['ssim_sr_hr']):.4f} ± {np.std(metrics['ssim_sr_hr']):.4f}")
    if metrics['lpips_sr_hr'][0] is not None:
        lpips_values = [x for x in metrics['lpips_sr_hr'] if x is not None]
        print(f"LPIPS: {np.mean(lpips_values):.4f} ± {np.std(lpips_values):.4f}")
    print(f"Edge Preservation: {np.mean(metrics['edge_preservation']):.4f} ± {np.std(metrics['edge_preservation']):.4f}")
    
    print(f"\nBaseline (Bicubic LR vs Ground Truth):")
    print(f"PSNR: {np.mean(metrics['psnr_lr_hr']):.2f} ± {np.std(metrics['psnr_lr_hr']):.2f} dB")
    print(f"SSIM: {np.mean(metrics['ssim_lr_hr']):.4f} ± {np.std(metrics['ssim_lr_hr']):.4f}")
    
    print(f"\nImprovement:")
    psnr_improvement = np.mean(metrics['psnr_sr_hr']) - np.mean(metrics['psnr_lr_hr'])
    ssim_improvement = np.mean(metrics['ssim_sr_hr']) - np.mean(metrics['ssim_lr_hr'])
    print(f"PSNR Gain: +{psnr_improvement:.2f} dB")
    print(f"SSIM Gain: +{ssim_improvement:.4f}")
    
    # Save detailed results
    os.makedirs(output_dir, exist_ok=True)
    
    # Save metrics to file
    results_file = os.path.join(output_dir, 'evaluation_results.txt')
    with open(results_file, 'w') as f:
        f.write("Super-Resolution Model Evaluation Results\n")
        f.write("="*50 + "\n\n")
        f.write(f"Number of test images: {len(metrics['psnr_sr_hr'])}\n\n")
        
        f.write("Super-Resolution vs Ground Truth:\n")
        f.write(f"PSNR: {np.mean(metrics['psnr_sr_hr']):.2f} ± {np.std(metrics['psnr_sr_hr']):.2f} dB\n")
        f.write(f"SSIM: {np.mean(metrics['ssim_sr_hr']):.4f} ± {np.std(metrics['ssim_sr_hr']):.4f}\n")
        if metrics['lpips_sr_hr'][0] is not None:
            lpips_values = [x for x in metrics['lpips_sr_hr'] if x is not None]
            f.write(f"LPIPS: {np.mean(lpips_values):.4f} ± {np.std(lpips_values):.4f}\n")
        f.write(f"Edge Preservation: {np.mean(metrics['edge_preservation']):.4f} ± {np.std(metrics['edge_preservation']):.4f}\n\n")
        
        f.write("Baseline (Bicubic LR vs Ground Truth):\n")
        f.write(f"PSNR: {np.mean(metrics['psnr_lr_hr']):.2f} ± {np.std(metrics['psnr_lr_hr']):.2f} dB\n")
        f.write(f"SSIM: {np.mean(metrics['ssim_lr_hr']):.4f} ± {np.std(metrics['ssim_lr_hr']):.4f}\n\n")
        
        f.write("Improvement:\n")
        f.write(f"PSNR Gain: +{psnr_improvement:.2f} dB\n")
        f.write(f"SSIM Gain: +{ssim_improvement:.4f}\n")
    
    # Create visualization plots
    create_evaluation_plots(metrics, output_dir)
    
    print(f"\nDetailed results saved to: {results_file}")
    print(f"Visualization plots saved to: {output_dir}")

def create_evaluation_plots(metrics, output_dir):
    """Create visualization plots for evaluation results"""
    
    # Set style
    plt.style.use('seaborn-v0_8')
    
    # Plot 1: PSNR comparison
    fig, (ax1, ax2) = plt.subplots(1, 2, figsize=(12, 5))
    
    ax1.hist(metrics['psnr_sr_hr'], bins=20, alpha=0.7, label='SR vs HR', color='blue')
    ax1.hist(metrics['psnr_lr_hr'], bins=20, alpha=0.7, label='LR vs HR', color='red')
    ax1.set_xlabel('PSNR (dB)')
    ax1.set_ylabel('Frequency')
    ax1.set_title('PSNR Distribution')
    ax1.legend()
    ax1.grid(True, alpha=0.3)
    
    # Plot 2: SSIM comparison
    ax2.hist(metrics['ssim_sr_hr'], bins=20, alpha=0.7, label='SR vs HR', color='blue')
    ax2.hist(metrics['ssim_lr_hr'], bins=20, alpha=0.7, label='LR vs HR', color='red')
    ax2.set_xlabel('SSIM')
    ax2.set_ylabel('Frequency')
    ax2.set_title('SSIM Distribution')
    ax2.legend()
    ax2.grid(True, alpha=0.3)
    
    plt.tight_layout()
    plt.savefig(os.path.join(output_dir, 'metrics_distribution.png'), dpi=300, bbox_inches='tight')
    plt.close()
    
    # Plot 3: Scatter plot of improvements
    fig, ax = plt.subplots(1, 1, figsize=(8, 6))
    
    psnr_improvements = np.array(metrics['psnr_sr_hr']) - np.array(metrics['psnr_lr_hr'])
    ssim_improvements = np.array(metrics['ssim_sr_hr']) - np.array(metrics['ssim_lr_hr'])
    
    ax.scatter(psnr_improvements, ssim_improvements, alpha=0.6)
    ax.set_xlabel('PSNR Improvement (dB)')
    ax.set_ylabel('SSIM Improvement')
    ax.set_title('Per-Image Improvements')
    ax.grid(True, alpha=0.3)
    
    # Add quadrant lines
    ax.axhline(y=0, color='k', linestyle='--', alpha=0.5)
    ax.axvline(x=0, color='k', linestyle='--', alpha=0.5)
    
    plt.tight_layout()
    plt.savefig(os.path.join(output_dir, 'improvement_scatter.png'), dpi=300, bbox_inches='tight')
    plt.close()

def main():
    parser = argparse.ArgumentParser(description='Evaluate Super-Resolution Model')
    parser.add_argument('--lr_dir', type=str, required=True, help='Directory with LR images')
    parser.add_argument('--hr_dir', type=str, required=True, help='Directory with HR images')
    parser.add_argument('--sr_dir', type=str, required=True, help='Directory with SR results')
    parser.add_argument('--output_dir', type=str, default='evaluation_results', help='Output directory')
    
    args = parser.parse_args()
    
    evaluate_model(args.lr_dir, args.hr_dir, args.sr_dir, args.output_dir)

if __name__ == '__main__':
    main()
