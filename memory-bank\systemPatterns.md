# CT图像超分辨率项目系统架构

## 系统架构

```
┌─────────────────┐     ┌─────────────────────────────┐     ┌─────────────────┐
│  数据预处理模块  │────▶│        模型训练模块         │────▶│  推理与评估模块  │
└─────────────────┘     └─────────────────────────────┘     └─────────────────┘
        │                             │                             │
        ▼                             ▼                             ▼
┌─────────────────┐     ┌─────────────────────────────┐     ┌─────────────────┐
│    数据加载器    │     │          损失函数           │     │    评估指标     │
└─────────────────┘     └─────────────────────────────┘     └─────────────────┘
        │                             │
        │                             │
        ▼                             ▼
┌─────────────────┐     ┌─────────────┬─────────────┬─────────────┐
│  数据增强模块    │     │ 自监督预训练 │ 监督训练    │ 模型架构    │
└─────────────────┘     └─────────────┴─────────────┴─────────────┘
```

### 详细架构

```
┌───────────────────────────────────────────────────────────────────────────┐
│                              数据处理模块                                  │
├───────────────────────────────────────────────────────────────────────────┤
│ ┌─────────────────┐  ┌─────────────────┐  ┌─────────────────────────────┐ │
│ │  mae_loader.py  │  │  ct_loader.py   │  │       数据增强模块          │ │
│ │                 │  │                 │  │  (augmentations.py)         │ │
│ └─────────────────┘  └─────────────────┘  └─────────────────────────────┘ │
└───────────────────────────────────────────────────────────────────────────┘

┌───────────────────────────────────────────────────────────────────────────┐
│                              自监督预训练模块                              │
├───────────────────────────────────────────────────────────────────────────┤
│ ┌─────────────────┐  ┌─────────────────┐  ┌─────────────────────────────┐ │
│ │ train_mae.py    │  │ train_swin_mae.py│  │ train_enhanced.py          │ │
│ │ (ViT + MAE)     │  │ (Swin + MAE)    │  │ (多尺度对比学习)            │ │
│ └─────────────────┘  └─────────────────┘  └─────────────────────────────┘ │
└───────────────────────────────────────────────────────────────────────────┘

┌───────────────────────────────────────────────────────────────────────────┐
│                              监督训练模块                                  │
├───────────────────────────────────────────────────────────────────────────┤
│ ┌─────────────────────────────────────────────────────────────────────────┐│
│ │                      EnhancedSRNetwork                                  ││
│ │                                                                         ││
│ │  ┌─────────────┐  ┌─────────────┐  ┌─────────────┐  ┌─────────────┐    ││
│ │  │ 预训练编码器 │  │ 伪3D处理    │  │ 注意力模块  │  │ 上采样模块  │    ││
│ │  └─────────────┘  └─────────────┘  └─────────────┘  └─────────────┘    ││
│ └─────────────────────────────────────────────────────────────────────────┘│
└───────────────────────────────────────────────────────────────────────────┘

┌───────────────────────────────────────────────────────────────────────────┐
│                              损失函数模块                                  │
├───────────────────────────────────────────────────────────────────────────┤
│ ┌─────────────────┐  ┌─────────────────┐  ┌─────────────────────────────┐ │
│ │  像素损失       │  │  感知损失       │  │  频域损失                   │ │
│ │  (L1/MSE)       │  │ (PerceptualLoss)│  │ (FrequencyLoss)             │ │
│ └─────────────────┘  └─────────────────┘  └─────────────────────────────┘ │
│ ┌─────────────────┐  ┌─────────────────┐  ┌─────────────────────────────┐ │
│ │  结构相似性损失  │  │  纹理损失       │  │  复合损失                   │ │
│ │  (SSIM Loss)    │  │ (TextureLoss)   │  │ (CompositeLoss)             │ │
│ └─────────────────┘  └─────────────────┘  └─────────────────────────────┘ │
└───────────────────────────────────────────────────────────────────────────┘
```

## 关键技术决策

1. **多种预训练方法实现**：
   - 实现了三种预训练方法：
     - Swin Transformer + MAE（`train_swin_mae.py`）
     - Vision Transformer + MAE（`train_mae.py`）
     - 多尺度对比学习（`train_enhanced.py`）
   - 理由：不同预训练方法可能捕获不同类型的特征，提供更全面的初始化选择

2. **两阶段训练策略**：
   - 第一阶段：自监督预训练（无需配对数据）
   - 第二阶段：监督训练（需要高低分辨率配对数据）
   - 理由：充分利用有限的高分辨率数据，通过自监督学习提高模型的泛化能力

3. **非重叠数据处理策略**：
   - 伪配对训练：对高分辨率数据降采样创建配对数据
   - 特征级对齐：在特征空间而非像素空间比较差异
   - 域适应方法：学习低分辨率域到高分辨率域的映射
   - 理由：数据集#2和#3来自同一岩心的不同、非重叠部位，需要特殊策略处理

4. **多损失函数组合**：
   - 像素损失（L1/MSE）：基础重建准确性
   - 感知损失（PerceptualLoss）：视觉质量
   - 频域损失（FrequencyLoss）：高频细节恢复
   - 结构相似性损失（StructureSimilarityLoss）：结构保持
   - 纹理损失（TextureLoss）：岩心纹理特征保持
   - 理由：不同损失函数关注图像不同方面的质量，组合使用可以获得更好的重建效果

5. **增强型超分辨率网络设计**：
   - 集成预训练编码器：利用自监督学习的特征表示
   - 伪3D处理：利用CT切片间的关系
   - 多种注意力机制：频域注意力、小波注意力、可变形大核注意力等
   - 多尺度特征融合：捕获不同尺度的结构信息
   - 理由：综合利用多种先进技术，更好地处理CT图像的复杂特性

6. **灰度图像特殊处理**：
   - 感知损失中通过通道复制（灰度→RGB）处理灰度图
   - 频域损失考虑体素大小差异，动态调整权重
   - 复合损失根据体素大小比例调整损失权重
   - 理由：标准损失函数通常针对RGB自然图像设计，需要适应CT图像的灰度特性和体素大小差异

7. **训练优化技术**：
   - 梯度累积：处理大尺寸CT图像时增加有效批量大小
   - 混合精度训练：使用FP16加速训练
   - 学习率预热和余弦退火：优化训练过程
   - 理由：2700×2700大尺寸CT图像需要大量GPU内存，需要特殊技术优化训练过程

## 设计模式

1. **模块化设计**：
   - 数据处理、模型定义、损失函数、训练循环分离
   - 便于替换和比较不同组件

2. **配置驱动**：
   - 使用配置文件或命令行参数控制训练参数
   - 便于实验管理和结果复现

3. **回调机制**：
   - 使用回调函数处理训练过程中的日志记录、模型保存等任务
   - 提高代码可维护性

4. **实验跟踪**：
   - 记录每次实验的参数和结果
   - 便于比较不同方法的效果

## 组件关系

### 数据处理流程

```
原始CT图像 ──▶ 预处理 ──▶ 数据增强 ──▶ 批处理 ──▶ 模型输入
```

### 模型结构

```
                  ┌─────────────────┐
                  │    Swin-MAE     │
                  └─────────────────┘
                          │
          ┌───────────────┴───────────────┐
          ▼                               ▼
┌─────────────────┐             ┌─────────────────┐
│  Swin编码器     │             │  MAE解码器      │
└─────────────────┘             └─────────────────┘
```

### 损失函数计算

```
                      ┌─────────────────┐
                      │    总损失函数    │
                      └─────────────────┘
                              │
      ┌─────────────┬─────────┼─────────┬─────────────┐
      ▼             ▼         ▼         ▼             ▼
┌──────────┐  ┌──────────┐ ┌─────┐ ┌──────────┐  ┌──────────┐
│ 重建损失 │  │ 感知损失 │ │ ... │ │特征模仿损失│  │ 对比损失 │
└──────────┘  └──────────┘ └─────┘ └──────────┘  └──────────┘
```

## 关键实现路径

1. **自监督预训练**：
   ```
   数据加载 ──▶ 随机掩蔽 ──▶ 编码器处理 ──▶ 解码器重建 ──▶ 计算重建损失 ──▶ 模型更新
   ```

2. **监督训练**：
   ```
   低分辨率输入 ──▶ 预训练模型 ──▶ 超分辨率输出 ──▶ 与高分辨率参考比较 ──▶ 计算多种损失 ──▶ 模型更新
   ```

3. **推理过程**：
   ```
   低分辨率输入 ──▶ 训练好的模型 ──▶ 超分辨率输出 ──▶ 后处理 ──▶ 最终结果
   ```

## 扩展性考虑

1. **模型替换**：
   - 设计接口允许替换编码器或解码器
   - 便于尝试不同的Transformer变体或其他架构

2. **损失函数扩展**：
   - 提供接口添加新的损失函数
   - 灵活调整损失函数权重

3. **数据适应性**：
   - 设计通用数据加载器，适应不同尺寸和特性的CT图像
   - 支持不同的数据增强策略
