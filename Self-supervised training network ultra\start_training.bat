@echo off
echo ============================================================
echo CT图像超分辨率训练启动脚本
echo ============================================================
echo.

echo 🔧 激活Python环境: pytorchEnv
call conda activate pytorchEnv
if %errorlevel% neq 0 (
    echo ❌ 无法激活pytorchEnv环境
    echo 请确保已安装conda并且pytorchEnv环境存在
    pause
    exit /b 1
)

echo ✅ 环境激活成功
echo.

echo 📁 切换到训练目录
cd /d "%~dp0"
echo 当前目录: %cd%
echo.

echo 🚀 开始训练...
echo ============================================================
python run_training.py

echo.
echo ============================================================
echo 训练脚本执行完成
echo ============================================================
pause
