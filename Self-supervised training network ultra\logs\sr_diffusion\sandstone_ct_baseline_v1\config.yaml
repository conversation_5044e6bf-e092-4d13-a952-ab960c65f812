data:
  beam_hardening_intensity:
  - 0.08
  - 0.15
  degradation_intensity: medium
  degradation_method: balanced_sandstone_ct
  electronic_noise_range:
  - 1.0
  - 6.0
  hr_dir: "data/3\u53F7CT\u6570\u636E"
  lr_dir: "data/2\u53F7CT\u6570\u636E"
  partial_volume_blur_sigma:
  - 0.3
  - 1.0
  partial_volume_intensity:
  - 0.02
  - 0.08
  patch_size_hr: 128
  quantum_photon_range:
  - 200
  - 1000
  reconstruction_blur_sigma:
  - 0.2
  - 0.8
  ring_artifact_intensity:
  - 0.03
  - 0.12
  ring_frequency_range:
  - 0.05
  - 0.4
  rock_artifact_prob: 0.7
  rock_blur_prob: 0.5
  rock_noise_prob: 0.9
  scale_factor: 4
  scatter_intensity_range:
  - 0.02
  - 0.08
  use_gpu_acceleration: true
diffusion:
  schedule_name: cosine
  timesteps: 1000
discriminator:
  lr: 0.0001
  n_layers: 3
  ndf: 64
experiment:
  description: Diffusion SR with sandstone CT-specific degradation modeling
  name: Sandstone_CT_Adaptive_v1
  tags:
  - diffusion
  - super-resolution
  - sandstone-ct
  - rock-imaging
  - adaptive-degradation
inference:
  eta: 0.0
  sampling_steps: 50
model:
  attention_resolutions:
  - 16
  - 8
  base_channels: 128
  channel_mults:
  - 1
  - 2
  - 2
  - 4
  condition_method: CrossAttention
  dropout: 0.1
  encoder_checkpoint: null
  encoder_type: Swin-MAE
  in_channels: 1
  num_heads: 8
  num_res_blocks: 2
  out_channels: 1
  use_pretrained_encoder: false
training:
  batch_size: 8
  checkpoint_root: ./checkpoints/sr_diffusion
  diffusion_loss_type: l1
  epochs: 120
  gan_loss_weight: 0.0
  grad_clip_norm: 1.0
  gradient_accumulation_steps: 4
  gradient_loss_weight: 0.2
  learning_rate: 8.0e-05
  log_interval: 25
  log_root: ./logs/sr_diffusion
  num_workers: 8
  perceptual_loss_type: l1
  perceptual_loss_weight: 0.1
  save_interval: 5
  scheduler_type: cosine
  seed: 42
  ssim_loss_weight: 0.35
  use_amp: true
  use_gan: false
  use_scheduler: true
  warmup_epochs: 30
  weight_decay: 0.0001
