import os
import numpy as np
from PIL import Image
import torch
from torch.utils.data import Dataset, DataLoader
from torchvision import transforms
import torch.nn.functional as F
import random
import cv2
from skimage.exposure import equalize_adapthist, adjust_gamma
from skimage.util import random_noise
import torchvision.transforms.functional as TF

# Helper function to filter None samples from the batch
def filter_none(batch):
    batch = list(filter(lambda x: x is not None and x[0] is not None and x[1] is not None, batch))
    if not batch:
        # Return empty tensors if batch is empty, matching expected output structure
        # Assuming Enhanced3DCTDataset returns (lr_sequence, hr_tensor)
        # Adjust the shape based on what the model expects if necessary
        # Example: return torch.empty(0, 5, 1, 32, 32), torch.empty(0, 1, 1, 128, 128)
        # For now, returning two empty tensors.
        return torch.empty(0), torch.empty(0)
    return torch.utils.data.dataloader.default_collate(batch)

# Helper function for dictionary outputs
def filter_none_dict(batch):
    batch = list(filter(lambda x: x is not None, batch))
    if not batch:
        # Return an empty dictionary or handle as appropriate
        print("Warning: Entire batch filtered out.")
        return None # Or handle differently, e.g., return {}
    # Assuming the default collate can handle a list of dictionaries
    return torch.utils.data.dataloader.default_collate(batch)


class Enhanced3DCTDataset(Dataset):
    """增强的3D CT数据集加载器"""
    def __init__(self, lr_dir, hr_dir, temporal_window=3, patch_size=64, scale=4, augment=True,
                 lr_voxel_size=0.03889, hr_voxel_size=0.00956, voxel_adapt=True,
                 voxel_tolerance=0.05, interp_mode='bicubic'):
        super().__init__()
        self.lr_paths = self._get_sorted_paths(lr_dir)
        self.hr_paths = self._get_sorted_paths(hr_dir)
        self.temporal_window = temporal_window
        self.patch_size = patch_size # LR patch size
        self.hr_patch_size = patch_size * scale # HR patch size
        self.scale = scale
        self.augment = augment

        # 添加圆形mask相关的参数
        self.image_size = 2700 # Assuming original image size for radius calculation
        self.center = self.image_size // 2
        self.valid_radius = int(self.image_size * 0.45)

        # 增强的体素大小适配参数
        self.lr_voxel_size = lr_voxel_size
        self.hr_voxel_size = hr_voxel_size
        self.voxel_ratio = self.hr_voxel_size / self.lr_voxel_size if self.lr_voxel_size > 0 else 1.0
        self.voxel_adapt = voxel_adapt
        self.voxel_tolerance = voxel_tolerance
        self.interp_mode = interp_mode

        # 体素尺寸校验
        self._verify_voxel_size()

        # 数据增强
        self.transform = self._get_enhanced_transform() if augment else None

    def _get_enhanced_transform(self):
        # More comprehensive transforms for supervised learning if needed
        return transforms.Compose([
            transforms.RandomHorizontalFlip(),
            transforms.RandomVerticalFlip(),
            transforms.RandomRotation(45), # Increased rotation
            # ColorJitter might not be suitable for grayscale CT
            # transforms.ColorJitter(brightness=0.2, contrast=0.2),
            # Add other relevant transforms like elastic deformation if applicable
        ])

    def _verify_voxel_size(self):
        # Placeholder for actual DICOM header reading if available
        actual_ratio = self.voxel_ratio
        expected_ratio = 4.07
        if abs(actual_ratio - expected_ratio) / expected_ratio > self.voxel_tolerance:
            print(f"警告: 体素尺寸比例 ({actual_ratio:.3f}) 与预期值 ({expected_ratio:.3f}) 相差超过 {self.voxel_tolerance*100}%")
            print(f"这可能导致空间不一致性问题。请检查输入数据或调整参数。")

    def _get_sorted_paths(self, directory):
         files = [f for f in os.listdir(directory) if f.endswith('.tif')]
         files.sort() # Simple alphabetical sort
         return [os.path.join(directory, f) for f in files]

    def _load_image(self, path):
        try:
            img = Image.open(path).convert('L')
            img = np.array(img, dtype=np.float32)
            max_val = np.max(img)
            if max_val > 255: img = img / 65535.0
            elif max_val > 1: img = img / 255.0
            img = np.clip(img, 0.0, 1.0)
            return img
        except Image.UnidentifiedImageError:
            print(f"Warning: Could not open or read image file: {path}, skipping.")
            return None
        except Exception as e:
            print(f"Error loading image {path}: {e}")
            return None

    def get_circle_mask(self, image_shape):
        """生成二维圆形mask based on shape"""
        h, w = image_shape
        center_y, center_x = h // 2, w // 2
        # Adjust radius based on actual image size relative to assumed 2700
        radius_ratio = min(h, w) / self.image_size if self.image_size > 0 else 1.0
        current_valid_radius = self.valid_radius * radius_ratio
        Y, X = np.ogrid[:h, :w]
        dist_from_center = np.sqrt((X - center_x)**2 + (Y - center_y)**2)
        return dist_from_center <= current_valid_radius

    def get_random_valid_coords(self, image_shape, patch_size):
        """获取有效区域内随机patch的左上角坐标"""
        mask = self.get_circle_mask(image_shape)
        valid_indices = np.argwhere(mask)
        if len(valid_indices) == 0 or image_shape[0] < patch_size or image_shape[1] < patch_size:
            return None, None

        attempts = 0
        max_attempts = 50
        h, w = image_shape

        while attempts < max_attempts:
            attempts += 1
            center_idx = random.choice(valid_indices)
            center_y, center_x = center_idx
            h_start = center_y - patch_size // 2
            w_start = center_x - patch_size // 2
            h_end = h_start + patch_size
            w_end = w_start + patch_size

            if (h_start >= 0 and w_start >= 0 and h_end <= h and w_end <= w):
                patch_mask = mask[h_start:h_end, w_start:w_end]
                if np.mean(patch_mask) >= 0.5: # Ensure patch is mostly valid
                    return h_start, w_start
        return None, None

    def __len__(self):
        return len(self.lr_paths) - self.temporal_window + 1

    def __getitem__(self, idx):
        # Get random coordinates for the LR patch
        # Load the first LR image to get shape for coordinate generation
        first_lr_img = self._load_image(self.lr_paths[idx])
        if first_lr_img is None: return None, None
        lr_h_start, lr_w_start = self.get_random_valid_coords(first_lr_img.shape, self.patch_size)
        if lr_h_start is None: return None, None

        # Calculate corresponding HR coordinates
        hr_h_start = lr_h_start * self.scale
        hr_w_start = lr_w_start * self.scale

        lr_patches_list = []
        for i in range(self.temporal_window):
            lr_path = self.lr_paths[idx + i]
            lr_img = self._load_image(lr_path)
            if lr_img is None: return None, None

            # Extract the patch using pre-calculated coordinates
            lr_patch_np = lr_img[lr_h_start : lr_h_start + self.patch_size, lr_w_start : lr_w_start + self.patch_size]
            if lr_patch_np.shape != (self.patch_size, self.patch_size):
                 print(f"Warning: LR patch shape mismatch for index {idx+i}. Expected ({self.patch_size},{self.patch_size}), got {lr_patch_np.shape}. Skipping.")
                 return None, None

            lr_tensor = torch.from_numpy(lr_patch_np).unsqueeze(0) # [1, P, P]
            lr_patches_list.append(lr_tensor)

        # Load corresponding HR center slice
        # Use a more robust mapping if LR/HR slices don't align perfectly
        hr_idx = idx + self.temporal_window // 2 # Center frame mapping
        hr_idx = min(max(0, hr_idx), len(self.hr_paths) - 1) # Clamp index
        hr_path = self.hr_paths[hr_idx]
        hr_img = self._load_image(hr_path)
        if hr_img is None: return None, None

        # Extract HR patch using corresponding coordinates
        hr_patch_np = hr_img[hr_h_start : hr_h_start + self.hr_patch_size, hr_w_start : hr_w_start + self.hr_patch_size]
        if hr_patch_np.shape != (self.hr_patch_size, self.hr_patch_size):
             print(f"Warning: HR patch shape mismatch for index {hr_idx}. Expected ({self.hr_patch_size},{self.hr_patch_size}), got {hr_patch_np.shape}. Skipping.")
             return None, None

        hr_tensor = torch.from_numpy(hr_patch_np).unsqueeze(0) # [1, sP, sP]

        # Stack LR patches -> [T, 1, P, P]
        lr_sequence_stacked = torch.stack(lr_patches_list, dim=0)

        # Apply augmentations (if defined and applicable)
        # This part needs careful consideration for temporal data
        # Example: Apply same geometric transform to all LR slices and HR slice
        if self.augment and self.transform:
            # Convert to PIL for torchvision transforms
            lr_pil_list = [TF.to_pil_image(lr.squeeze(0)) for lr in lr_sequence_stacked]
            hr_pil = TF.to_pil_image(hr_tensor.squeeze(0))

            # Get transform parameters (e.g., flip state, rotation angle)
            if random.random() > 0.5: # Random Horizontal Flip
                lr_pil_list = [TF.hflip(img) for img in lr_pil_list]
                hr_pil = TF.hflip(hr_pil)
            if random.random() > 0.5: # Random Vertical Flip
                lr_pil_list = [TF.vflip(img) for img in lr_pil_list]
                hr_pil = TF.vflip(hr_pil)
            angle = transforms.RandomRotation.get_params([-45, 45]) # Random Rotation
            lr_pil_list = [TF.rotate(img, angle) for img in lr_pil_list]
            hr_pil = TF.rotate(hr_pil, angle)

            # Convert back to tensor
            lr_sequence_stacked = torch.stack([TF.to_tensor(img) for img in lr_pil_list], dim=0) # [T, 1, P, P]
            hr_tensor = TF.to_tensor(hr_pil) # [1, sP, sP]


        # Final shapes expected by many models:
        # LR: [B, T, H, W] or [B, C=1, D=T, H, W]
        # HR: [B, 1, sH, sW]
        lr_final = lr_sequence_stacked.squeeze(1) # [T, P, P]
        # Add Batch dim: [1, T, P, P]
        lr_final = lr_final.unsqueeze(0)

        # Add Batch dim: [1, 1, sP, sP]
        hr_final = hr_tensor.unsqueeze(0)

        # --- Assertion Fix ---
        # Assert final shapes are 4D as expected by most 2D/pseudo-3D models
        expected_lr_shape_4d = (1, self.temporal_window, self.patch_size, self.patch_size)
        expected_hr_shape_4d = (1, 1, self.hr_patch_size, self.hr_patch_size)

        assert lr_final.shape == expected_lr_shape_4d, f"LR 最终形状错误: {lr_final.shape} vs {expected_lr_shape_4d}"
        assert hr_final.shape == expected_hr_shape_4d, f"HR 最终形状错误: {hr_final.shape} vs {expected_hr_shape_4d}"

        return lr_final, hr_final


# --- Augmentation Helper Functions ---
# ... (Keep the augmentation helper functions as they are) ...
def apply_elastic_transform(image, alpha, sigma, random_state=None):
    """Apply elastic deformation on an image as described in [Simard2003]_.
    .. [Simard2003] Simard, Steinkraus and Platt, "Best Practices for
       Convolutional Neural Networks applied to Visual Document Analysis", in
       Proc. of the International Conference on Document Analysis and
       Recognition, 2003.
    """
    if random_state is None:
        random_state = np.random.RandomState(None)

    shape = image.shape
    dx = cv2.GaussianBlur((random_state.rand(*shape) * 2 - 1), (0,0), sigma) * alpha
    dy = cv2.GaussianBlur((random_state.rand(*shape) * 2 - 1), (0,0), sigma) * alpha

    x, y = np.meshgrid(np.arange(shape[1]), np.arange(shape[0]))
    map_x = (x + dx).astype(np.float32)
    map_y = (y + dy).astype(np.float32)

    return cv2.remap(image, map_x, map_y, interpolation=cv2.INTER_LINEAR, borderMode=cv2.BORDER_REFLECT_101)

class ElasticTransform(object):
    def __init__(self, alpha, sigma):
        self.alpha = alpha
        self.sigma = sigma

    def __call__(self, img):
        img_np = np.array(img)
        alpha = random.uniform(self.alpha * 0.7, self.alpha * 1.3) # Randomize alpha
        sigma = random.uniform(self.sigma * 0.7, self.sigma * 1.3) # Randomize sigma
        transformed_img = apply_elastic_transform(img_np, alpha=alpha, sigma=sigma)
        return Image.fromarray(transformed_img)

class AdjustGamma(object):
    def __init__(self, gamma_range=(0.7, 1.3)):
        self.gamma_range = gamma_range

    def __call__(self, img):
        gamma = random.uniform(self.gamma_range[0], self.gamma_range[1])
        return TF.adjust_gamma(img, gamma)

class ApplyCLAHE(object):
    def __init__(self, clip_limit_range=(0.01, 0.03)):
         self.clip_limit_range = clip_limit_range

    def __call__(self, img):
        img_np = np.array(img)
        # Ensure image is in uint8 or uint16 for CLAHE
        if img_np.dtype == np.float32 or img_np.dtype == np.float64:
             # Scale to uint16 range for better precision with original data
             img_np = (img_np * 65535).astype(np.uint16)

        clip_limit = random.uniform(self.clip_limit_range[0], self.clip_limit_range[1])
        # Calculate kernel size based on image size, e.g., 1/8th of the smaller dimension
        kernel_size = (max(1, img_np.shape[0] // 8), max(1, img_np.shape[1] // 8))
        img_clahe = equalize_adapthist(img_np, kernel_size=kernel_size, clip_limit=clip_limit)

        # Convert back to float32 in [0, 1] range
        img_clahe = img_clahe.astype(np.float32)
        return Image.fromarray(img_clahe) # Return PIL image

class RandomNoise(object):
    def __init__(self, modes=['gaussian', 's&p'], intensity_range=(0.001, 0.005)):
        self.modes = modes
        self.intensity_range = intensity_range

    def __call__(self, img):
        img_np = np.array(img)
        mode = random.choice(self.modes)
        intensity = random.uniform(self.intensity_range[0], self.intensity_range[1])
        if mode == 'gaussian':
            noise_img = random_noise(img_np, mode=mode, var=intensity**2) # var is std^2
        elif mode == 's&p':
            noise_img = random_noise(img_np, mode=mode, amount=intensity)
        else:
            noise_img = img_np
        # Ensure output is float32 in [0, 1]
        noise_img = np.clip(noise_img, 0, 1).astype(np.float32)
        return Image.fromarray(noise_img)


class SelfSupervisedCTDataset(Dataset):
    """Dataset for self-supervised contrastive learning with multi-scale patches and advanced augmentations."""
    # --- MODIFIED: Changed default patch sizes ---
    def __init__(self, data_dir, patch_sizes=[64, 128, 256], augment=True):
        super().__init__()
        self.data_paths = self._get_sorted_paths(data_dir)
        self.patch_sizes = sorted(patch_sizes, reverse=True) # Store largest first
        self.augment = augment
        self.image_size = 2700
        self.center = self.image_size // 2
        self.valid_radius = int(self.image_size * 0.45)

        if augment:
            # --- MODIFIED: Simplified Augmentations ---
            self.transform1 = transforms.Compose([
                transforms.RandomHorizontalFlip(p=0.5),
                transforms.RandomVerticalFlip(p=0.5),
                transforms.RandomRotation(15), # Reduced rotation angle
                transforms.RandomApply([transforms.GaussianBlur(kernel_size=3, sigma=(0.1, 1.0))], p=0.3), # Mild Gaussian Blur
                transforms.ToTensor()
            ])
            self.transform2 = transforms.Compose([
                transforms.RandomHorizontalFlip(p=0.5),
                transforms.RandomVerticalFlip(p=0.5),
                transforms.RandomRotation(15), # Reduced rotation angle
                transforms.RandomApply([transforms.GaussianBlur(kernel_size=3, sigma=(0.1, 1.0))], p=0.3), # Mild Gaussian Blur (can use slightly different params if needed)
                transforms.ToTensor()
            ])
        else:
            self.transform1 = transforms.ToTensor()
            self.transform2 = transforms.ToTensor()

    def _get_sorted_paths(self, directory):
        files = [f for f in os.listdir(directory) if f.endswith('.tif')]
        files.sort()
        return [os.path.join(directory, f) for f in files]

    def _load_image(self, path):
        try:
            img = Image.open(path).convert('L')
            img_array = np.array(img, dtype=np.float32)
            max_val = np.max(img_array)
            if max_val > 255: img_array = img_array / 65535.0
            elif max_val > 1: img_array = img_array / 255.0
            img_array = np.clip(img_array, 0.0, 1.0)
            return img_array
        except Image.UnidentifiedImageError:
            print(f"Warning: Could not open or read image file: {path}, skipping.")
            return None
        except Exception as e:
            print(f"Warning: Error loading image {path}: {e}, skipping.")
            return None

    def get_circle_mask(self, image_shape):
        h, w = image_shape
        center_y, center_x = h // 2, w // 2
        radius_ratio = min(h, w) / self.image_size if self.image_size > 0 else 1.0
        current_valid_radius = self.valid_radius * radius_ratio
        Y, X = np.ogrid[:h, :w]
        dist_from_center = np.sqrt((X - center_x)**2 + (Y - center_y)**2)
        return dist_from_center <= current_valid_radius

    def get_valid_patch_coords(self, image, largest_patch_size):
        mask = self.get_circle_mask(image.shape)
        valid_indices = np.argwhere(mask)
        if len(valid_indices) == 0 or image.shape[0] < largest_patch_size or image.shape[1] < largest_patch_size:
            print(f"Warning: Image size {image.shape} < largest patch size {largest_patch_size} or no valid coords. Skipping.")
            return None, None
        attempts = 0
        max_attempts = 50
        while attempts < max_attempts:
            attempts += 1
            center_idx = random.choice(valid_indices)
            center_y, center_x = center_idx
            h_start = center_y - largest_patch_size // 2
            w_start = center_x - largest_patch_size // 2
            h_end = h_start + largest_patch_size
            w_end = w_start + largest_patch_size
            if (h_start >= 0 and w_start >= 0 and h_end <= image.shape[0] and w_end <= image.shape[1]):
                patch_mask = mask[h_start:h_end, w_start:w_end]
                if np.mean(patch_mask) >= 0.75:
                    return h_start, w_start
        print(f"Warning: Could not find suitable patch location after {max_attempts} attempts for image shape {image.shape} and patch size {largest_patch_size}. Skipping.")
        return None, None

    def __len__(self):
        return len(self.data_paths)

    def __getitem__(self, idx):
        img_path = self.data_paths[idx]
        img = self._load_image(img_path)
        if img is None: return None
        largest_patch_size = self.patch_sizes[0]
        h_start, w_start = self.get_valid_patch_coords(img, largest_patch_size)
        if h_start is None: return None
        largest_patch = img[h_start : h_start + largest_patch_size, w_start : w_start + largest_patch_size]
        multi_scale_views = {}
        center_crop_y = largest_patch_size // 2
        center_crop_x = largest_patch_size // 2
        for size in self.patch_sizes:
            offset = size // 2
            current_patch_np = largest_patch[center_crop_y - offset : center_crop_y + offset, center_crop_x - offset : center_crop_x + offset]
            if current_patch_np.shape != (size, size):
                 print(f"Warning: Cropped patch size mismatch for size {size}. Expected ({size},{size}), got {current_patch_np.shape}. Skipping sample.")
                 return None
            current_patch_pil = Image.fromarray((current_patch_np * 255).astype(np.uint8))
            view1 = self.transform1(current_patch_pil)
            view2 = self.transform2(current_patch_pil)
            view1 = view1.unsqueeze(1) # Add depth dimension D=1 -> [C, D, H, W]
            view2 = view2.unsqueeze(1) # Add depth dimension D=1 -> [C, D, H, W]
            expected_shape = (1, 1, size, size) # C=1, D=1
            if view1.shape != expected_shape or view2.shape != expected_shape:
                 print(f"Warning: View shape mismatch for size {size}. Expected {expected_shape}, got view1:{view1.shape}, view2:{view2.shape}. Skipping sample.")
                 return None
            multi_scale_views[size] = (view1, view2)
        return multi_scale_views


def get_enhanced_data_loader(lr_dir, hr_dir, batch_size=4, num_workers=2, temporal_window=3, patch_size=64):
    """获取增强的3D CT数据加载器 (Supervised)"""
    dataset = Enhanced3DCTDataset(
        lr_dir=lr_dir,
        hr_dir=hr_dir,
        temporal_window=temporal_window,
        patch_size=patch_size,
        scale=4,
        augment=True
    )
    loader = DataLoader(
        dataset,
        batch_size=batch_size,
        shuffle=True,
        num_workers=num_workers,
        pin_memory=True,
        collate_fn=filter_none
    )
    return loader

def get_self_supervised_loader(data_dir, batch_size=16, num_workers=2, patch_sizes=[32, 64, 128]):
    """获取自监督学习的数据加载器 (Multi-Scale)"""
    dataset = SelfSupervisedCTDataset(
        data_dir=data_dir,
        patch_sizes=patch_sizes,
        augment=True
    )
    loader = DataLoader(
        dataset,
        batch_size=batch_size,
        shuffle=True,
        num_workers=num_workers,
        pin_memory=True,
        drop_last=True,
        collate_fn=filter_none_dict
    )
    return loader
