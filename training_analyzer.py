import os
import torch
import glob
import argparse
import numpy as np
import matplotlib.pyplot as plt
import matplotlib.font_manager as fm
from datetime import datetime
from tensorboard.backend.event_processing.event_accumulator import EventAccumulator

class TrainingAnalyzer:
    """
    训练分析工具，整合了多种分析功能:
    1. 分析TensorBoard日志
    2. 比较不同训练阶段
    3. 绘制检查点损失曲线
    """
    
    def __init__(self, output_dir=None):
        # Set output directory
        if output_dir is None:
            self.output_dir = os.path.join("e:/vscode/非配位超分辨/plots", 
                                          f"analysis_{datetime.now().strftime('%Y%m%d_%H%M%S')}")
        else:
            self.output_dir = output_dir
            
        os.makedirs(self.output_dir, exist_ok=True)
        print(f"Analysis results will be saved to: {self.output_dir}")
    
    def _setup_fonts(self):
        """设置字体并检查是否使用英文标签"""
        # 设置Times New Roman字体
        plt.rcParams.update({
            'font.family': 'serif',
            'font.serif': ['Times New Roman'],
            'mathtext.fontset': 'stix',  # 数学字体与Times风格匹配
            'axes.unicode_minus': False,  # 确保负号正确显示
            'pdf.fonttype': 42,
            'ps.fonttype': 42
        })
        
        # 强制使用英文标签
        self.use_english_labels = True
        print("Using English labels with Times New Roman font")
    
    def _check_font_availability(self):
        """检查中文字体是否可用，如果不可用则使用英文标签"""
        # 获取所有可用字体
        available_fonts = set(f.name for f in fm.fontManager.ttflist)
        chinese_fonts = ['SimSun', 'SimHei', 'Microsoft YaHei', 'Arial Unicode MS', 'DengXian']
        
        # 检查是否有可用的中文字体
        found_chinese_font = False
        for font in chinese_fonts:
            if font in available_fonts:
                found_chinese_font = True
                plt.rcParams['font.sans-serif'] = [font] + plt.rcParams['font.sans-serif']
                print(f"使用中文字体: {font}")
                break
        
        if not found_chinese_font:
            print("警告: 未找到可用的中文字体，将使用英文标签")
            # 使用英文标签替换中文标签
            self.use_english_labels = True
        else:
            self.use_english_labels = False
    
    def analyze_tensorboard_logs(self, log_dir='logs/pretrain', mode='latest', smooth_factor=0.9):
        """分析TensorBoard日志文件"""
        # 获取日志文件
        event_files = [f for f in os.listdir(log_dir) if f.startswith('events.out.tfevents')]
        if not event_files:
            print(f"未在 {log_dir} 中找到任何训练日志文件")
            return
        
        if mode == 'latest':
            # 只分析最新的日志文件
            event_file_paths = [max([os.path.join(log_dir, f) for f in event_files], key=os.path.getmtime)]
            print(f"分析最新的训练日志: {event_file_paths[0]}")
        else:
            # 分析所有日志文件
            event_file_paths = [os.path.join(log_dir, f) for f in event_files]
            print(f"分析所有训练日志: {len(event_file_paths)} 个文件")
        
        # 分析每个日志文件
        all_stats = []
        for event_file in event_file_paths:
            stats = self._analyze_single_log(event_file, smooth_factor)
            if stats:
                file_time = os.path.getmtime(event_file)
                all_stats.append((file_time, stats))
        
        # 如果是分析所有日志，还可以生成训练趋势图
        if mode == 'all' and len(all_stats) > 1:
            self._generate_training_trend(all_stats)
        
        print(f"TensorBoard日志分析完成")
        return all_stats
    
    def _analyze_single_log(self, event_file, smooth_factor=0.9):
        """分析单个TensorBoard日志文件"""
        # 提取文件名用于输出
        file_name = os.path.basename(event_file).replace('events.out.tfevents.', '')
        
        # 读取TensorBoard日志
        event_acc = EventAccumulator(event_file)
        event_acc.Reload()

        # 获取可用的指标
        available_scalars = event_acc.Tags()['scalars']
        print(f"Available metrics: {available_scalars}")
        
        # 确定子图数量和布局
        n_metrics = len(available_scalars)
        
        # 优化布局：根据指标数量动态调整行列数
        if n_metrics <= 2:
            rows, cols = 1, n_metrics
        elif n_metrics <= 4:
            rows, cols = 2, 2
        elif n_metrics <= 6:
            rows, cols = 2, 3
        else:
            rows = (n_metrics + 2) // 3  # 每行最多3个图
            cols = 3
        
        # 设置 Nature 风格
        plt.style.use('seaborn-v0_8-whitegrid')
        plt.rcParams.update({
            'font.family': 'serif',
            'font.serif': ['Times New Roman'],
            'mathtext.fontset': 'stix',
            'axes.unicode_minus': False,
            'figure.figsize': (10, 6),  # 调整为更适合的比例
            'figure.dpi': 300,
            'axes.linewidth': 0.8,
            'axes.edgecolor': 'black',
            'grid.color': '#E5E5E5',
            'grid.linestyle': ':',
            'grid.linewidth': 0.3,
            'lines.linewidth': 1.2,
            'font.size': 9,
            'axes.titlesize': 10,
            'axes.labelsize': 9,
            'xtick.labelsize': 8,
            'ytick.labelsize': 8,
            'legend.fontsize': 8,
            'legend.frameon': True,
            'legend.edgecolor': 'none',
            'legend.facecolor': 'white',
            'legend.framealpha': 0.6,
        })
        
        # 创建图表
        fig = plt.figure(constrained_layout=False)
        gs = fig.add_gridspec(rows, cols, width_ratios=[1]*cols, wspace=0.25, hspace=0.35)
        
        for i, metric in enumerate(available_scalars):
            ax = fig.add_subplot(gs[i])
            
            # 提取数据
            steps_values = [(s.step, s.value) for s in event_acc.Scalars(metric)]
            if not steps_values:
                continue
                
            steps, values = zip(*steps_values)
            
            # 绘制原始数据和平滑曲线
            ax.plot(steps, values, color='#B2B2B2', alpha=0.3, linewidth=0.8,
                   label='Raw data', zorder=1)
            
            # 平滑处理
            smooth_values = np.array(values)
            for j in range(1, len(smooth_values)):
                smooth_values[j] = smooth_values[j-1] * smooth_factor + values[j] * (1 - smooth_factor)
            
            ax.plot(steps, smooth_values, color='#2171B5', linewidth=1.2, 
                   label='Smoothed', zorder=2)
            
            # 优化坐标轴
            ax.spines['top'].set_visible(False)
            ax.spines['right'].set_visible(False)
            ax.spines['left'].set_linewidth(0.8)
            ax.spines['bottom'].set_linewidth(0.8)
            
            # 设置标题和标签
            ax.set_title(metric, pad=8)
            ax.set_xlabel('Epoch', labelpad=5)
            ax.set_ylabel('Value', labelpad=5)
            
            # 优化刻度
            ax.tick_params(direction='out', length=3, width=0.8, pad=3)
            
            # 对学习率使用对数刻度
            if 'LR' in metric or 'lr' in metric.lower():
                ax.set_yscale('log')
                ax.grid(True, which='both', alpha=0.15)
            else:
                ax.grid(True, alpha=0.15)
                # 自动调整y轴范围
                ymin, ymax = ax.get_ylim()
                y_margin = (ymax - ymin) * 0.08
                ax.set_ylim(ymin - y_margin, ymax + y_margin)
            
            # 添加统计信息
            stats_text = (f'Final: {values[-1]:.6f}\n'
                         f'Best: {min(values) if "loss" in metric.lower() else max(values):.6f}\n'
                         f'Mean: {np.mean(values):.6f}\n'
                         f'Std: {np.std(values):.6f}')
            
            # 智能定位统计信息
            if 'LR' in metric or 'lr' in metric.lower():
                text_pos = 'upper right'
                text_x, text_y = 0.98, 0.98
                text_ha = 'right'
            else:
                text_pos = 'upper left'
                text_x, text_y = 0.02, 0.98
                text_ha = 'left'
            
            ax.text(text_x, text_y, stats_text,
                   transform=ax.transAxes,
                   fontsize=7,
                   family='monospace',
                   verticalalignment='top',
                   horizontalalignment=text_ha,
                   bbox=dict(boxstyle='round,pad=0.5',
                           facecolor='white',
                           edgecolor='none',
                           alpha=0.7))
            
            # 优化图例位置
            ax.legend(loc='lower right' if 'LR' in metric else 'lower left',
                     frameon=True,
                     framealpha=0.7,
                     edgecolor='none',
                     facecolor='white',
                     bbox_to_anchor=(0.98, 0.02) if 'LR' in metric else (0.02, 0.02))
        
        # 调整布局
        plt.tight_layout(pad=1.5)
        
        # 保存图表
        output_file = os.path.join(self.output_dir, f'analysis_{file_name}.png')
        plt.savefig(output_file, dpi=300, bbox_inches='tight',
                   facecolor='white', edgecolor='none')
        print(f"Analysis chart saved to {output_file}")
        
        plt.close(fig)
        
        # 返回关键指标的统计信息
        stats = {}
        for metric in available_scalars:
            steps_values = [(s.step, s.value) for s in event_acc.Scalars(metric)]
            if not steps_values:
                continue
            _, values = zip(*steps_values)
            stats[metric] = {
                'final': values[-1],
                'best': min(values) if "loss" in metric.lower() else max(values),
                'mean': np.mean(values),
                'std': np.std(values)
            }
        
        return stats
    
    def _generate_training_trend(self, all_stats):
        """生成多个训练日志的趋势图，比较不同训练运行的效果"""
        # 提取常见指标
        common_metrics = set()
        for _, stats in all_stats:
            common_metrics.update(stats.keys())
        
        # 为每个指标创建趋势图
        for metric in common_metrics:
            fig, ax = plt.subplots(figsize=(10, 6))
            
            # 收集该指标的所有数据
            times = []
            final_values = []
            best_values = []
            
            for file_time, stats in all_stats:
                if metric in stats:
                    times.append(datetime.fromtimestamp(file_time))
                    final_values.append(stats[metric]['final'])
                    best_values.append(stats[metric]['best'])
            
            if not times:
                continue
                
            # 绘制趋势
            ax.plot(times, final_values, 'b-o', label='最终值')
            ax.plot(times, best_values, 'r-o', label='最佳值')
            
            # 设置图表属性
            ax.set_title(f'{metric} 训练趋势')
            ax.set_xlabel('训练时间')
            ax.set_ylabel('值')
            ax.grid(True)
            ax.legend()
            
            # 旋转日期标签
            plt.xticks(rotation=45)
            
            # 保存图表
            plt.tight_layout()
            output_file = os.path.join(self.output_dir, f'trend_{metric}.png')
            plt.savefig(output_file, dpi=300, bbox_inches='tight')
            print(f"{metric} 趋势图已保存到 {output_file}")
            
            plt.close(fig)
    
    def compare_training_phases(self, pretrain_log_dir='logs/pretrain', enhanced_log_dir='logs/enhanced', smooth_factor=0.9):
        """比较预训练和增强训练阶段"""
        print(f"分析预训练日志: {pretrain_log_dir}")
        print(f"分析增强训练阶段: {enhanced_log_dir}")
        
        # 读取预训练日志
        pretrain_event_files = [f for f in os.listdir(pretrain_log_dir) if f.startswith('events.out.tfevents')]
        if not pretrain_event_files:
            print(f"未在 {pretrain_log_dir} 中找到任何训练日志文件")
            return
        
        pretrain_event_file = max([os.path.join(pretrain_log_dir, f) for f in pretrain_event_files], key=os.path.getmtime)
        pretrain_acc = EventAccumulator(pretrain_event_file)
        pretrain_acc.Reload()
        print(f"预训练可用指标: {pretrain_acc.Tags()['scalars']}")
        
        # 读取增强训练日志
        enhanced_event_files = [f for f in os.listdir(enhanced_log_dir) if f.startswith('events.out.tfevents')]
        if not enhanced_event_files:
            print(f"未在 {enhanced_log_dir} 中找到任何训练日志文件")
            return
        
        enhanced_event_file = max([os.path.join(enhanced_log_dir, f) for f in enhanced_event_files], key=os.path.getmtime)
        enhanced_acc = EventAccumulator(enhanced_event_file)
        enhanced_acc.Reload()
        print(f"增强训练可用指标: {enhanced_acc.Tags()['scalars']}")
        
        # 比较损失曲线
        pretrain_loss_tag = 'Loss/train'
        enhanced_loss_tag = 'Loss/train'
        self._compare_loss_curves(pretrain_acc, enhanced_acc, smooth_factor)
        
        # 比较学习率曲线
        if 'LR' in pretrain_acc.Tags()['scalars'] and 'LR' in enhanced_acc.Tags()['scalars']:
            self._compare_lr_curves(pretrain_acc, enhanced_acc, smooth_factor)
        
        # 生成比较报告
        self._generate_comparison_report(pretrain_acc, enhanced_acc)
        
        print("比较分析完成")
    
    def _compare_loss_curves(self, pretrain_acc, enhanced_acc, smooth_factor=0.9):
        """比较预训练和增强训练的损失曲线"""
        pretrain_loss_tag = 'Loss/train'
        enhanced_loss_tag = 'Loss/train'
        
        # 设置 Times New Roman 字体
        plt.style.use('seaborn-v0_8-whitegrid')
        plt.rcParams.update({
            'font.family': 'serif',
            'font.serif': ['Times New Roman'],
            'mathtext.fontset': 'stix',
            'axes.unicode_minus': False,
            'figure.figsize': (12, 5),
            'figure.dpi': 300,
            'axes.linewidth': 0.8,
            'axes.edgecolor': 'black',
            'grid.color': '#E5E5E5',
            'grid.linestyle': ':',
            'grid.linewidth': 0.3,
            'lines.linewidth': 1.2,
            'font.size': 9,
            'axes.titlesize': 10,
            'axes.labelsize': 9,
            'xtick.labelsize': 8,
            'ytick.labelsize': 8,
            'legend.fontsize': 8,
            'legend.frameon': True,
            'legend.edgecolor': 'none',
            'legend.facecolor': 'white',
            'legend.framealpha': 0.6,
        })

        # 创建图表
        fig = plt.figure(constrained_layout=False)
        ax = fig.add_subplot(111)
        
        # 绘制预训练损失
        if pretrain_loss_tag in pretrain_acc.Tags()['scalars']:
            pretrain_loss = [(s.step, s.value) for s in pretrain_acc.Scalars(pretrain_loss_tag)]
            if pretrain_loss:
                steps, values = zip(*pretrain_loss)
                ax.plot(steps, values, color='#B2B2B2', alpha=0.3, linewidth=0.8,
                       label='Pretrain (raw)', zorder=1)
                
                # 平滑处理
                smooth_values = np.array(values)
                for i in range(1, len(smooth_values)):
                    smooth_values[i] = smooth_values[i-1] * smooth_factor + values[i] * (1 - smooth_factor)
                ax.plot(steps, smooth_values, color='#2171B5', linewidth=1.2,
                       label='Pretrain (smoothed)', zorder=2)
        
        # 绘制增强训练损失
        if enhanced_loss_tag in enhanced_acc.Tags()['scalars']:
            enhanced_loss = [(s.step, s.value) for s in enhanced_acc.Scalars(enhanced_loss_tag)]
            if enhanced_loss:
                steps, values = zip(*enhanced_loss)
                ax.plot(steps, values, color='#B2B2B2', alpha=0.3, linewidth=0.8,
                       label='Enhanced (raw)', zorder=1)
                
                # 平滑处理
                smooth_values = np.array(values)
                for i in range(1, len(smooth_values)):
                    smooth_values[i] = smooth_values[i-1] * smooth_factor + values[i] * (1 - smooth_factor)
                ax.plot(steps, smooth_values, color='#E31A1C', linewidth=1.2,
                       label='Enhanced (smoothed)', zorder=2)
        
        # 优化坐标轴
        ax.spines['top'].set_visible(False)
        ax.spines['right'].set_visible(False)
        ax.spines['left'].set_linewidth(0.8)
        ax.spines['bottom'].set_linewidth(0.8)
        
        # 设置标题和标签 - 全部使用英文
        ax.set_title('Training Loss Comparison', fontname='Times New Roman', pad=8)
        ax.set_xlabel('Epoch', fontname='Times New Roman', labelpad=5)
        ax.set_ylabel('Loss', fontname='Times New Roman', labelpad=5)
        
        # 优化刻度
        ax.tick_params(direction='out', length=3, width=0.8, pad=3)
        ax.grid(True, alpha=0.15)
        
        # 自动调整y轴范围
        ymin, ymax = ax.get_ylim()
        y_margin = (ymax - ymin) * 0.08
        ax.set_ylim(ymin - y_margin, ymax + y_margin)
        
        # 将图例放在图表内部右下角
        ax.legend(loc='lower right',
                 bbox_to_anchor=(0.95, 0.05),  # 相对于图表区域的位置
                 bbox_transform=ax.transAxes,   # 使用轴变换
                 frameon=True,
                 framealpha=0.7,
                 edgecolor='none',
                 facecolor='white',
                 prop={'family': 'Times New Roman'},
                 borderpad=0.8,                 # 调整内边距
                 handletextpad=0.5)            # 调整图例标记和文本之间的间距
        
        # 调整布局，确保所有元素都在图表范围内
        plt.tight_layout(pad=1.5)
        
        # 设置标题和标签 - 全部使用英文
        ax.set_title('Training Loss Comparison', fontname='Times New Roman', pad=8)
        ax.set_xlabel('Epoch', fontname='Times New Roman', labelpad=5)
        ax.set_ylabel('Loss', fontname='Times New Roman', labelpad=5)
        
        # 优化刻度
        ax.tick_params(direction='out', length=3, width=0.8, pad=3)
        ax.grid(True, alpha=0.15)
        
        # 自动调整y轴范围
        ymin, ymax = ax.get_ylim()
        y_margin = (ymax - ymin) * 0.08
        ax.set_ylim(ymin - y_margin, ymax + y_margin)
        
        # 优化图例位置
        ax.legend(loc='upper right',
                 frameon=True,
                 framealpha=0.7,
                 edgecolor='none',
                 facecolor='white',
                 ncol=2,
                 prop={'family': 'Times New Roman'})
        
        # 调整布局
        plt.tight_layout(pad=1.5)
        
        # 保存图表 - 使用英文文件名
        loss_comparison_file = os.path.join(self.output_dir, 'loss_comparison.png')
        plt.savefig(loss_comparison_file, dpi=300, bbox_inches='tight',
                   facecolor='white', edgecolor='none')
        print(f"损失曲线比较图已保存到 {loss_comparison_file}")
        plt.close(fig)
    
    def _compare_lr_curves(self, pretrain_acc, enhanced_acc, smooth_factor=0.9):
        """比较预训练和增强训练的学习率变化"""
        fig, ax = plt.subplots(figsize=(12, 8))
        
        # 提取预训练学习率
        if 'LR' in pretrain_acc.Tags()['scalars']:
            pretrain_lr = [(s.step, s.value) for s in pretrain_acc.Scalars('LR')]
            if pretrain_lr:
                steps, values = zip(*pretrain_lr)
                ax.plot(steps, values, 'b-', label='预训练学习率')
        
        # 提取增强训练学习率
        if 'LR' in enhanced_acc.Tags()['scalars']:
            enhanced_lr = [(s.step, s.value) for s in enhanced_acc.Scalars('LR')]
            if enhanced_lr:
                steps, values = zip(*enhanced_lr)
                ax.plot(steps, values, 'r-', label='增强训练学习率')
        
        # 设置图表属性
        ax.set_title('预训练与增强训练学习率比较')
        ax.set_xlabel('Epoch')
        ax.set_ylabel('Learning Rate')
        ax.set_yscale('log')  # 使用对数刻度
        ax.grid(True)
        ax.legend()
        
        # 保存图表
        output_file = os.path.join(self.output_dir, 'learning_rate_comparison.png')
        plt.savefig(output_file, dpi=300, bbox_inches='tight')
        print(f"学习率比较图已保存到 {output_file}")
        plt.close(fig)
    
    def _analyze_gradient_norms(self, enhanced_acc, smooth_factor=0.9):
        """分析梯度范数变化"""
        fig, ax = plt.subplots(figsize=(12, 8))
        
        # 提取梯度范数
        grad_norms = [(s.step, s.value) for s in enhanced_acc.Scalars('Gradients/norm')]
        if not grad_norms:
            print("未找到梯度范数数据")
            return
        
        steps, values = zip(*grad_norms)
        
        # 绘制原始数据
        ax.plot(steps, values, 'g-', alpha=0.3, label='原始梯度范数')
        
        # 平滑处理
        smooth_values = np.array(values)
        for i in range(1, len(smooth_values)):
            smooth_values[i] = smooth_values[i-1] * smooth_factor + values[i] * (1 - smooth_factor)
        
        ax.plot(steps, smooth_values, 'g-', label='平滑梯度范数')
        
        # 设置图表属性
        ax.set_title('训练过程中的梯度范数变化')
        ax.set_xlabel('Batch')
        ax.set_ylabel('Gradient Norm')
        ax.grid(True)
        ax.legend()
        
        # 添加统计信息
        stats_text = f'梯度范数统计:\n'
        stats_text += f'最大值: {max(values):.6f}\n'
        stats_text += f'最小值: {min(values):.6f}\n'
        stats_text += f'平均值: {np.mean(values):.6f}\n'
        stats_text += f'标准差: {np.std(values):.6f}'
        
        ax.text(0.02, 0.02, stats_text, transform=ax.transAxes, fontsize=10, 
                family='monospace', verticalalignment='bottom', 
                bbox=dict(boxstyle='round', facecolor='wheat', alpha=0.5))
        
        # 保存图表
        output_file = os.path.join(self.output_dir, 'gradient_norm_analysis.png')
        plt.savefig(output_file, dpi=300, bbox_inches='tight')
        print(f"梯度范数分析图已保存到 {output_file}")
        plt.close(fig)
    
    def _generate_comparison_report(self, pretrain_acc, enhanced_acc):
        """Generate training phase comparison report"""
        report_file = os.path.join(self.output_dir, 'training_report.md')
        
        with open(report_file, 'w', encoding='utf-8') as f:
            # Report header
            f.write("# Training Analysis Report\n\n")
            f.write(f"Generated: {datetime.now().strftime('%Y-%m-%d %H:%M:%S')}\n\n")
            
            # Pretraining phase analysis
            f.write("## 1. Pretraining Phase Analysis\n\n")
            pretrain_loss_tag = 'Loss/train'
            if pretrain_loss_tag in pretrain_acc.Tags()['scalars']:
                pretrain_loss = [s.value for s in pretrain_acc.Scalars(pretrain_loss_tag)]
                if pretrain_loss:
                    f.write("### 1.1 Loss Statistics\n\n")
                    f.write(f"- Initial Loss: {pretrain_loss[0]:.6f}\n")
                    f.write(f"- Final Loss: {pretrain_loss[-1]:.6f}\n")
                    f.write(f"- Best Loss: {min(pretrain_loss):.6f}\n")
                    f.write(f"- Average Loss: {np.mean(pretrain_loss):.6f}\n")
                    f.write(f"- Loss Std: {np.std(pretrain_loss):.6f}\n")
                    f.write(f"- Loss Reduction: {(1 - pretrain_loss[-1]/pretrain_loss[0])*100:.2f}%\n\n")
            
            if 'LR' in pretrain_acc.Tags()['scalars']:
                pretrain_lr = [s.value for s in pretrain_acc.Scalars('LR')]
                if pretrain_lr:
                    f.write("### 1.2 Learning Rate\n\n")
                    f.write(f"- Initial LR: {pretrain_lr[0]:.6f}\n")
                    f.write(f"- Final LR: {pretrain_lr[-1]:.6f}\n")
                    f.write(f"- LR Range: [{min(pretrain_lr):.6f}, {max(pretrain_lr):.6f}]\n\n")
            
            # Enhanced training phase analysis
            f.write("## 2. Enhanced Training Phase Analysis\n\n")
            enhanced_loss_tag = 'Loss/train'
            if enhanced_loss_tag in enhanced_acc.Tags()['scalars']:
                enhanced_loss = [s.value for s in enhanced_acc.Scalars(enhanced_loss_tag)]
                if enhanced_loss:
                    f.write("### 2.1 Loss Statistics\n\n")
                    f.write(f"- Initial Loss: {enhanced_loss[0]:.6f}\n")
                    f.write(f"- Final Loss: {enhanced_loss[-1]:.6f}\n")
                    f.write(f"- Best Loss: {min(enhanced_loss):.6f}\n")
                    f.write(f"- Average Loss: {np.mean(enhanced_loss):.6f}\n")
                    f.write(f"- Loss Std: {np.std(enhanced_loss):.6f}\n")
                    f.write(f"- Loss Reduction: {(1 - enhanced_loss[-1]/enhanced_loss[0])*100:.2f}%\n\n")
            
            # Training phase comparison
            f.write("## 3. Phase Comparison\n\n")
            if pretrain_loss_tag in pretrain_acc.Tags()['scalars'] and enhanced_loss_tag in enhanced_acc.Tags()['scalars']:
                pretrain_loss = [s.value for s in pretrain_acc.Scalars(pretrain_loss_tag)]
                enhanced_loss = [s.value for s in enhanced_acc.Scalars(enhanced_loss_tag)]
                if pretrain_loss and enhanced_loss:
                    f.write("### 3.1 Loss Comparison\n\n")
                    f.write("| Metric | Pretraining | Enhanced |\n")
                    f.write("| --- | --- | --- |\n")
                    f.write(f"| Initial Loss | {pretrain_loss[0]:.6f} | {enhanced_loss[0]:.6f} |\n")
                    f.write(f"| Final Loss | {pretrain_loss[-1]:.6f} | {enhanced_loss[-1]:.6f} |\n")
                    f.write(f"| Best Loss | {min(pretrain_loss):.6f} | {min(enhanced_loss):.6f} |\n")
                    f.write(f"| Average Loss | {np.mean(pretrain_loss):.6f} | {np.mean(enhanced_loss):.6f} |\n")
                    f.write(f"| Loss Reduction | {(1 - pretrain_loss[-1]/pretrain_loss[0])*100:.2f}% | {(1 - enhanced_loss[-1]/enhanced_loss[0])*100:.2f}% |\n\n")
            
            # Conclusions and recommendations
            f.write("## 4. Conclusions\n\n")
            f.write("### 4.1 Training Analysis\n\n")
            
            if pretrain_loss_tag in pretrain_acc.Tags()['scalars']:
                pretrain_loss = [s.value for s in pretrain_acc.Scalars(pretrain_loss_tag)]
                if pretrain_loss:
                    if pretrain_loss[-1] < 0.5 * pretrain_loss[0]:
                        f.write("- Pretraining phase shows significant loss reduction\n")
                    else:
                        f.write("- Pretraining phase loss reduction is limited\n")
            
            if enhanced_loss_tag in enhanced_acc.Tags()['scalars']:
                enhanced_loss = [s.value for s in enhanced_acc.Scalars(enhanced_loss_tag)]
                if enhanced_loss:
                    if enhanced_loss[-1] < 0.5 * enhanced_loss[0]:
                        f.write("- Enhanced training phase shows significant improvement\n")
                    else:
                        f.write("- Enhanced training phase shows limited improvement\n")
            
            f.write("\n### 4.2 Recommendations\n\n")
            f.write("- Consider using more complex loss functions\n")
            f.write("- Try different learning rate schedules\n")
            f.write("- Increase data augmentation\n")
            f.write("- Consider deeper network architectures\n")
        
        print(f"Training report saved to {report_file}")
    
    def plot_checkpoint_loss(self, checkpoint_dir='checkpoints/pretrain'):
        """Plot checkpoint loss curve"""
        # Collect all checkpoint files
        checkpoint_files = glob.glob(os.path.join(checkpoint_dir, "checkpoint_epoch*.pth"))
        checkpoint_files.sort(key=lambda x: int(os.path.basename(x).split("epoch")[1].split(".")[0]))
        
        if not checkpoint_files:
            print(f"No checkpoint files found in {checkpoint_dir}")
            return
        
        # Load final model
        final_model_path = os.path.join(checkpoint_dir, "final_model.pth")
        if os.path.exists(final_model_path):
            final_checkpoint = torch.load(final_model_path, map_location='cpu')
            final_epoch = final_checkpoint.get('epoch', 0)
            final_loss = final_checkpoint.get('loss', 0)
        else:
            final_epoch = None
            final_loss = None
        
        # Load best model
        best_model_path = os.path.join(checkpoint_dir, "best_model.pth")
        if os.path.exists(best_model_path):
            best_checkpoint = torch.load(best_model_path, map_location='cpu')
            best_epoch = best_checkpoint.get('epoch', 0)
            best_loss = best_checkpoint.get('loss', 0)
        else:
            best_epoch = None
            best_loss = None
        
        # Collect loss values from all checkpoints
        epochs = []
        losses = []
        
        for file in checkpoint_files:
            checkpoint = torch.load(file, map_location='cpu')
            epoch = checkpoint.get('epoch', 0)
            loss = checkpoint.get('loss', 0)
            epochs.append(epoch)
            losses.append(loss)
        
        # Add final model if not in checkpoint list
        if final_epoch is not None and final_epoch not in epochs:
            epochs.append(final_epoch)
            losses.append(final_loss)
        
        # 设置 Nature 风格
        plt.style.use('seaborn-v0_8-whitegrid')
        plt.rcParams.update({
            'font.family': 'serif',
            'font.serif': ['Times New Roman'],
            'mathtext.fontset': 'stix',
            'axes.unicode_minus': False,
            'figure.figsize': (10, 6),  # 调整为更适合的比例
            'figure.dpi': 300,
            'axes.linewidth': 0.8,
            'axes.edgecolor': 'black',
            'grid.color': '#E5E5E5',
            'grid.linestyle': ':',
            'grid.linewidth': 0.3,
            'lines.linewidth': 1.2,
            'font.size': 9,
            'axes.titlesize': 10,
            'axes.labelsize': 9,
            'xtick.labelsize': 8,
            'ytick.labelsize': 8,
            'legend.fontsize': 8,
            'legend.frameon': True,
            'legend.edgecolor': 'none',
            'legend.facecolor': 'white',
            'legend.framealpha': 0.6,
        })
        
        # 创建图表
        fig = plt.figure(constrained_layout=False)
        ax = fig.add_subplot(111)
        
        # 绘制损失曲线
        ax.plot(epochs, losses, color='#B2B2B2', alpha=0.3, linewidth=0.8,
               label='Raw data', zorder=1)
        
        # 平滑处理
        smooth_losses = np.array(losses)
        for i in range(1, len(smooth_losses)):
            smooth_losses[i] = smooth_losses[i-1] * 0.9 + losses[i] * 0.1
        
        ax.plot(epochs, smooth_losses, color='#2171B5', linewidth=1.2,
               label='Smoothed', zorder=2)
        
        # 标记最佳模型和最终模型
        best_marker = None
        if best_epoch is not None and best_loss is not None:
            best_marker = ax.plot(best_epoch, best_loss, 'r*', markersize=8, label='Best model', zorder=3)[0]
        
        if final_epoch is not None and final_loss is not None:
            ax.plot(final_epoch, final_loss, 'g*', markersize=8, label='Final model', zorder=3)
        
        # 优化坐标轴
        ax.spines['top'].set_visible(False)
        ax.spines['right'].set_visible(False)
        ax.spines['left'].set_linewidth(0.8)
        ax.spines['bottom'].set_linewidth(0.8)
        
        # 设置标题和标签
        ax.set_title('Checkpoint Loss Curve', fontname='Times New Roman', pad=8)
        ax.set_xlabel('Epoch', fontname='Times New Roman', labelpad=5)
        ax.set_ylabel('Loss', fontname='Times New Roman', labelpad=5)
        
        # 优化刻度
        ax.tick_params(direction='out', length=3, width=0.8, pad=3)
        ax.grid(True, alpha=0.15)
        
        # 自动调整y轴范围，确保数据不被遮挡
        ymin, ymax = ax.get_ylim()
        y_margin = (ymax - ymin) * 0.1
        ax.set_ylim(max(0, ymin - y_margin), ymax + y_margin)
        
        # 添加统计信息，放在右上角以避免遮挡数据
        stats_text = (f'Final: {losses[-1]:.6f}\n'
                     f'Best: {min(losses):.6f}\n'
                     f'Mean: {np.mean(losses):.6f}\n'
                     f'Std: {np.std(losses):.6f}')
        
        # 智能定位统计信息框，避免遮挡数据点
        # 检查数据趋势决定文本位置
        if losses[0] > losses[-1]:  # 损失下降趋势
            text_x, text_y = 0.98, 0.98
            text_ha = 'right'
        else:  # 损失上升或波动
            text_x, text_y = 0.02, 0.98
            text_ha = 'left'
        
        ax.text(text_x, text_y, stats_text,
               transform=ax.transAxes,
               fontsize=7,
               family='Times New Roman',
               verticalalignment='top',
               horizontalalignment=text_ha,
               bbox=dict(boxstyle='round,pad=0.5',
                       facecolor='white',
                       edgecolor='none',
                       alpha=0.7))
        
        # 优化图例位置，放在左下角避免遮挡数据
        # 创建图表
        fig = plt.figure(constrained_layout=False)
        ax = fig.add_subplot(111)
        
        # 绘制损失曲线
        ax.plot(epochs, losses, color='#B2B2B2', alpha=0.3, linewidth=0.8,
               label='Raw data', zorder=1)
        
        # 平滑处理
        smooth_losses = np.array(losses)
        for i in range(1, len(smooth_losses)):
            smooth_losses[i] = smooth_losses[i-1] * 0.9 + losses[i] * 0.1
        
        ax.plot(epochs, smooth_losses, color='#2171B5', linewidth=1.2,
               label='Smoothed', zorder=2)
        
        # 标记最佳模型和最终模型
        best_marker = None
        if best_epoch is not None and best_loss is not None:
            best_marker = ax.plot(best_epoch, best_loss, 'r*', markersize=8, label='Best model', zorder=3)[0]
        
        if final_epoch is not None and final_loss is not None:
            ax.plot(final_epoch, final_loss, 'g*', markersize=8, label='Final model', zorder=3)
        
        # 优化坐标轴
        ax.spines['top'].set_visible(False)
        ax.spines['right'].set_visible(False)
        ax.spines['left'].set_linewidth(0.8)
        ax.spines['bottom'].set_linewidth(0.8)
        
        # 设置标题和标签
        ax.set_title('Checkpoint Loss Curve', fontname='Times New Roman', pad=8)
        ax.set_xlabel('Epoch', fontname='Times New Roman', labelpad=5)
        ax.set_ylabel('Loss', fontname='Times New Roman', labelpad=5)
        
        # 优化刻度
        ax.tick_params(direction='out', length=3, width=0.8, pad=3)
        ax.grid(True, alpha=0.15)
        
        # 自动调整y轴范围，确保数据不被遮挡
        ymin, ymax = ax.get_ylim()
        y_margin = (ymax - ymin) * 0.1
        ax.set_ylim(max(0, ymin - y_margin), ymax + y_margin)
        
        # 添加统计信息，放在右上角以避免遮挡数据
        stats_text = (f'Final: {losses[-1]:.6f}\n'
                     f'Best: {min(losses):.6f}\n'
                     f'Mean: {np.mean(losses):.6f}\n'
                     f'Std: {np.std(losses):.6f}')
        
        # 智能定位统计信息框，避免遮挡数据点
        # 检查数据趋势决定文本位置
        if losses[0] > losses[-1]:  # 损失下降趋势
            text_x, text_y = 0.98, 0.98
            text_ha = 'right'
        else:  # 损失上升或波动
            text_x, text_y = 0.02, 0.98
            text_ha = 'left'
        
        ax.text(text_x, text_y, stats_text,
               transform=ax.transAxes,
               fontsize=7,
               family='Times New Roman',
               verticalalignment='top',
               horizontalalignment=text_ha,
               bbox=dict(boxstyle='round,pad=0.5',
                       facecolor='white',
                       edgecolor='none',
                       alpha=0.7))
        
        # 将图例放在右侧，避免遮挡数据点
        ax.legend(loc='center right',
                 frameon=True,
                 framealpha=0.7,
                 edgecolor='none',
                 facecolor='white',
                 prop={'family': 'Times New Roman'})
        
        # 调整布局，为右侧图例留出空间
        plt.tight_layout(pad=1.5, rect=[0, 0, 0.9, 1])  # 右侧留出10%的空间给图例
        
        # 保存图表
        output_file = os.path.join(self.output_dir, 'checkpoint_loss_curve.png')
        plt.savefig(output_file, dpi=300, bbox_inches='tight',
                   facecolor='white', edgecolor='none')
        print(f"Checkpoint loss curve saved to {output_file}")
        
        plt.close(fig)


def main():
    """命令行接口"""
    parser = argparse.ArgumentParser(description="CT图像超分辨率训练分析工具")
    
    # 通用参数
    parser.add_argument("--output_dir", type=str, default=None,
                        help="分析结果输出目录")
    
    # 子命令
    subparsers = parser.add_subparsers(dest="command", help="分析命令")
    
    # 1. 分析TensorBoard日志
    analyze_parser = subparsers.add_parser("analyze", help="分析TensorBoard日志")
    analyze_parser.add_argument("--log_dir", type=str, default="logs/pretrain",
                              help="TensorBoard日志目录")
    analyze_parser.add_argument("--mode", type=str, default="latest", choices=["latest", "all"],
                              help="分析模式: 'latest'仅分析最新日志, 'all'分析所有日志")
    analyze_parser.add_argument("--smooth", type=float, default=0.9,
                              help="平滑因子 (0-1)")
    
    # 2. 比较训练阶段
    compare_parser = subparsers.add_parser("compare", help="比较预训练和增强训练阶段")
    compare_parser.add_argument("--pretrain_dir", type=str, default="logs/pretrain",
                              help="预训练日志目录")
    compare_parser.add_argument("--enhanced_dir", type=str, default="logs/enhanced",
                              help="增强训练日志目录")
    compare_parser.add_argument("--smooth", type=float, default=0.9,
                              help="平滑因子 (0-1)")
    
    # 3. 绘制检查点损失曲线
    plot_parser = subparsers.add_parser("plot", help="绘制检查点损失曲线")
    plot_parser.add_argument("--checkpoint_dir", type=str, default="checkpoints/pretrain",
                           help="检查点目录")
    
    # 4. 全部分析
    all_parser = subparsers.add_parser("all", help="执行所有分析")
    all_parser.add_argument("--pretrain_log", type=str, default="logs/pretrain",
                          help="预训练日志目录")
    all_parser.add_argument("--enhanced_log", type=str, default="logs/enhanced",
                          help="增强训练日志目录")
    all_parser.add_argument("--pretrain_checkpoint", type=str, default="checkpoints/pretrain",
                          help="预训练检查点目录")
    all_parser.add_argument("--enhanced_checkpoint", type=str, default="checkpoints/enhanced",
                          help="增强训练检查点目录")
    all_parser.add_argument("--smooth", type=float, default=0.9,
                          help="平滑因子 (0-1)")
    
    args = parser.parse_args()
    
    # 创建分析器
    analyzer = TrainingAnalyzer(output_dir=args.output_dir)
    
    # 执行命令
    if args.command == "analyze":
        analyzer.analyze_tensorboard_logs(
            log_dir=args.log_dir,
            mode=args.mode,
            smooth_factor=args.smooth
        )
    elif args.command == "compare":
        analyzer.compare_training_phases(
            pretrain_log_dir=args.pretrain_dir,
            enhanced_log_dir=args.enhanced_dir,
            smooth_factor=args.smooth
        )
    elif args.command == "plot":
        analyzer.plot_checkpoint_loss(
            checkpoint_dir=args.checkpoint_dir
        )
    elif args.command == "all":
        # 分析预训练日志
        analyzer.analyze_tensorboard_logs(
            log_dir=args.pretrain_log,
            mode="latest",
            smooth_factor=args.smooth
        )
        
        # 分析增强训练日志
        analyzer.analyze_tensorboard_logs(
            log_dir=args.enhanced_log,
            mode="latest",
            smooth_factor=args.smooth
        )
        
        # 比较训练阶段
        analyzer.compare_training_phases(
            pretrain_log_dir=args.pretrain_log,
            enhanced_log_dir=args.enhanced_log,
            smooth_factor=args.smooth
        )
        
        # 绘制预训练检查点损失曲线
        analyzer.plot_checkpoint_loss(
            checkpoint_dir=args.pretrain_checkpoint
        )
        
        # 绘制增强训练检查点损失曲线
        analyzer.plot_checkpoint_loss(
            checkpoint_dir=args.enhanced_checkpoint
        )
    else:
        parser.print_help()


if __name__ == "__main__":
    main()