#!/bin/bash
# <PERSON><PERSON><PERSON> to start supervised super-resolution training
# This script launches the conditional diffusion model training for CT image super-resolution

echo "🎯 Starting Supervised Super-Resolution Training"
echo "================================================"
echo "Using best pretrained model: PSNR=21.51, SSIM=0.626"
echo "Target: Super-resolution with 2-3dB PSNR improvement"
echo "================================================"

# Activate Python environment
echo "Activating Python environment..."
# Uncomment the line below if you need to activate a specific environment
# source activate pytorchEnv

# Check GPU availability
echo "Checking GPU availability..."
python -c "import torch; print(f'CUDA available: {torch.cuda.is_available()}'); print(f'GPU count: {torch.cuda.device_count()}'); print(f'Current device: {torch.cuda.current_device() if torch.cuda.is_available() else \"CPU\"}')"

# Verify data paths exist
echo "Verifying data paths..."
if [ ! -d "data/2号CT数据" ]; then
    echo "❌ Error: data/2号CT数据 directory not found!"
    echo "Please ensure the low-resolution CT data is in the correct location."
    exit 1
fi

if [ ! -d "data/3号CT数据" ]; then
    echo "❌ Error: data/3号CT数据 directory not found!"
    echo "Please ensure the high-resolution CT data is in the correct location."
    exit 1
fi

# Verify pretrained model exists
echo "Verifying pretrained model..."
if [ ! -f "checkpoints/swin_mae_hierarchical_random_ssim_nce_w002/best_model.pth" ]; then
    echo "❌ Error: Pretrained model not found!"
    echo "Expected: checkpoints/swin_mae_hierarchical_random_ssim_nce_w002/best_model.pth"
    echo "Please ensure the pretrained model is available."
    exit 1
fi

echo "✅ All prerequisites verified!"

# Create necessary directories
echo "Creating directories..."
mkdir -p "logs/sr_diffusion"
mkdir -p "checkpoints/sr_diffusion"

# Change to the ultra directory
cd "Self-supervised training network ultra"

# Start supervised training
echo "Starting supervised super-resolution training..."
python train_sr_ultra.py \
    --config configs/config_ultra.yaml \
    --tag SwinMAE_Hierarchical_DiffusionSR_v1

echo ""
echo "🎉 Training launched successfully!"
echo "================================================"
echo "Monitor training progress:"
echo "- Logs: logs/sr_diffusion/SwinMAE_Hierarchical_DiffusionSR_v1/"
echo "- Checkpoints: checkpoints/sr_diffusion/SwinMAE_Hierarchical_DiffusionSR_v1/"
echo "- TensorBoard: tensorboard --logdir logs/sr_diffusion/SwinMAE_Hierarchical_DiffusionSR_v1"
echo ""
echo "Expected training time: Several hours to days depending on GPU"
echo "Monitor PSNR and SSIM metrics for super-resolution quality"
echo "================================================"
