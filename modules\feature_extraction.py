import torch
import torch.nn as nn
import torch.nn.functional as F
from modules.attention import DualAttention

class FeaturePyramid(nn.Module):
    """多尺度特征金字塔，支持体素大小自适应"""
    def __init__(self, in_channels, out_channels, voxel_size=0.03889):
        super().__init__()
        self.voxel_size = voxel_size
        # 根据体素大小动态调整卷积核大小
        k2 = max(3, int(3 * (0.03889 / voxel_size)))
        k3 = max(5, int(5 * (0.03889 / voxel_size)))
        k4 = max(7, int(7 * (0.03889 / voxel_size)))
        
        self.conv1 = nn.Conv2d(in_channels, out_channels, kernel_size=1)
        self.conv2 = nn.Conv2d(in_channels, out_channels, kernel_size=k2, padding=k2//2)
        self.conv3 = nn.Conv2d(in_channels, out_channels, kernel_size=k3, padding=k3//2)
        self.conv4 = nn.Conv2d(in_channels, out_channels, kernel_size=k4, padding=k4//2)
        self.fusion = nn.Conv2d(out_channels * 4, out_channels, kernel_size=1)

    def forward(self, x):
        x1 = self.conv1(x)
        x2 = self.conv2(x)
        x3 = self.conv3(x)
        x4 = self.conv4(x)
        
        # 上采样到相同大小
        x2 = F.interpolate(x2, size=x1.shape[2:], mode='bilinear', align_corners=False)
        x3 = F.interpolate(x3, size=x1.shape[2:], mode='bilinear', align_corners=False)
        x4 = F.interpolate(x4, size=x1.shape[2:], mode='bilinear', align_corners=False)
        
        # 特征融合
        return self.fusion(torch.cat([x1, x2, x3, x4], dim=1))

class GlobalContext(nn.Module):
    """全局上下文模块"""
    def __init__(self, channels, reduction=16):
        super().__init__()
        self.avg_pool = nn.AdaptiveAvgPool2d(1)
        self.fc = nn.Sequential(
            nn.Linear(channels, channels // reduction, bias=False),
            nn.ReLU(inplace=True),
            nn.Linear(channels // reduction, channels, bias=False),
            nn.Sigmoid()
        )

    def forward(self, x):
        b, c, _, _ = x.size()
        y = self.avg_pool(x).view(b, c)
        y = self.fc(y).view(b, c, 1, 1)
        return x * y

class LocalDetailEnhancement(nn.Module):
    """局部细节增强"""
    def __init__(self, channels):
        super().__init__()
        self.conv1 = nn.Conv2d(channels, channels, kernel_size=3, padding=1)
        self.conv2 = nn.Conv2d(channels, channels, kernel_size=3, padding=1)
        self.attention = DualAttention(channels)

    def forward(self, x):
        y = F.relu(self.conv1(x))
        y = self.conv2(y)
        y = self.attention(y)
        return x + y
