import os
import argparse
import torch
from train_enhanced import train_enhanced_sr, pretrain_self_supervised

def main():
    parser = argparse.ArgumentParser(description="Enhanced Super-Resolution Training for Non-registered CT Data")
    # 训练模式设置
    parser.add_argument("--mode", type=str, default="train", choices=["train", "pretrain", "validate"], 
                        help="Training mode: 'train' for full model, 'pretrain' for self-supervised, 'validate' for evaluation")
    
    # 模型参数
    parser.add_argument("--model_dim", type=int, default=64,
                        help="Dimension of model features")
    parser.add_argument("--model_depth", type=int, default=4, # Updated depth to 4 as per Phase 1 plan
                       help="Depth of transformer layers")
    
    # 数据集参数
    parser.add_argument("--lr_dir", type=str, default="E:/vscode/2号CT数据", # Updated path
                        help="Directory containing LR images (2号CT数据, 分辨率2700x2700, 体素大小0.03889mm)")
    parser.add_argument("--hr_dir", type=str, default="E:/vscode/3号CT数据", # Updated path
                        help="Directory containing HR images (3号CT数据, 分辨率2700x2700, 体素大小0.00956mm)")
    
    # 训练超参数
    parser.add_argument("--batch_size", type=int, default=8, help="Batch size for training") # Keep supervised training batch size
    parser.add_argument("--pretrain_batch_size", type=int, default=6, help="Batch size for pretraining")   # Updated pretrain batch size to 6
    # 确保这里正确解析epochs参数
    parser.add_argument("--epochs", type=int, default=150, help="训练轮数") # Keep supervised training epochs
    parser.add_argument("--pretrain_epochs", type=int,default=50, help="预训练轮数")  # Changed default to 50
    parser.add_argument("--lr", type=float, default=1e-4, help="Initial learning rate")
    parser.add_argument("--warmup_epochs", type=int, default=5, help="Number of warmup epochs for learning rate scheduler") # 添加 warmup_epochs 参数
    parser.add_argument("--temporal_window", type=int, default=5,
                        help="Temporal window size for continuous CT slices processing")
    # --patch_size is now primarily for supervised 'train' mode or single-scale pretrain fallback
    parser.add_argument("--patch_size", type=int, default=32, 
                        help="Size of image patches for training (supervised mode or single-scale pretrain)")
    # New argument for multi-scale pretraining patch sizes
    parser.add_argument("--patch_sizes", type=int, nargs='+', default=[32, 64, 128],
                        help="List of patch sizes for multi-scale pretraining (e.g., --patch_sizes 32 64 128)")
    parser.add_argument('--temperature', type=float, default=0.1, help='Temperature for contrastive loss') # 添加 temperature 参数
    parser.add_argument("--gradient_accumulation_steps", type=int, default=32,  # Updated gradient accumulation steps to 32
                        help="Number of steps to accumulate gradients before updating weights")
    parser.add_argument("--use_amp", action="store_true", default=True,
                        help="Use Automatic Mixed Precision for training")
    parser.add_argument("--use_mem_efficient", action="store_true", default=True)  # 新增内存优化选项
    parser.add_argument("--use_xformers", action="store_true", default=True)  # 新增xformers支持
    
    # 模型参数
    parser.add_argument("--pretrained_encoder", type=str, default=None,
                        help="Path to pretrained encoder weights (optional)")
    parser.add_argument("--num_workers", type=int, default=4,
                        help="Number of data loading workers")
    parser.add_argument("--device", type=str, default="cuda" if torch.cuda.is_available() else "cpu",
                        help="Device to use for training (cuda/cpu)")
    
    # 保存和日志参数
    parser.add_argument("--save_interval", type=int, default=10,
                        help="Epoch interval for saving checkpoints")
    parser.add_argument("--resume", type=str, default=None,
                        help="Path to checkpoint for resuming training")
    
    args = parser.parse_args()
    
    # 创建必要的目录
    os.makedirs("checkpoints/enhanced", exist_ok=True)
    os.makedirs("logs/enhanced", exist_ok=True)
    os.makedirs("checkpoints/pretrain", exist_ok=True)
    os.makedirs("logs/pretrain", exist_ok=True)
    
    # 打印训练配置信息
    print("\nTraining Configuration:")
    print(f"Mode: {args.mode}")
    print(f"Device: {args.device}")
    if args.mode == "train":
        print(f"Batch Size: {args.batch_size}")
        print(f"Epochs: {args.epochs}")
        print(f"Gradient Accumulation Steps: {args.gradient_accumulation_steps}")
    else:
        print(f"Batch Size: {args.pretrain_batch_size}")
        print(f"Epochs: {args.pretrain_epochs}")
    print(f"Learning Rate: {args.lr}")
    print(f"Number of Workers: {args.num_workers}")
    print(f"Mixed Precision Training: {args.use_amp}")
    print(f"Memory Efficient Attention: {args.use_mem_efficient}")  # 新增状态显示
    print(f"XFormers Enabled: {args.use_xformers}")  # 新增状态显示
    if args.mode == "train":
        print(f"LR Dataset: {args.lr_dir} (2700x2700, 0.03889mm)")
        print(f"HR Dataset: {args.hr_dir} (2700x2700, 0.00956mm)")
        print(f"Temporal Window: {args.temporal_window}")
    # Update print statement based on mode
    if args.mode == "pretrain":
        print(f"Pretrain Patch Sizes: {args.patch_sizes}")
    else:
        print(f"Train Patch Size: {args.patch_size}")
    print(f"Temperature (Contrastive): {args.temperature}\n")


    if args.mode == "pretrain":
        # 进行自监督预训练
        print("Starting self-supervised pretraining...")
        pretrain_self_supervised(
            data_dir=args.lr_dir,  # 使用2号CT数据进行预训练
            batch_size=args.pretrain_batch_size,
            epochs=args.pretrain_epochs,  # 使用命令行参数的预训练轮数
            lr=args.lr,
            patch_sizes=args.patch_sizes, # Pass the list of patch sizes
            num_workers=args.num_workers,
            device=args.device,
            save_interval=args.save_interval,
            resume=args.resume,
            use_amp=args.use_amp,
            use_mem_efficient=args.use_mem_efficient,  # 传递内存优化参数
            use_xformers=args.use_xformers,  # 传递xformers参数
            model_dim=args.model_dim,  # 添加model_dim参数
            model_depth=args.model_depth,  # 添加model_depth参数
            temperature=args.temperature, # 传递 temperature 参数
            warmup_epochs=args.warmup_epochs, # 传递 warmup_epochs 参数
            gradient_accumulation_steps=args.gradient_accumulation_steps # 添加缺失的参数传递
        )
    elif args.mode == "train":
        # 使用预训练的编码器进行监督训练
        print("\nStarting enhanced super-resolution training...")
        # 检查预训练模型是否存在
        pretrained_path = "checkpoints/pretrain/best_model.pth"
        if not os.path.exists(pretrained_path):
             print(f"Warning: Pretrained model not found at {pretrained_path}. Training from scratch.")
             pretrained_path = None # Set to None if not found

        train_enhanced_sr(
            lr_dir=args.lr_dir,
            hr_dir=args.hr_dir,
            batch_size=args.batch_size,
            epochs=args.epochs,
            lr=args.lr,
            temporal_window=args.temporal_window,
            patch_size=args.patch_size,
            pretrained_encoder_path=pretrained_path, # Use variable path
            num_workers=args.num_workers,
            device=args.device,
            save_interval=args.save_interval,
            resume=args.resume,
            gradient_accumulation_steps=args.gradient_accumulation_steps,
            use_amp=args.use_amp,
            # use_mem_efficient=args.use_mem_efficient,  # train_enhanced_sr 不需要此参数
            # use_xformers=args.use_xformers,  # train_enhanced_sr 不需要此参数
            warmup_epochs=args.warmup_epochs # 传递 warmup_epochs 参数
        )
    elif args.mode == "validate":
        print("Validation mode not yet implemented.")
        # 这里可以添加验证逻辑
    else:
        print(f"Unknown mode: {args.mode}")


if __name__ == "__main__":
    main()
