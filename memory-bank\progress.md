# CT图像超分辨率项目进展

## 已完成工作

1. **项目规划与设计**：
   - 确定了基于自监督预训练和监督训练相结合的方法
   - 规划了多种损失函数的实现和比较
   - 设计了针对CT图像特性的处理策略

2. **自监督预训练实现**：
   - 实现了多种预训练方法：
     - `train_swin_mae.py`：基础Swin-MAE实现
     - `train_mae.py`：基于Vision Transformer的MAE实现
     - `train_enhanced.py`：包含多尺度对比学习的预训练
     - `train_swin_mae_resnet_random_mask_perc_final.py`：改进的Swin-MAE，使用随机掩码策略
     - `train_swin_mae_ultra.py`：最新的Swin-MAE变体，使用窗口掩码和层次化解码器
     - `train_swin_mae_resnet_random_mask_structured.py`：结合随机掩码和结构化解码器输入的实现
   - 实现了不同的掩码策略：
     - 随机掩码（仅用于损失计算）：编码器处理完整图像，掩码仅在计算损失时应用
     - 窗口掩码（应用于编码器输入）：在编码器输入前应用掩码，被掩码区域用掩码标记替换
   - 实现了不同的解码器架构：
     - 标准解码器：直接从编码器输出解码到原始分辨率
     - 层次化解码器：使用跨阶段特征融合和交叉注意力机制，类似U-Net结构
     - 层次化解码器（结合交叉注意力）：在层次化结构中明确引入交叉注意力模块
     - 结构化解码器输入：使用decoder_mask_token和decoder_pos_embed提供更好的位置信息
   - 完成了数据加载和预处理流程
   - 实现了梯度累积和混合精度训练以处理大尺寸CT图像

3. **损失函数实现**：
   - 实现了感知损失（PerceptualLoss），基于ResNet18提取特征，针对CT图像优化
   - 实现了频域损失（FrequencyLoss/FrequencyDomainLoss），考虑体素大小差异
   - 实现了结构相似性损失（StructureSimilarityLoss/SSIM），关注结构而非像素级差异
   - 实现了纹理损失（TextureLoss），特别关注岩心纹理特征
   - 实现了PatchNCE对比损失，使重建特征与原始特征更相似，同时与其他特征保持差异
     - 创建了专门的投影器，将解码器特征和原始patch像素投影到共享特征空间
     - 优化了特征对比方式，使用温度参数控制softmax的平滑程度
     - 实现了正负样本对的构建和对比学习损失计算
   - 实现了复合损失（CompositeLoss），组合多种损失函数

4. **超分辨率网络实现**：
   - 实现了增强型超分辨率网络（EnhancedSRNetwork）
   - 实现了多种先进组件，包括伪3D处理、增强型残差块、可变形大核注意力等
   - 实现了频域注意力机制和小波注意力模块

## 待完成工作

1. **自监督预训练优化**：
   - 完成`train_swin_mae_ultra.py`的两组对比实验：
     - 实验1：不使用SSIM损失，评估窗口掩码和层次化解码器的基础效果
     - 实验2：添加SSIM损失，评估结构相似性损失对特征学习的影响
   - 使用`visualize_swin_mae_resnet.py`进行预训练模型的可视化和分析
   - 评估不同掩码策略和解码器设计的特征表示能力
   - 确定最适合超分辨率任务的预训练模型架构和损失函数组合

2. **监督训练阶段**：
   - 设计并实现处理非重叠数据的训练策略（伪配对训练）
   - 对高分辨率的3号CT数据进行降采样，创建配对数据
   - 将预训练编码器集成到超分辨率网络中
   - 实现完整的超分辨率重建模型，包括模型架构、损失函数和训练配置
   - 使用数据集#3作为高分辨率参考进行训练
   - 应用训练好的模型到数据集#2，进行超分辨率重建

3. **评估与优化**：
   - 实现全面的评估指标（PSNR、SSIM等）
   - 进行可视化对比（原始低分辨率、插值放大、SR模型输出、高分辨率参考）
   - 分析HU值分布和特定结构（如孔隙、颗粒）的清晰度
   - 优化模型推理效率

4. **进阶方法探索**：
   - 探索特征级对齐/损失方法，进一步处理非重叠数据
   - 考虑域适应/生成对抗网络方法，提高重建质量
   - 进行消融实验，评估不同模块和损失函数的贡献

## 当前状态

项目正在优化自监督预训练阶段，同时准备进入监督训练阶段。已经实现了多种预训练方法（对比学习、MAE特征学习、Swin-MAE特征学习）、掩码策略（随机掩码、窗口掩码）、解码器架构（标准解码器、层次化解码器、结构化解码器输入）、损失函数（重建损失、感知损失、SSIM损失、PatchNCE对比损失）和超分辨率网络架构。

**最新重大突破**：在`train_swin_mae_resnet_random_mask_hierarchical.py`中实现了显著的性能提升。使用层次化解码器 + 随机掩码 + SSIM损失 + PatchNCE对比损失的组合，在检查点`checkpoints/swin_mae_hierarchical_random_ssim_nce_w002/best_model.pth`中取得了**PSNR = 21.51, SSIM = 0.626**的优秀结果。相比之前的最佳结果（PSNR = 21.49），不仅PSNR有所提升，SSIM更是从0.53大幅提升到0.626，这表明模型在结构相似性方面有了质的飞跃。这一成果验证了层次化解码器架构的有效性，为后续的监督训练阶段提供了强大的特征提取基础。

同时，仍在进行`train_swin_mae_ultra.py`的测试，该模型引入了窗口掩码策略和层次化解码器，并进行多组对比实验：评估窗口掩码vs随机掩码、SSIM损失和PatchNCE损失的效果。这些实验将帮助确定最佳的预训练模型架构和损失函数组合。

此外，已实现了结合随机掩码和层次化解码器（包含交叉注意力）的新变体（<mcfile name="train_swin_mae_resnet_random_mask_hierarchical.py" path="e:\\vscode\\非配位超分辨\\train_swin_mae_resnet_random_mask_hierarchical.py"></mcfile>），正在进行初步测试以评估其性能。

初步实验结果表明，随机掩码策略（先前实验PSNR=18.77，最新实验PSNR=21.32）似乎优于窗口掩码策略（PSNR=18.12）。这可能是因为CT图像具有高度结构化的特征，完整处理可以更好地捕获全局上下文，而掩码标记可能引入人为噪声，影响特征学习。PatchNCE对比损失的成功引入（PSNR提升至21.49）进一步证实了其在提升特征学习质量方面的潜力，尤其是在保持局部细节和结构信息方面。

### 关键指标

| 指标 | 基线方法 | 当前最佳 | 目标 | 状态 |
|------|---------|---------|------|------|
| 预训练重建质量(PSNR) | 随机掩码(21.32) | **层次化+随机掩码+SSIM+NCE (21.51)** | >22.0 | 🔥 接近目标 |
| 预训练重建质量(SSIM) | 基础MAE(0.53) | **层次化解码器 (0.626)** | >0.65 | 🚀 显著提升 |
| 预训练重建质量(视觉) | 基础MAE | **Swin-MAE+层次化解码器** | 进一步提高 | ✅ 已改善 |
| 超分辨率PSNR | 待建立 | - | 提升2-3 dB | ⏳ 准备启动 |
| 超分辨率SSIM | 待建立 | - | 提升5-10% | ⏳ 准备启动 |
| 训练时间 | 已记录 | 优化中 | 进一步优化 | 🔄 持续改进 |
| 推理时间 | 待测试 | - | <1秒/切片 | ⏳ 待测试 |

### 🎯 **当前最佳模型配置**
- **模型架构**：`train_swin_mae_resnet_random_mask_hierarchical.py`
- **检查点路径**：`checkpoints/swin_mae_hierarchical_random_ssim_nce_w002/best_model.pth`
- **关键技术组合**：
  - ✅ 层次化解码器（跨阶段特征融合）
  - ✅ 随机掩码策略（编码器处理完整图像）
  - ✅ SSIM损失（结构相似性优化）
  - ✅ PatchNCE对比损失（特征判别性增强）
  - ✅ ResNet18感知损失（视觉质量保证）

## 已知问题

1. **掩码策略与解码器设计**：
   - 窗口掩码策略（PSNR=18.12）似乎不如随机掩码策略（PSNR=18.77）
   - 需要进一步测试层次化解码器的效果，可能需要与随机掩码策略结合
   - 需要确定SSIM损失对特征学习的影响

2. **非重叠数据训练策略**：
   - 数据集#2和#3来自同一岩心的不同、非重叠部位，无法直接进行像素级配对训练
   - 需要设计特殊的训练策略，如伪配对训练、特征级对齐或域适应方法
   - 伪配对训练可能无法完全模拟真实的低分辨率到高分辨率映射

3. **针对CT图像的感知损失**：
   - 当前实现通过简单通道复制（灰度→RGB）处理灰度图
   - 可能不是最优方法，需要探索更适合CT图像的特征提取器或处理方式

3. **预训练编码器集成**：
   - 如何有效地将预训练编码器集成到超分辨率网络中
   - 需要决定是冻结部分层进行微调，还是仅使用权重初始化

4. **计算资源限制**：
   - 处理2700×2700大尺寸CT图像需要大量GPU内存
   - 当前使用梯度累积和混合精度训练缓解，但仍需优化

## 项目决策演变

### 预训练方法选择

- **初始考虑**：单一的MAE预训练
- **中期决策**：实现多种预训练方法（Swin-MAE、MAE、多尺度对比学习）
- **当前决策**：重点优化Swin-MAE架构，测试不同掩码策略（随机掩码vs窗口掩码）和解码器设计（标准解码器vs层次化解码器）
- **原因**：
  - 不同预训练方法可能捕获不同类型的特征，提供更全面的初始化
  - 初步实验表明随机掩码策略效果更好，但层次化解码器可能提供更好的特征学习能力

### 监督训练策略

- **初始考虑**：直接监督训练
- **当前决策**：考虑伪配对训练作为主要策略，辅以特征级对齐
- **原因**：伪配对训练是处理非重叠数据的常用且相对直接的方法，特征级对齐可能提供更好的泛化能力

### 损失函数选择

- **初始考虑**：基础像素级损失（MSE/L1）
- **自监督预训练阶段**：
  - **中期决策**：重建损失(L1/L2) + 感知损失(ResNet18)
  - **当前决策**：测试重建损失 + 感知损失，以及重建损失 + 感知损失 + SSIM损失的组合
  - **原因**：SSIM损失关注图像的结构相似性，对于CT图像这类医学图像特别有价值
- **监督训练阶段**：
  - **当前决策**：像素损失 + 感知损失 + 频域损失的组合
  - **原因**：频域损失对恢复高频细节特别有效，感知损失关注视觉质量，像素损失保证基本重建准确性

### 网络架构

- **自监督预训练阶段**：
  - **初始考虑**：基础MAE/Swin-MAE架构
  - **中期决策**：改进的Swin-MAE，使用随机掩码策略和感知损失
  - **当前决策**：测试窗口掩码策略和层次化解码器（`train_swin_mae_ultra.py`）
  - **原因**：层次化解码器通过跨阶段特征融合可能提供更好的重建质量，但窗口掩码可能不如随机掩码

- **监督训练阶段**：
  - **初始考虑**：传统CNN架构（如U-Net）
  - **当前决策**：增强型超分辨率网络，结合Transformer、注意力机制和频域处理
  - **原因**：综合利用多种先进技术，更好地处理CT图像的复杂特性
