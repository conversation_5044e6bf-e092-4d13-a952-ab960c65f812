# Fast BSRGAN Test Configuration - 快速验证准确BSRGAN降质
# 优化参数以加快测试速度

# --- Data Configuration ---
data:
  lr_dir: 'data/2号CT数据'
  hr_dir: 'data/3号CT数据'
  patch_size_hr: 128
  scale_factor: 4
  
  # 降质方法选择
  degradation_method: 'accurate_bsrgan'

  # 简化的BSRGAN参数（减少计算复杂度）
  blur_prob: 0.5                        # 降低到50%
  noise_prob: 0.5                       # 降低到50%
  downsample_prob: 1.0                   # 保持100%（必须）
  jpeg_prob: 0.1                        # 降低到10%
  
  # 简化模糊参数
  blur_kernel_size: 11                   # 减小核大小
  blur_sigma_range: [0.5, 2.0]          # 减小范围
  aniso_prob: 0.3                       # 降低各向异性概率
  
  # 简化下采样参数
  downsample_methods: ['bicubic', 'bilinear']  # 减少方法数量
  
  # 简化噪声参数
  noise_gaussian_sigma_range: [1, 15]    # 减小范围

# --- Model Configuration ---
model:
  in_channels: 1
  out_channels: 1
  base_channels: 64                      # 减小模型尺寸
  channel_mults: [1, 2, 2]              # 减少层数
  attention_resolutions: [16]            # 减少注意力层
  num_res_blocks: 1                     # 减少残差块
  dropout: 0.1

  # Pretrained Encoder Settings (disabled)
  use_pretrained_encoder: False
  encoder_type: "Swin-MAE"
  encoder_checkpoint: null
  condition_method: 'CrossAttention'
  num_heads: 4                          # 减少注意力头数

# --- Diffusion Process Configuration ---
diffusion:
  schedule_name: 'linear'               # 使用更简单的调度
  timesteps: 100                        # 大幅减少时间步

# --- Training Configuration ---
training:
  log_root: './logs/sr_diffusion'
  checkpoint_root: './checkpoints/sr_diffusion'
  learning_rate: 2.0e-4                # 提高学习率
  weight_decay: 1.0e-5                 # 减小权重衰减
  batch_size: 4                        # 减小批次大小
  epochs: 5                            # 只训练5个epoch用于测试

  # 简化损失函数
  diffusion_loss_type: 'l1'
  perceptual_loss_weight: 0.1          # 减小权重
  perceptual_loss_type: 'l1'
  ssim_loss_weight: 0.1                # 减小权重
  gradient_loss_weight: 0.05           # 减小权重

  # GAN Disabled
  use_gan: False
  gan_loss_weight: 0.0

  # 优化设置
  use_amp: True
  seed: 42
  num_workers: 2                       # 减少工作进程
  log_interval: 10                     # 更频繁的日志
  save_interval: 2                     # 更频繁的保存

  # 禁用调度器
  use_scheduler: False
  
  # 简化梯度优化
  grad_clip_norm: 0.5
  gradient_accumulation_steps: 1       # 不使用梯度累积

# --- Inference Configuration ---
inference:
  sampling_steps: 10                   # 大幅减少采样步数
  eta: 0.0

# --- Experiment Tracking ---
experiment:
  name: "Fast_BSRGAN_Test_v1"
  description: "Fast test of accurate BSRGAN degradation (optimized for speed)"
  tags: ["test", "fast", "accurate-bsrgan", "speed-optimized"]
