import torch
import torch.nn as nn
import torch.nn.functional as F
import torch.optim as optim
from torch.optim.lr_scheduler import CosineAnnealingLR
from data.mae_loader import get_mae_loader # Import new loader
import timm # Import timm
assert timm.__version__ >= "0.3.2" # MAE requires timm support, assert version
from timm.models.vision_transformer import Block # Use timm's Block for decoder
from tqdm import tqdm
import os
import numpy as np
import matplotlib.pyplot as plt
from torch.utils.tensorboard import SummaryWriter
import math
import random
from util.pos_embed import get_2d_sincos_pos_embed # Assuming pos embed helper is in util
import util.lr_sched as lr_sched # Import lr_sched for warmup adjustment

# --------------------------------------------------------
# Based on MAE implementation by Meta Platforms, Inc.
# --------------------------------------------------------

class MaskedAutoencoderViT(nn.Module):
    """ Masked Autoencoder with VisionTransformer backbone
    """
    def __init__(self, img_size=256, patch_size=32, in_chans=1, # Changed patch_size default to 32
                 embed_dim=768, depth=12, num_heads=12,
                 decoder_embed_dim=512, decoder_depth=8, decoder_num_heads=16,
                 mlp_ratio=4., norm_layer=nn.LayerNorm, norm_pix_loss=False):
        super().__init__()

        # --------------------------------------------------------------------------
        # MAE encoder specifics
        # Use timm's VisionTransformer for the encoder
        self.encoder = timm.create_model(
            'vit_base_patch32_224', # Changed to a known timm model name, will rely on img_size param
            pretrained=False,
            in_chans=in_chans,
            img_size=img_size, # Keep img_size=256, timm might interpolate pos embeds
            patch_size=patch_size,
            embed_dim=embed_dim,
            depth=depth,
            num_heads=num_heads,
            mlp_ratio=mlp_ratio,
            norm_layer=norm_layer,
            global_pool='' # Important: disable global pooling for MAE encoder
        )
        # Remove the final norm and head from timm's ViT as we only need features
        del self.encoder.norm
        del self.encoder.head
        # Access patch_embed and pos_embed from the timm model
        self.patch_embed = self.encoder.patch_embed
        self.pos_embed = self.encoder.pos_embed # timm ViT includes pos_embed
        num_patches = self.patch_embed.num_patches
        # --------------------------------------------------------------------------

        # --------------------------------------------------------------------------
        # MAE decoder specifics
        self.decoder_embed = nn.Linear(embed_dim, decoder_embed_dim, bias=True)

        self.mask_token = nn.Parameter(torch.zeros(1, 1, decoder_embed_dim))

        # Create decoder positional embedding
        self.decoder_pos_embed = nn.Parameter(torch.zeros(1, num_patches + 1, decoder_embed_dim), requires_grad=False)  # fixed sin-cos embedding

        # Create decoder blocks using timm's Block definition
        self.decoder_blocks = nn.ModuleList([
            Block(decoder_embed_dim, decoder_num_heads, mlp_ratio, qkv_bias=True, norm_layer=norm_layer)
            for i in range(decoder_depth)])

        self.decoder_norm = norm_layer(decoder_embed_dim)
        self.decoder_pred = nn.Linear(decoder_embed_dim, patch_size**2 * in_chans, bias=True) # decoder to patch
        # --------------------------------------------------------------------------

        self.norm_pix_loss = norm_pix_loss

        self.initialize_weights()

    def initialize_weights(self):
        # Initialize (and freeze) pos_embed by sin-cos embedding
        pos_embed = get_2d_sincos_pos_embed(self.pos_embed.shape[-1], int(self.patch_embed.num_patches**0.5), cls_token=True)
        self.pos_embed.data.copy_(torch.from_numpy(pos_embed).float().unsqueeze(0))

        decoder_pos_embed = get_2d_sincos_pos_embed(self.decoder_pos_embed.shape[-1], int(self.patch_embed.num_patches**0.5), cls_token=True)
        self.decoder_pos_embed.data.copy_(torch.from_numpy(decoder_pos_embed).float().unsqueeze(0))

        # Initialize patch_embed like nn.Linear (instead of nn.Conv2d)
        w = self.patch_embed.proj.weight.data
        torch.nn.init.xavier_uniform_(w.view([w.shape[0], -1]))

        # Initialize cls_token and mask_token
        # torch.nn.init.normal_(self.encoder.cls_token, std=.02) # timm model has cls_token
        torch.nn.init.normal_(self.mask_token, std=.02)

        # Initialize nn.Linear and nn.LayerNorm
        self.apply(self._init_weights)

    def _init_weights(self, m):
        if isinstance(m, nn.Linear):
            torch.nn.init.xavier_uniform_(m.weight)
            if isinstance(m, nn.Linear) and m.bias is not None:
                nn.init.constant_(m.bias, 0)
        elif isinstance(m, nn.LayerNorm):
            nn.init.constant_(m.bias, 0)
            nn.init.constant_(m.weight, 1.0)

    def random_masking(self, x, mask_ratio):
        """ Perform per-sample random masking by shuffling :: x: [N, L, D] """
        # Note: N is batch size, L is sequence length (num_patches), D is embed_dim
        N, L, D = x.shape  # batch, length, dim
        len_keep = int(L * (1 - mask_ratio))

        noise = torch.rand(N, L, device=x.device)  # noise in [0, 1]

        # sort noise for each sample
        ids_shuffle = torch.argsort(noise, dim=1)  # ascend: small is keep, large is remove
        ids_restore = torch.argsort(ids_shuffle, dim=1)

        # keep the first subset
        ids_keep = ids_shuffle[:, :len_keep]
        x_masked = torch.gather(x, dim=1, index=ids_keep.unsqueeze(-1).repeat(1, 1, D))

        # generate the binary mask: 0 is keep, 1 is remove
        mask = torch.ones([N, L], device=x.device)
        mask[:, :len_keep] = 0
        # unshuffle to get the binary mask
        mask = torch.gather(mask, dim=1, index=ids_restore)

        return x_masked, mask, ids_restore

    def forward_encoder(self, x, mask_ratio):
        # embed patches
        x = self.patch_embed(x) # Uses timm's patch_embed

        # add pos embed w/o cls token
        x = x + self.pos_embed[:, 1:, :] # Access pos_embed from timm model

        # masking: length -> length * mask_ratio
        x, mask, ids_restore = self.random_masking(x, mask_ratio)

        # append cls token
        cls_token = self.encoder.cls_token + self.pos_embed[:, :1, :] # Access cls_token from timm model
        cls_tokens = cls_token.expand(x.shape[0], -1, -1)
        x = torch.cat((cls_tokens, x), dim=1)

        # apply Transformer blocks (from timm model)
        # We need to iterate through blocks as timm doesn't expose a single 'encoder' attribute
        for blk in self.encoder.blocks:
             x = blk(x)
        # Note: We removed the final norm from the timm encoder instance

        return x, mask, ids_restore

    def forward_decoder(self, x, ids_restore):
        # embed tokens
        x = self.decoder_embed(x)

        # append mask tokens to sequence
        mask_tokens = self.mask_token.repeat(x.shape[0], ids_restore.shape[1] + 1 - x.shape[1], 1)
        x_ = torch.cat([x[:, 1:, :], mask_tokens], dim=1)  # no cls token
        x_ = torch.gather(x_, dim=1, index=ids_restore.unsqueeze(-1).repeat(1, 1, x.shape[2]))  # unshuffle
        x = torch.cat([x[:, :1, :], x_], dim=1)  # append cls token

        # add pos embed
        x = x + self.decoder_pos_embed

        # apply Transformer blocks
        for blk in self.decoder_blocks:
            x = blk(x)
        x = self.decoder_norm(x)

        # predictor projection
        x = self.decoder_pred(x)

        # remove cls token
        x = x[:, 1:, :]

        return x

    def patchify(self, imgs):
        """ imgs: (N, C, H, W) -> (N, L, patch_size**2 * C) """
        # Use patch_embed info
        p = self.patch_embed.patch_size[0] # Get patch size from timm's patch_embed
        assert imgs.shape[2] == imgs.shape[3] and imgs.shape[2] % p == 0
        h = w = imgs.shape[2] // p
        c = imgs.shape[1]
        x = imgs.reshape(shape=(imgs.shape[0], c, h, p, w, p))
        x = torch.einsum('nchpwq->nhwpqc', x)
        x = x.reshape(shape=(imgs.shape[0], h * w, p**2 * c))
        return x

    def unpatchify(self, x):
        """ x: (N, L, patch_size**2 * C) -> (N, C, H, W) """
        p = self.patch_embed.patch_size[0]
        h = w = int(x.shape[1]**0.5)
        assert h * w == x.shape[1]
        c = x.shape[2] // (p*p) # Calculate C

        x = x.reshape(shape=(x.shape[0], h, w, p, p, c))
        x = torch.einsum('nhwpqc->nchpwq', x)
        imgs = x.reshape(shape=(x.shape[0], c, h * p, w * p))
        return imgs

    def forward_loss(self, imgs, pred, mask):
        """
        imgs: [N, C, H, W]
        pred: [N, L, p*p*C]
        mask: [N, L], 0 is keep, 1 is remove,
        """
        target = self.patchify(imgs)
        if self.norm_pix_loss:
            mean = target.mean(dim=-1, keepdim=True)
            var = target.var(dim=-1, keepdim=True)
            target = (target - mean) / (var + 1.e-6)**.5

        loss = (pred - target) ** 2
        loss = loss.mean(dim=-1)  # [N, L], mean loss per patch

        # loss = (loss * mask).sum() / mask.sum() if mask.sum() > 0 else (loss*mask).sum() # Original MAE loss
        # Safer version to avoid division by zero if mask is all zeros (shouldn't happen with mask_ratio < 1)
        mask_sum = mask.sum()
        loss = (loss * mask).sum() / (mask_sum + 1e-8) # mean loss on removed patches

        return loss

    def forward(self, imgs, mask_ratio=0.75):
        # Use timm encoder's forward_features for flexibility if needed, or call blocks directly
        latent, mask, ids_restore = self.forward_encoder(imgs, mask_ratio)
        pred = self.forward_decoder(latent, ids_restore)  # [N, L, p*p*C]
        loss = self.forward_loss(imgs, pred, mask)
        return loss, pred, mask


# --- Training Function ---
def setup_mae_training(model_params, optimizer_params, device):
    """设置 MAE 训练的优化器和调度器"""
    # MAE often uses AdamW with higher LR and different WD than contrastive
    lr = optimizer_params.get('lr', 1.5e-4)
    weight_decay = optimizer_params.get('weight_decay', 0.05)
    optimizer = optim.AdamW(model_params, lr=lr, betas=(0.9, 0.95), weight_decay=weight_decay)

    # Cosine scheduler is common
    scheduler = CosineAnnealingLR(optimizer, T_max=optimizer_params['epochs'], eta_min=lr/100) # Adjust eta_min if needed

    # Warmup can be added manually or via a wrapper if needed
    # scaler = torch.cuda.amp.GradScaler(enabled=optimizer_params.get('use_amp', True))
    # MAE might be sensitive to AMP, start without it or test carefully
    scaler = torch.cuda.amp.GradScaler(enabled=optimizer_params.get('use_amp', False)) # Allow enabling AMP via params

    return optimizer, scheduler, scaler

def train_epoch_mae(model, train_loader, optimizer, scaler, device, writer, epoch, total_epochs, base_lr, warmup_epochs, log_interval=50, use_amp=False): # Added total_epochs, base_lr, warmup_epochs
    """MAE 训练循环 with warmup"""
def train_epoch_mae(model, train_loader, optimizer, scaler, device, writer, epoch, total_epochs, base_lr, warmup_epochs, gradient_accumulation_steps=1, log_interval=50, use_amp=False): # Added gradient_accumulation_steps
    """MAE 训练循环 with warmup and gradient accumulation"""
    model.train()
    total_loss = 0
    num_batches = len(train_loader)
    processed_batches = 0 # Count batches actually processed
    optimizer.zero_grad() # Zero gradients at the beginning of the epoch

    with tqdm(train_loader, unit="batch") as pbar:
        for batch_idx, imgs in enumerate(pbar):
            if imgs is None or imgs.nelement() == 0: # Skip empty batches from filter_none
                print(f"Skipping empty batch at index {batch_idx}")
                continue

            global_step = epoch * num_batches + batch_idx
            # --- Adjust learning rate using linear warmup ---
            # We use a simplified approach here, adjusting LR *before* the step.
            # A more robust way might involve lr_sched.adjust_learning_rate helper if available.
            if epoch < warmup_epochs:
                lr_scale = (global_step + 1) / (warmup_epochs * num_batches) # +1 because global_step starts at 0
                new_lr = base_lr * lr_scale
                for param_group in optimizer.param_groups:
                    param_group['lr'] = new_lr
            # After warmup, the scheduler (stepped per epoch) handles decay.
            # --- End LR adjustment ---

            imgs = imgs.to(device, non_blocking=True)

            # optimizer.zero_grad() # Moved outside the loop for accumulation

            with torch.cuda.amp.autocast(enabled=use_amp): # Enable AMP context
                loss, _, _ = model(imgs, mask_ratio=0.75) # Get loss directly from model
                loss_value = loss.item() # Store original loss for logging

                # Scale loss for gradient accumulation
                loss = loss / gradient_accumulation_steps

            # Check for NaN loss *before* backward pass
            if math.isnan(loss_value):
                print(f"Warning: NaN loss detected at epoch {epoch+1}, batch {batch_idx}. Skipping batch gradient.")
                # Don't call backward or step, let the loop continue to the next batch
                # Gradients should already be zeroed if the previous step was successful,
                # or will be zeroed at the next optimizer step.
                continue

            scaler.scale(loss).backward()

            # --- Gradient Accumulation Step ---
            if (batch_idx + 1) % gradient_accumulation_steps == 0 or (batch_idx + 1) == num_batches:
                scaler.step(optimizer)
                scaler.update()
                optimizer.zero_grad() # Zero gradients only after stepping

            # Use loss_value for logging and tracking
            current_loss = loss_value
            if math.isnan(current_loss): # Double check after potential scaling issues, though unlikely
                print(f"Warning: NaN loss detected at epoch {epoch+1}, batch {batch_idx}. Skipping batch.")
                optimizer.zero_grad() # Reset gradients if loss is NaN
                continue

            total_loss += current_loss # Accumulate the original loss value
            processed_batches += 1
            # Display the non-accumulated loss in the progress bar
            pbar.set_postfix(loss=f"{current_loss:.4f}", lr=f"{optimizer.param_groups[0]['lr']:.1e}")

            # Log batch loss based on the original value, potentially less frequently if accumulating
            if (batch_idx + 1) % (log_interval * gradient_accumulation_steps) == 0:
                writer.add_scalar('Loss/batch', current_loss, global_step) # Log original loss value
                writer.add_scalar('LR', optimizer.param_groups[0]['lr'], global_step)

    avg_loss = total_loss / processed_batches if processed_batches > 0 else 0
    return avg_loss


def pretrain_mae(data_dir='E:/vscode/2号CT数据', batch_size=64, num_workers=4, # Updated path
                 epochs=200, lr=1.5e-4, warmup_epochs=40, weight_decay=0.05, use_amp=True,
                 gradient_accumulation_steps=1, # Add gradient accumulation steps
                 img_size=256, patch_size=32,
                 model_embed_dim=768, model_depth=12, model_num_heads=12, # Encoder params
                 decoder_embed_dim=512, decoder_depth=8, decoder_num_heads=16, # Decoder params
                 clip_min=-1000.0, clip_max=1000.0, # Add clip parameters
                 device=None, save_interval=20, resume=None,
                 checkpoint_dir='checkpoints/mae_pretrain', log_dir='logs/mae_pretrain'):
    """MAE 自监督预训练函数"""
    device = torch.device('cuda' if torch.cuda.is_available() else 'cpu')
    print(f"Using device: {device}")
    # Calculate effective batch size
    effective_batch_size = batch_size * gradient_accumulation_steps
    print(f"MAE Pretraining Parameters: epochs={epochs}, batch_size={batch_size}, grad_accum={gradient_accumulation_steps}, effective_batch_size={effective_batch_size}, img_size={img_size}, patch_size={patch_size}, use_amp={use_amp}")
    print(f"MAE Pretraining Parameters: epochs={epochs}, batch_size={batch_size}, img_size={img_size}, patch_size={patch_size}, use_amp={use_amp}")

    os.makedirs(checkpoint_dir, exist_ok=True)
    os.makedirs(log_dir, exist_ok=True)
    writer = SummaryWriter(log_dir)

    # Instantiate MAE model
    # Consider using smaller ViT variants if memory is an issue e.g., vit_small_patch16_224
    model = MaskedAutoencoderViT(
        img_size=img_size, patch_size=patch_size, in_chans=1,
        embed_dim=model_embed_dim, depth=model_depth, num_heads=model_num_heads,
        decoder_embed_dim=decoder_embed_dim, decoder_depth=decoder_depth, decoder_num_heads=decoder_num_heads,
        mlp_ratio=4.0, norm_pix_loss=False # Set norm_pix_loss=False
    ).to(device)

    print(f"MAE Model Parameters: {sum(p.numel() for p in model.parameters() if p.requires_grad):,}")

    # Get MAE data loader
    # Note: crop_size in loader should match img_size for the model
    train_loader = get_mae_loader(
        data_dir=data_dir,
        batch_size=batch_size,
        num_workers=num_workers,
         crop_size=img_size, # Loader provides crops of the size model expects
         augment=True,
         clip_min=clip_min, # Pass clip parameters
         clip_max=clip_max
     )

    # Setup optimizer and scheduler for MAE
    # Adjust T_max for scheduler to account for warmup
    optimizer_params = {'lr': lr, 'epochs': epochs - warmup_epochs, 'weight_decay': weight_decay, 'use_amp': use_amp} # Scheduler T_max is total epochs minus warmup
    optimizer, scheduler, scaler = setup_mae_training(model.parameters(), optimizer_params, device)

    start_epoch = 0
    best_loss = float('inf')

    # Resume logic (simplified, adjust as needed)
    if resume and os.path.isfile(resume):
        print(f"Resuming from checkpoint: {resume}")
        checkpoint = torch.load(resume, map_location='cpu')
        # Strict=False allows resuming even if model architecture changed slightly (e.g., decoder size)
        model.load_state_dict(checkpoint['model_state_dict'], strict=False)
        optimizer.load_state_dict(checkpoint['optimizer_state_dict'])
        scheduler.load_state_dict(checkpoint['scheduler_state_dict'])
        start_epoch = checkpoint['epoch']
        best_loss = checkpoint.get('loss', float('inf'))
        # Load scaler state if resuming and using AMP
        if use_amp and 'scaler_state_dict' in checkpoint:
            scaler.load_state_dict(checkpoint['scaler_state_dict'])
        print(f"Resumed from epoch {start_epoch}")

    # Training loop
    for epoch in range(start_epoch, epochs):
        print(f"\nEpoch {epoch+1}/{epochs}:")
        avg_loss = train_epoch_mae(
            model, train_loader, optimizer, scaler, device, writer, epoch,
            total_epochs=epochs, base_lr=lr, warmup_epochs=warmup_epochs,
            gradient_accumulation_steps=gradient_accumulation_steps, # Pass grad accum steps
            use_amp=use_amp
        )

        # Step the scheduler only *after* the warmup phase
        if epoch >= warmup_epochs:
            scheduler.step()

        print(f"Epoch {epoch+1}/{epochs} completed. Average Loss: {avg_loss:.6f}, LR: {scheduler.get_last_lr()[0]:.6f}")
        writer.add_scalar('Loss/train_epoch', avg_loss, epoch)
        writer.add_scalar('LR_epoch', scheduler.get_last_lr()[0], epoch)

        # Save checkpoint logic
        is_best = avg_loss < best_loss
        if is_best:
            best_loss = avg_loss

        if (epoch + 1) % save_interval == 0 or is_best:
             checkpoint_data = {
                 'epoch': epoch + 1,
                 'model_state_dict': model.state_dict(), # Save full model state
                 'optimizer_state_dict': optimizer.state_dict(),
                 'scheduler_state_dict': scheduler.state_dict(),
                 'scaler_state_dict': scaler.state_dict() if use_amp else None, # Save scaler state if using AMP
                 'loss': avg_loss,
                 'args': { # Save key parameters including warmup
                     'img_size': img_size, 'patch_size': patch_size,
                     'model_embed_dim': model_embed_dim, 'model_depth': model_depth,
                     'decoder_embed_dim': decoder_embed_dim, 'decoder_depth': decoder_depth,
                     'lr': lr, 'warmup_epochs': warmup_epochs, 'epochs': epochs, # Save training params
                     'norm_pix_loss': model.norm_pix_loss # Add norm_pix_loss from the model instance
                 }
             }
             save_path = f"{checkpoint_dir}/checkpoint_epoch{epoch+1}.pth"
             torch.save(checkpoint_data, save_path)
             print(f"Saved checkpoint: {save_path}")
             if is_best:
                 best_path = f"{checkpoint_dir}/best_model.pth"
                 torch.save(checkpoint_data, best_path)
                 print(f"Saved best model to: {best_path} with loss: {best_loss:.6f}")


    writer.close()
    print("MAE Pretraining completed!")


# --- Main execution block (Example) ---
if __name__ == '__main__':
    # Example usage for MAE pretraining
    # Adjust parameters as needed based on GPU memory and desired setup
    # Define arguments using argparse for better control
    import argparse
    parser = argparse.ArgumentParser('MAE pretraining script', add_help=False)
    parser.add_argument('--batch_size', default=64, type=int, help='Batch size per GPU')
    parser.add_argument('--epochs', default=200, type=int)
    parser.add_argument('--lr', type=float, default=1.5e-4, help='base learning rate') # Clarified base LR
    parser.add_argument('--warmup_epochs', type=int, default=40, help='epochs to warmup LR') # Added warmup_epochs
    parser.add_argument('--weight_decay', type=float, default=0.05)
    parser.add_argument('--use_amp', action='store_true', default=True, help='Use mixed precision') # Changed default to True

    parser.add_argument('--img_size', default=256, type=int, help='images input size')
    parser.add_argument('--patch_size', default=32, type=int) # Changed patch_size default to 32
    parser.add_argument('--model_embed_dim', default=768, type=int)
    parser.add_argument('--model_depth', default=12, type=int)
    parser.add_argument('--model_num_heads', default=12, type=int)
    parser.add_argument('--decoder_embed_dim', default=512, type=int)
    parser.add_argument('--decoder_depth', default=8, type=int)
    parser.add_argument('--decoder_num_heads', default=16, type=int)

    parser.add_argument('--data_dir', default='E:/vscode/2号CT数据', type=str, help='dataset path') # Updated path
    parser.add_argument('--num_workers', default=8, type=int) # Increased num_workers to 8
    parser.add_argument('--checkpoint_dir', default='checkpoints/mae_pretrain', help='path where to save checkpoints')
    parser.add_argument('--log_dir', default='logs/mae_pretrain', help='path where to tensorboard log')
    parser.add_argument('--save_interval', default=20, type=int, help='Save checkpoint every x epochs') # Reverted default to 20
    parser.add_argument('--resume', default='', help='resume from checkpoint')
    # Add arguments for clipping
    parser.add_argument('--clip_min', type=float, default=-1000.0, help='Minimum HU value for clipping')
    parser.add_argument('--clip_max', type=float, default=1000.0, help='Maximum HU value for clipping')
    # Add argument for gradient accumulation
    parser.add_argument('--gradient_accumulation_steps', type=int, default=1, help='Number of steps to accumulate gradients before updating weights')

    args = parser.parse_args()

    pretrain_mae(
        data_dir=args.data_dir,
        batch_size=args.batch_size,
        num_workers=args.num_workers,
        epochs=args.epochs,
        lr=args.lr, # Base LR
        warmup_epochs=args.warmup_epochs, # Pass warmup epochs
        weight_decay=args.weight_decay,
        use_amp=args.use_amp,
        gradient_accumulation_steps=args.gradient_accumulation_steps, # Pass grad accum steps
        img_size=args.img_size,
        patch_size=args.patch_size,
        model_embed_dim=args.model_embed_dim,
        model_depth=args.model_depth,
        model_num_heads=args.model_num_heads,
        decoder_embed_dim=args.decoder_embed_dim,
        decoder_depth=args.decoder_depth,
        decoder_num_heads=args.decoder_num_heads, # Corrected indentation
        clip_min=args.clip_min, # Pass clipping args
        clip_max=args.clip_max,
        checkpoint_dir=args.checkpoint_dir,
        log_dir=args.log_dir, # Corrected indentation
        save_interval=args.save_interval,
        resume=args.resume
    )

    # --- Old code (Contrastive/Supervised) is removed below this line ---
    # (Keep the old functions if needed for reference, but the main block now calls MAE)
