# Accurate BSRGAN Configuration for Conditional Diffusion Super-Resolution Training
# Uses the fixed BSRGAN degradation process

# --- Data Configuration ---
data:
  lr_dir: 'data/2号CT数据'
  hr_dir: 'data/3号CT数据'
  patch_size_hr: 128
  scale_factor: 4
  
  # 降质方法选择
  degradation_method: 'accurate_bsrgan'  # 'accurate_bsrgan', 'original_bsrgan', 'simple'

  # Accurate BSRGAN Degradation Parameters
  blur_prob: 0.8                        # 80%概率应用模糊
  noise_prob: 0.8                       # 80%概率应用噪声
  downsample_prob: 1.0                   # 100%概率下采样（必须）
  jpeg_prob: 0.3                        # 30%概率JPEG压缩模拟
  
  # 模糊参数
  blur_kernel_size: 21
  blur_sigma_range: [0.2, 3.0]
  aniso_prob: 0.5                       # 各向异性模糊概率
  
  # 下采样参数
  downsample_methods: ['bicubic', 'bilinear', 'area', 'nearest']
  
  # 噪声参数
  noise_gaussian_sigma_range: [1, 30]    # 高斯噪声范围
  
  # JPEG压缩参数
  jpeg_quality_range: [30, 95]

# --- Model Configuration ---
model:
  in_channels: 1
  out_channels: 1
  base_channels: 128
  channel_mults: [1, 2, 2, 4]
  attention_resolutions: [16, 8]
  num_res_blocks: 2
  dropout: 0.1

  # Pretrained Encoder Settings (disabled for baseline)
  use_pretrained_encoder: False
  encoder_type: "Swin-MAE"
  encoder_checkpoint: null
  condition_method: 'CrossAttention'
  num_heads: 8

# --- Discriminator Configuration (Disabled) ---
discriminator:
  ndf: 64
  n_layers: 3
  lr: 1.0e-4

# --- Diffusion Process Configuration ---
diffusion:
  schedule_name: 'cosine'
  timesteps: 1000

# --- Training Configuration (No GAN) ---
training:
  log_root: './logs/sr_diffusion'
  checkpoint_root: './checkpoints/sr_diffusion'
  learning_rate: 1.0e-4
  weight_decay: 1.0e-4
  batch_size: 8
  epochs: 200

  # Optimized Loss Weights (No GAN)
  diffusion_loss_type: 'l1'
  perceptual_loss_weight: 0.15          # 感知损失权重
  perceptual_loss_type: 'l1'
  ssim_loss_weight: 0.25                # SSIM损失权重
  gradient_loss_weight: 0.15            # 梯度损失权重

  # GAN Disabled
  use_gan: False
  gan_loss_weight: 0.0

  # Training Optimization
  use_amp: True
  seed: 42
  num_workers: 4
  log_interval: 50                      # 减少日志频率
  save_interval: 10

  # Learning Rate Scheduling
  use_scheduler: True
  scheduler_type: 'cosine'
  warmup_epochs: 20

  # Gradient Optimization
  grad_clip_norm: 1.0
  gradient_accumulation_steps: 2

# --- Inference Configuration ---
inference:
  sampling_steps: 50
  eta: 0.0

# --- Experiment Tracking ---
experiment:
  name: "Accurate_BSRGAN_Baseline_v1"
  description: "Baseline diffusion SR with accurate BSRGAN degradation (fixed size issues)"
  tags: ["diffusion", "super-resolution", "accurate-bsrgan", "ct-images", "fixed-degradation"]
