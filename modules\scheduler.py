import math
import torch
from torch.optim.lr_scheduler import _LRScheduler

class WarmupCosineScheduler(_LRScheduler):
    """
    带有线性预热的余弦退火学习率调度器
    
    参数:
        optimizer (Optimizer): 要调度的优化器
        warmup_epochs (int): 预热阶段的epoch数量
        total_epochs (int): 总的训练epoch数量
        min_lr (float, optional): 最小学习率，默认为初始学习率的1%
        warmup_start_lr (float, optional): 预热阶段的起始学习率，默认为初始学习率的1%
        last_epoch (int, optional): 上一个epoch的索引，默认为-1
    """
    
    def __init__(self, optimizer, warmup_epochs, total_epochs, min_lr=None, 
                 warmup_start_lr=None, last_epoch=-1):
        self.warmup_epochs = warmup_epochs
        self.total_epochs = total_epochs
        self.min_lr = min_lr
        self.warmup_start_lr = warmup_start_lr
        super(WarmupCosineScheduler, self).__init__(optimizer, last_epoch)
    
    def get_lr(self):
        if self.last_epoch < 0:
            return [group['lr'] for group in self.optimizer.param_groups]
        
        if self.warmup_epochs == 0:
            # 没有预热阶段，直接使用余弦退火
            return self._cosine_annealing()
        
        if self.last_epoch < self.warmup_epochs:
            # 预热阶段使用线性预热
            return self._linear_warmup()
        else:
            # 预热后使用余弦退火
            return self._cosine_annealing()
    
    def _linear_warmup(self):
        """
        线性预热阶段的学习率计算
        """
        lrs = []
        for base_lr in self.base_lrs:
            start_lr = self.warmup_start_lr or base_lr * 0.01
            lr = start_lr + (base_lr - start_lr) * (self.last_epoch / self.warmup_epochs)
            lrs.append(lr)
        return lrs
    
    def _cosine_annealing(self):
        """
        余弦退火阶段的学习率计算
        """
        lrs = []
        for base_lr in self.base_lrs:
            min_lr = self.min_lr or base_lr * 0.01
            # 计算余弦退火的进度，从预热结束到总epoch
            if self.total_epochs > self.warmup_epochs:
                progress = (self.last_epoch - self.warmup_epochs) / (self.total_epochs - self.warmup_epochs)
                progress = min(max(0, progress), 1)  # 确保进度在[0, 1]范围内
                cosine_decay = 0.5 * (1 + math.cos(math.pi * progress))
            else:
                # 如果总epochs等于warmup_epochs，直接使用最小学习率
                cosine_decay = 0
            lr = min_lr + (base_lr - min_lr) * cosine_decay
            lrs.append(lr)
        return lrs
