import torch
import torch.nn as nn
import torch.nn.functional as F
import torch.optim as optim
from torch.optim.lr_scheduler import CosineAnnealingLR
from data.mae_loader import get_mae_loader # Import loader
import timm # Import timm
assert timm.__version__ >= "0.3.2" # MAE requires timm support, assert version
# Use timm's Block for ViT-style decoder
from timm.models.vision_transformer import Block # Changed to ViT Block
from timm.models.layers import DropPath, Mlp
from tqdm import tqdm
import os
import numpy as np
import matplotlib.pyplot as plt
from torch.utils.tensorboard import SummaryWriter
import math
import random
from util.pos_embed import get_2d_sincos_pos_embed # Assuming pos embed helper is in util
import torchvision.models as models # Added for ResNet
import torchvision.transforms as transforms # Added for ResNet
import argparse # Added for argparse
from pytorch_msssim import ssim, ms_ssim # Import SSIM and MS-SSIM for structural similarity loss

# --- Random seed control function ---
def set_seed(seed=42):
    """设置所有随机种子以确保可重复性"""
    random.seed(seed)
    np.random.seed(seed)
    torch.manual_seed(seed)
    if torch.cuda.is_available():
        torch.cuda.manual_seed(seed)
        torch.cuda.manual_seed_all(seed)
    torch.backends.cudnn.deterministic = True
    torch.backends.cudnn.benchmark = False
    print(f"随机种子已设置为: {seed}")


# --------------------------------------------------------
# Enhanced Perceptual Loss using ResNet18 for CT images
# --------------------------------------------------------
class ResNetPerceptualLoss(nn.Module):
    def __init__(self, feature_layer_names=['layer2', 'layer3'], use_ct_norm=True, requires_grad=False): # Default to shallower layers, add norm flag
        super().__init__()
        # 加载预训练的ResNet18
        resnet = models.resnet18(weights=models.ResNet18_Weights.IMAGENET1K_V1)
        self.use_ct_norm = use_ct_norm # Flag for custom CT normalization

        # 修改第一层卷积以接受单通道输入
        original_conv = resnet.conv1
        resnet.conv1 = nn.Conv2d(1, 64, kernel_size=7, stride=2, padding=3, bias=False)

        # 初始化新的卷积层权重 - 使用原始权重的平均值
        with torch.no_grad():
            resnet.conv1.weight.data = original_conv.weight.data.mean(dim=1, keepdim=True)

        # 定义要提取的特征层 (using names now)
        self.feature_layer_names = feature_layer_names
        # Define weights based on the chosen layers (example weights, might need tuning)
        default_weights = {'layer1': 1.0, 'layer2': 0.8, 'layer3': 0.6, 'layer4': 0.4}
        self.layer_weights = [default_weights.get(name, 0.5) for name in self.feature_layer_names] # Get weights for specified layers

        # 提取特征层
        self.features = nn.ModuleDict()
        current_model = nn.Sequential()

        # 添加初始层
        current_model.add_module('conv1', resnet.conv1)
        current_model.add_module('bn1', resnet.bn1)
        current_model.add_module('relu', resnet.relu)
        current_model.add_module('maxpool', resnet.maxpool)

        # 添加ResNet的各个层 based on feature_layer_names
        if 'layer1' in self.feature_layer_names:
            self.features['layer1'] = nn.Sequential(current_model, resnet.layer1)
        # Always advance current_model regardless of whether the layer is used for loss
        current_model = nn.Sequential(current_model, resnet.layer1)

        if 'layer2' in self.feature_layer_names:
            self.features['layer2'] = nn.Sequential(current_model, resnet.layer2)
        current_model = nn.Sequential(current_model, resnet.layer2)

        if 'layer3' in self.feature_layer_names:
            self.features['layer3'] = nn.Sequential(current_model, resnet.layer3)
        current_model = nn.Sequential(current_model, resnet.layer3)

        if 'layer4' in self.feature_layer_names:
            self.features['layer4'] = nn.Sequential(current_model, resnet.layer4)
        # current_model = nn.Sequential(current_model, resnet.layer4) # Not needed if layer4 is the last

        if not self.features:
             raise ValueError(f"No valid ResNet feature layers specified in {self.feature_layer_names}")

        # 冻结参数
        if not requires_grad:
            for param in self.parameters():
                param.requires_grad = False

        self.eval()  # 设置为评估模式
        self.criterion = nn.L1Loss(reduction='mean')
        print(f"Initialized ResNetPerceptualLoss using layers: {self.feature_layer_names} with weights {self.layer_weights}. CT norm: {self.use_ct_norm}")

    def _normalize_ct(self, x):
        """归一化CT图像以适应ResNet (Instance-wise MinMax after [0,1] scaling)
        Input: Tensor in range [-1, 1]
        Output: Tensor in range [0, 1] (instance normalized)
        """
        # 从[-1, 1]转换到[0, 1]
        x = (x + 1.0) / 2.0

        # 应用CT图像特定的对比度增强 (Instance-wise Min-Max scaling)
        x_min = x.min(dim=2, keepdim=True)[0].min(dim=3, keepdim=True)[0]
        x_max = x.max(dim=2, keepdim=True)[0].max(dim=3, keepdim=True)[0]
        denominator = x_max - x_min
        # Avoid division by zero for blank images/patches
        x_norm = torch.where(denominator > 1e-8, (x - x_min) / denominator, torch.zeros_like(x))

        return x_norm

    def forward(self, x, y, mask=None):
        """计算感知损失
        Args:
            x, y: 输入图像 [N, 1, H, W] in range [-1, 1]
            mask: 可选掩码 [N, 1, H, W]，1表示掩码区域
        """
        # Apply normalization if enabled
        if self.use_ct_norm:
            x = self._normalize_ct(x)
            y = self._normalize_ct(y)
        else:
            # If not using CT norm, just scale to [0, 1] as ResNet expects non-negative input
            x = (x + 1.0) / 2.0
            y = (y + 1.0) / 2.0

        total_loss = 0.0

        # 计算每个特征层的损失
        for i, layer_name in enumerate(self.feature_layer_names): # Use names here
            layer = self.features[layer_name]
            x_feat = layer(x)
            y_feat = layer(y)

            # 应用掩码（如果提供）
            if mask is not None:
                # 调整掩码大小以匹配特征图
                mask_resized = F.interpolate(mask, size=x_feat.shape[2:], mode='nearest')
                # 计算掩码区域的损失
                layer_loss = self.criterion(x_feat * mask_resized, y_feat * mask_resized)
            else:
                layer_loss = self.criterion(x_feat, y_feat)

            # 应用层权重
            weight = self.layer_weights[i]
            total_loss += weight * layer_loss

        return total_loss


# --------------------------------------------------------
# PatchNCE Loss Implementation
# --------------------------------------------------------
class AdaptivePatchNCELoss(nn.Module):
    """
    自适应PatchNCE损失实现

    这是一种对比损失，用于使重建的patch特征与原始patch特征更加相似，
    同时与其他patch特征保持不同。这有助于学习更有区分性的特征表示。

    自适应版本会根据GPU内存情况动态调整采样数量，尽可能接近真实掩码数量。
    """
    def __init__(self, batch_size=0, nce_T=0.07, initial_sample_ratio=1.0, min_sample_ratio=0.1, step=0.1):
        """
        初始化自适应PatchNCE损失

        Args:
            batch_size: 批量大小（可动态设置）
            nce_T: 温度参数，控制softmax的平滑程度
            initial_sample_ratio: 初始采样比例，默认为1.0（全部使用）
            min_sample_ratio: 最小采样比例，默认为0.1（至少使用10%）
            step: 每次降低的步长，默认为0.1（每次降低10%）
        """
        super().__init__()
        self.batch_size = batch_size
        self.nce_T = nce_T
        self.cross_entropy_loss = torch.nn.CrossEntropyLoss(reduction='mean')

        # 自适应采样参数
        self.initial_sample_ratio = initial_sample_ratio
        self.current_sample_ratio = initial_sample_ratio
        self.min_sample_ratio = min_sample_ratio
        self.step = step
        self.adaptation_done = False

    def forward(self, feat_q, feat_k):
        """
        计算自适应PatchNCE损失

        Args:
            feat_q: 查询特征（从重建patch投影）[B, Num_masked, C_proj]
            feat_k: 键特征（从原始patch投影）[B, Num_masked, C_proj]

        假设feat_q和feat_k在patch级别上一一对应，并且已经针对掩码区域进行了选择。
        """
        if feat_q is None or feat_k is None or feat_q.numel() == 0 or feat_k.numel() == 0:
            return torch.tensor(0.0, device=feat_q.device if feat_q is not None else 'cpu')

        # 获取特征维度
        batch_size, num_patches, dim = feat_q.shape
        total_patches = batch_size * num_patches

        # 如果已经完成适应，直接使用当前采样比例
        if self.adaptation_done:
            return self._forward_with_sampling(feat_q, feat_k, self.current_sample_ratio)

        # 自适应采样过程
        while self.current_sample_ratio >= self.min_sample_ratio:
            try:
                # 尝试使用当前采样比例
                loss = self._forward_with_sampling(feat_q, feat_k, self.current_sample_ratio)
                # 成功，标记适应完成
                self.adaptation_done = True
                print(f"PatchNCE using {self.current_sample_ratio*100:.1f}% of masked patches ({int(total_patches*self.current_sample_ratio)} / {total_patches})")
                return loss
            except RuntimeError as e:
                # 检查是否是内存不足错误
                if "CUDA out of memory" in str(e):
                    # 降低采样比例
                    self.current_sample_ratio -= self.step
                    # 清除缓存
                    torch.cuda.empty_cache()
                    print(f"CUDA OOM, reducing sample ratio to {self.current_sample_ratio:.2f}")
                else:
                    # 其他错误，直接抛出
                    raise e

        # 如果所有采样比例都失败，使用最小采样比例
        self.current_sample_ratio = self.min_sample_ratio
        self.adaptation_done = True
        print(f"Using minimum sample ratio: {self.min_sample_ratio:.2f}")
        return self._forward_with_sampling(feat_q, feat_k, self.min_sample_ratio)

    def _forward_with_sampling(self, feat_q, feat_k, sample_ratio):
        """
        使用指定采样比例计算PatchNCE损失

        Args:
            feat_q: 查询特征 [B, Num_masked, C_proj]
            feat_k: 键特征 [B, Num_masked, C_proj]
            sample_ratio: 采样比例

        Returns:
            loss: PatchNCE损失
        """
        # 获取特征维度
        batch_size, num_patches, dim = feat_q.shape

        # 计算采样数量
        num_samples = max(1, int(num_patches * sample_ratio))

        # 如果需要采样
        if num_samples < num_patches:
            # 随机采样
            indices = torch.randperm(num_patches, device=feat_q.device)[:num_samples]
            feat_q = feat_q[:, indices, :]
            feat_k = feat_k[:, indices, :]
            num_patches = num_samples

        # 展平特征以便计算相似度
        feat_q = feat_q.reshape(-1, dim)  # [batch_size*num_patches, dim]
        feat_k = feat_k.reshape(-1, dim)  # [batch_size*num_patches, dim]

        # 归一化特征
        q = F.normalize(feat_q, dim=1)
        k = F.normalize(feat_k, dim=1)

        # 正样本对的相似度: (N*L_masked) x 1
        l_pos = torch.einsum('nc,nc->n', [q, k]).unsqueeze(-1)

        # 计算相似度矩阵
        logits_all = torch.mm(q, k.t()) / self.nce_T  # [batch_size*num_patches, batch_size*num_patches]

        # 创建身份矩阵以区分正负样本
        identity = torch.eye(batch_size * num_patches, dtype=torch.bool, device=q.device)

        # 提取负样本的相似度
        l_neg = torch.masked_select(logits_all, ~identity).reshape(batch_size * num_patches, -1)

        # 组合正样本和负样本的相似度
        logits = torch.cat([l_pos, l_neg], dim=1)  # [batch_size*num_patches, 1+batch_size*num_patches-1]

        # 标签: 正样本是第0类
        labels = torch.zeros(logits.shape[0], dtype=torch.long, device=logits.device)

        # 计算交叉熵损失
        loss = self.cross_entropy_loss(logits, labels)
        return loss

# 为了向后兼容，保留原始类名
class PatchNCELoss(AdaptivePatchNCELoss):
    def __init__(self, batch_size=0, nce_T=0.07):
        super().__init__(batch_size=batch_size, nce_T=nce_T)

# --------------------------------------------------------
# Helper functions for masking and position embeddings
# --------------------------------------------------------
def interpolate_pos_encoding_2d(pos_embed_original, current_grid_H, current_grid_W, target_grid_H, target_grid_W, dim):
    """
    对2D位置编码进行插值，用于生成不同分辨率的位置编码

    Args:
        pos_embed_original: 原始位置编码 [1, H_orig*W_orig, dim]
        current_grid_H, current_grid_W: 原始位置编码对应的网格尺寸
        target_grid_H, target_grid_W: 目标网格尺寸
        dim: 编码维度

    Returns:
        插值后的位置编码 [1, target_grid_H*target_grid_W, dim]
    """
    N = pos_embed_original.shape[0]  # 应该为1
    L_original = pos_embed_original.shape[1]
    assert L_original == current_grid_H * current_grid_W, f"位置编码长度 {L_original} 与网格尺寸 {current_grid_H}x{current_grid_W} 不匹配"
    assert N == 1, "interpolate_pos_encoding_2d 期望 N=1"

    # 将位置编码重塑为空间形式 [1, dim, H, W]
    pos_embed_reshaped = pos_embed_original.reshape(N, current_grid_H, current_grid_W, dim).permute(0, 3, 1, 2)

    # 使用双三次插值进行上采样/下采样
    pos_embed_interpolated = F.interpolate(
        pos_embed_reshaped,
        size=(target_grid_H, target_grid_W),
        mode='bicubic',
        align_corners=False
    )

    # 重塑回序列形式 [1, target_grid_H*target_grid_W, dim]
    return pos_embed_interpolated.permute(0, 2, 3, 1).reshape(N, target_grid_H * target_grid_W, dim)
def random_masking(N, L, mask_ratio, device):
    """
    执行随机掩码并返回必要的信息用于损失计算和解码器结构化输入

    注意：在当前实现中，编码器处理完整图像（不应用掩码），掩码仅用于损失计算。
    这种设计对CT图像特别有效，因为CT图像的结构信息分布均匀，需要全局上下文。

    Args:
        N: batch size
        L: 序列长度
        mask_ratio: 掩码比例
        device: 设备

    Returns:
        mask: 掩码标记 (1=masked, 0=visible)，已恢复到原始顺序
        ids_restore: 用于恢复原始顺序的索引
    """
    # 计算要保留的tokens数量
    len_keep = int(L * (1 - mask_ratio))

    # 为每个样本生成随机排列
    noise = torch.rand(N, L, device=device)  # 噪声用于随机排列
    ids_shuffle = torch.argsort(noise, dim=1)  # 升序排列，前len_keep为保留的tokens
    ids_restore = torch.argsort(ids_shuffle, dim=1)  # 恢复原始顺序的索引

    # 生成掩码 (在随机排列的顺序中)
    # 0 表示保留 (visible)，1 表示移除 (masked)
    binary_mask_shuffled = torch.ones([N, L], device=device)  # 初始化为全1 (全部掩码)
    binary_mask_shuffled[:, :len_keep] = 0  # 前len_keep个位置设为可见 (0)

    # 将掩码恢复到原始图像顺序
    mask = torch.gather(binary_mask_shuffled, dim=1, index=ids_restore)

    return mask, ids_restore  # 返回掩码和恢复索引


# Helper Modules for Hierarchical Decoder with Cross-Attention

# Basic Cross Attention Block
class CrossAttention(nn.Module):
    def __init__(self, dim, num_heads=8, qkv_bias=False, attn_drop=0., proj_drop=0.):
        super().__init__()
        assert dim % num_heads == 0, 'dim should be divisible by num_heads'
        self.num_heads = num_heads
        head_dim = dim // num_heads
        self.scale = head_dim ** -0.5

        self.q = nn.Linear(dim, dim, bias=qkv_bias)
        # Project K and V together from encoder features (context)
        self.kv = nn.Linear(dim, dim * 2, bias=qkv_bias)
        self.attn_drop = nn.Dropout(attn_drop)
        self.proj = nn.Linear(dim, dim)
        self.proj_drop = nn.Dropout(proj_drop)

    def forward(self, x_query, x_kv):
        # x_query: [B, N_q, C] (Decoder features - Query)
        # x_kv: [B, N_kv, C] (Encoder features - Key/Value context)
        B, N_q, C = x_query.shape
        B_kv, N_kv, C_kv = x_kv.shape # Get shape of context
        assert C == C_kv, "Query and Key/Value dimensions must match"

        q = self.q(x_query).reshape(B, N_q, self.num_heads, C // self.num_heads).permute(0, 2, 1, 3) # B, h, N_q, C/h
        kv = self.kv(x_kv).reshape(B_kv, N_kv, 2, self.num_heads, C // self.num_heads).permute(2, 0, 3, 1, 4) # 2, B, h, N_kv, C/h
        k, v = kv.unbind(0) # B, h, N_kv, C/h

        attn = (q @ k.transpose(-2, -1)) * self.scale # B, h, N_q, N_kv
        attn = attn.softmax(dim=-1)
        attn = self.attn_drop(attn)

        x = (attn @ v).transpose(1, 2).reshape(B, N_q, C) # B, N_q, C
        x = self.proj(x)
        x = self.proj_drop(x)
        return x

# Combined Cross-Attention and Self-Attention Block (inspired by Transformer decoders)
class DecoderCrossAttentionBlock(nn.Module):
    def __init__(self, dim, num_heads, mlp_ratio=4., qkv_bias=False, drop=0., attn_drop=0.,
                 drop_path=0., act_layer=nn.GELU, norm_layer=nn.LayerNorm):
        super().__init__()
        self.norm1_q = norm_layer(dim)
        self.norm1_kv = norm_layer(dim) # Norm for encoder features (Key/Value)
        self.cross_attn = CrossAttention(
            dim, num_heads=num_heads, qkv_bias=qkv_bias, attn_drop=attn_drop, proj_drop=drop)
        self.drop_path1 = DropPath(drop_path) if drop_path > 0. else nn.Identity()

        # Standard Self-Attention Block (using timm's Block)
        # Note: timm's Block applies norm *before* attention/mlp and includes residual connection
        self.self_attn_block = Block(
            dim=dim, num_heads=num_heads, mlp_ratio=mlp_ratio, qkv_bias=qkv_bias,
            proj_drop=drop, attn_drop=attn_drop, drop_path=drop_path, norm_layer=norm_layer, act_layer=act_layer
        )

    def forward(self, x_query, x_kv):
        # x_query: [B, N_q, C] (Decoder features)
        # x_kv: [B, N_kv, C] (Encoder features)

        # Cross-Attention part (Query attends to Key/Value from Encoder) + Residual
        x_query = x_query + self.drop_path1(self.cross_attn(self.norm1_q(x_query), self.norm1_kv(x_kv)))

        # Self-Attention + MLP part (using timm's Block, which includes residuals internally)
        x_query = self.self_attn_block(x_query)

        return x_query


class UpSampleBlock(nn.Module):
    """Upsampling block using ConvTranspose2d"""
    def __init__(self, in_channels, out_channels, scale_factor=2):
        super().__init__()
        # Ensure kernel_size and stride match scale_factor for simple upsampling
        self.conv_transpose = nn.ConvTranspose2d(in_channels, out_channels, kernel_size=scale_factor, stride=scale_factor)
        # Using GroupNorm as an alternative, potentially more stable than LayerNorm on spatial features
        self.norm = nn.GroupNorm(num_groups=max(1, out_channels // 32), num_channels=out_channels) # Example GroupNorm
        self.act = nn.GELU()

    def forward(self, x):
        # Input x expected shape: [N, C, H, W]
        x = self.conv_transpose(x)
        x = self.norm(x)
        x = self.act(x)
        return x


# Hierarchical Decoder (Inspired by U-Net and Hi-End-MAE concepts - Using Cross Attention)
class HierarchicalDecoder(nn.Module):
    def __init__(self, encoder_dims, decoder_embed_dim, decoder_depths, decoder_num_heads,
                mlp_ratio=4., qkv_bias=True, norm_layer=nn.LayerNorm, patch_size=4, in_chans=1,
                drop_path_rate=0., full_res_decoder_pos_embed=None, initial_grid_size=None):
        super().__init__()
        # 保存encoder_dims，确保它在整个类中可用
        self.encoder_dims = list(encoder_dims)  # 创建一个副本，避免引用问题
        print(f"HierarchicalDecoder.__init__: encoder_dims = {self.encoder_dims}")

        self.patch_size = patch_size
        self.in_chans = in_chans
        self.num_stages = len(self.encoder_dims) # Number of encoder stages providing features
        self.decoder_embed_dim = decoder_embed_dim # 存储解码器嵌入维度，用于位置编码检查

        # 计算最终的grid_size (用于上采样计算)
        # 这个值通常由MaskedAutoencoderSwin类传入，但我们在这里添加一个默认值
        # 在实际使用时，这个值会被MaskedAutoencoderSwin类中的self.grid_final覆盖
        self.grid_final = (8, 8)  # 默认值，实际使用时会被覆盖

        # 存储初始网格尺寸（全分辨率）
        self.initial_grid_size = initial_grid_size

        # Upsampling layers
        self.upsample_blocks = nn.ModuleList()
        # Decoder blocks (CrossAttention + SelfAttention + MLP) for each stage
        self.decoder_stages = nn.ModuleList()
        # Linear projections for encoder features (Key/Value context) to match decoder dimension at each stage
        self.encoder_projections = nn.ModuleList()

        current_dim = decoder_embed_dim # Start with the dimension after initial projection from latent_full

        # Stochastic depth decay rule
        dpr = [x.item() for x in torch.linspace(0, drop_path_rate, sum(decoder_depths))]
        block_idx = 0

        # Build decoder stages from deep to shallow
        for i in range(self.num_stages - 1, -1, -1): # Iterate encoder stages in reverse
            encoder_dim = encoder_dims[i]
            stage_depth = decoder_depths[i] # Depth for this decoder stage
            stage_heads = decoder_num_heads[i] # Heads for this decoder stage

            # 1. Upsampling Block (except for the deepest stage)
            if i < self.num_stages - 1:
                # Upsample from previous decoder stage dim to current stage dim
                # Keep dimension consistent during upsampling
                upsample = UpSampleBlock(in_channels=current_dim, out_channels=current_dim, scale_factor=2)
                self.upsample_blocks.insert(0, upsample) # Prepend to list
            else:
                 self.upsample_blocks.insert(0, nn.Identity()) # No upsampling for the first stage

            # 2. Encoder Feature Projection (for Key/Value context)
            # Project encoder feature dim to match the current decoder dimension
            encoder_proj = nn.Linear(encoder_dim, current_dim, bias=True) if encoder_dim != current_dim else nn.Identity()
            self.encoder_projections.insert(0, encoder_proj) # Prepend

            # 3. Decoder Stage Blocks (CrossAttention + SelfAttention)
            stage_blocks = nn.ModuleList()
            for j in range(stage_depth):
                 stage_blocks.append(
                     DecoderCrossAttentionBlock(
                         dim=current_dim, num_heads=stage_heads, mlp_ratio=mlp_ratio, qkv_bias=qkv_bias,
                         drop=0., attn_drop=0., drop_path=dpr[block_idx + j], norm_layer=norm_layer
                     )
                 )
            self.decoder_stages.insert(0, stage_blocks) # Prepend
            block_idx += stage_depth

            # Note: current_dim remains the same across stages in this design

        # Final prediction head
        self.decoder_norm = norm_layer(current_dim) # Norm after last stage
        self.decoder_pred = nn.Linear(current_dim, patch_size**2 * in_chans, bias=True)

        # 初始化多尺度位置编码
        # 这是一个进阶优化，为解码器的每个上采样阶段提供相应分辨率的位置编码
        # 与仅在最终全分辨率添加位置编码相比，这种方法可以在解码过程的每个阶段都提供位置信息
        # 有助于解码器更好地理解空间关系，提高重建质量
        self.multi_scale_pos_embeds = nn.ParameterDict()  # 使用ParameterDict存储不同尺度的位置编码

        if full_res_decoder_pos_embed is not None and initial_grid_size is not None:
            # 存储全分辨率位置编码
            self.multi_scale_pos_embeds['full_res'] = nn.Parameter(
                full_res_decoder_pos_embed.clone(), requires_grad=False
            )

            # 获取全分辨率网格尺寸
            if isinstance(initial_grid_size, tuple) and len(initial_grid_size) == 2:
                initial_grid_H, initial_grid_W = initial_grid_size
            else:
                # 如果没有提供明确的网格尺寸，尝试从位置编码推断
                L_initial = full_res_decoder_pos_embed.shape[1]
                initial_grid_H = initial_grid_W = int(math.sqrt(L_initial))
                assert initial_grid_H * initial_grid_W == L_initial, f"无法从位置编码长度 {L_initial} 推断出方形网格尺寸"

            # 为每个解码器阶段生成对应分辨率的位置编码
            # 从深层（最小分辨率）到浅层（最大分辨率）迭代
            temp_H, temp_W = self.grid_final  # 最深层（编码器输出）的分辨率

            for k in range(self.num_stages):
                # 使用更直观的分辨率作为键名，不依赖于循环索引
                res_key = f"res_{temp_H}x{temp_W}"

                if temp_H == initial_grid_H and temp_W == initial_grid_W:
                    # 这是全分辨率，直接使用已存储的全分辨率位置编码
                    self.multi_scale_pos_embeds[res_key] = nn.Parameter(
                        full_res_decoder_pos_embed.clone(), requires_grad=False
                    )
                    print(f"使用原始全分辨率位置编码: {res_key}")
                else:
                    # 从全分辨率位置编码插值生成当前分辨率的位置编码
                    interp_pos = interpolate_pos_encoding_2d(
                        full_res_decoder_pos_embed,  # 形状 [1, L_initial, D_decoder]
                        initial_grid_H, initial_grid_W,  # 全分辨率网格尺寸
                        temp_H, temp_W,  # 当前阶段的目标网格尺寸
                        decoder_embed_dim
                    )
                    self.multi_scale_pos_embeds[res_key] = nn.Parameter(interp_pos.clone(), requires_grad=False)
                    print(f"生成插值位置编码: {res_key}")

                # 对于下一个更浅层的阶段，分辨率翻倍（如果不是最浅层）
                if k < self.num_stages - 1:  # 不是最浅层
                    temp_H *= 2
                    temp_W *= 2

            print(f"初始化多尺度位置编码: {list(self.multi_scale_pos_embeds.keys())}")
        else:
            print("警告: 未提供全分辨率位置编码或初始网格尺寸，无法初始化多尺度位置编码")

        print(f"Initialized HierarchicalDecoder (Cross-Attention) with {self.num_stages} stages.")
        print(f"Decoder stage depths: {decoder_depths}")
        print(f"Decoder stage heads: {decoder_num_heads}")


    def forward(self, x_latent, encoder_features, decoder_pos_embed_full_res=None,
              mask_initial_resolution=None, decoder_mask_token_param=None):
        """
        层次化解码器前向传播

        改进说明：
        1. 解码器从编码器输出的粗糙特征开始，逐步上采样到全分辨率
        2. 在上采样过程中，通过跳跃连接融合编码器的中间特征
        3. 位置编码在特征达到全分辨率时添加，提供空间位置信息
        4. 这种设计更符合U-Net风格的层次化解码器工作方式，从粗糙到精细地重建特征
        5. 在全分辨率特征上，根据掩码信息有选择地应用decoder_mask_token，
           为被掩码区域提供额外的学习信号

        Args:
            x_latent: 解码器输入序列 [N, L_final, D_decoder]，编码器输出经过投影
            encoder_features: 编码器中间特征列表 [N, C, H, W]
            decoder_pos_embed_full_res: 解码器位置编码 [1, L_initial, D_decoder]，用于全分辨率特征
            mask_initial_resolution: 原始分辨率的掩码 [N, L_initial]，1表示掩码区域
            decoder_mask_token_param: 解码器掩码令牌 [1, 1, D_decoder]，用于替换掩码区域的特征

        Returns:
            pred: 解码器预测的patch值 [N, L_initial, patch_size^2*C]
        """
        # 获取输入维度
        N = x_latent.shape[0]  # 批量大小
        L_final = x_latent.shape[1]  # 编码器输出序列长度（粗糙分辨率）
        D_decoder = x_latent.shape[-1]  # 解码器特征维度

        # 记录初始特征长度，用于后续检查位置编码匹配
        initial_L = None  # 将在上采样到全分辨率时设置

        # 直接使用输入序列作为起点
        x = x_latent  # [N, L_final, D_decoder]

        # Decoder path (from deep to shallow)
        for i in range(self.num_stages):
            stage_blks = self.decoder_stages[i]
            upsample_blk = self.upsample_blocks[i]

            # 确定编码器特征和投影层的索引
            # 这个索引应该随着i从0到num_stages-1，从深层(num_stages-1)到浅层(0)
            current_processing_depth_idx = self.num_stages - 1 - i

            # 使用相同的索引获取投影层和编码器特征
            encoder_proj_blk = self.encoder_projections[current_processing_depth_idx]

            # 计算当前阶段的网格尺寸，用于多尺度位置编码
            if i == 0:
                # 第一阶段（最深层），使用self.grid_final
                current_grid_H, current_grid_W = self.grid_final
            else:
                # 后续阶段，尺寸是grid_final的2^i倍
                scale_factor = 2**i
                current_grid_H = self.grid_final[0] * scale_factor
                current_grid_W = self.grid_final[1] * scale_factor

            # 应用当前阶段的多尺度位置编码（在特征处理前）
            # 这是一个进阶优化，为解码器的每个上采样阶段提供相应分辨率的位置编码
            # 与仅在最终全分辨率添加位置编码相比，这种方法可以在解码过程的每个阶段都提供位置信息
            # 有助于解码器更好地理解空间关系，提高重建质量

            # 使用更直观的分辨率作为键名
            res_key = f"res_{current_grid_H}x{current_grid_W}"

            if hasattr(self, 'multi_scale_pos_embeds') and len(self.multi_scale_pos_embeds) > 0:
                if res_key in self.multi_scale_pos_embeds:
                    # 使用当前阶段对应分辨率的位置编码
                    pos_embed_this_stage = self.multi_scale_pos_embeds[res_key]
                    if x.shape[1] == pos_embed_this_stage.shape[1] and x.shape[2] == pos_embed_this_stage.shape[2]:
                        # 添加位置编码到当前特征
                        x = x + pos_embed_this_stage.to(x.dtype).to(x.device)
                        if random.random() < 0.01:  # 低频率日志
                            print(f"应用分辨率 {res_key} 的位置编码到阶段 {i}")
                    else:
                        print(f"警告: 分辨率 {res_key} 的位置编码维度不匹配. 特征: {x.shape[1:]}，位置编码: {pos_embed_this_stage.shape[1:]}")
                else:
                    # 如果找不到对应分辨率的位置编码，尝试使用全分辨率位置编码（如果适用）
                    if i == self.num_stages - 1 and 'res_{}x{}'.format(self.initial_grid_size[0], self.initial_grid_size[1]) in self.multi_scale_pos_embeds:
                        full_res_key = 'res_{}x{}'.format(self.initial_grid_size[0], self.initial_grid_size[1])
                        pos_embed_this_stage = self.multi_scale_pos_embeds[full_res_key]
                        if x.shape[1] == pos_embed_this_stage.shape[1] and x.shape[2] == pos_embed_this_stage.shape[2]:
                            x = x + pos_embed_this_stage.to(x.dtype).to(x.device)
                            if random.random() < 0.01:  # 低频率日志
                                print(f"应用全分辨率位置编码 {full_res_key} 到最终阶段 {i}")
                        else:
                            print(f"警告: 全分辨率位置编码维度不匹配. 特征: {x.shape[1:]}, 位置编码: {pos_embed_this_stage.shape[1:]}")
                    elif random.random() < 0.01:  # 低频率日志
                        print(f"未找到分辨率 {res_key} 的位置编码")

            # 1. Upsample (if not the first stage) - Operates on spatial, so reshape needed
            if not isinstance(upsample_blk, nn.Identity):
                # Calculate H, W of the feature map *before* upsampling
                # The spatial size doubles at each stage going from deep to shallow
                if i == 0:
                    # For the first upsampling stage, use self.grid_final directly
                    H_prev, W_prev = self.grid_final
                else:
                    # For subsequent stages, calculate based on previous upsampling
                    scale_factor = 2**i  # Each stage doubles the size
                    H_prev = self.grid_final[0] * scale_factor
                    W_prev = self.grid_final[1] * scale_factor

                # Get current dimensions
                C_prev = x.shape[-1]
                L_curr = x.shape[1]

                # Check if dimensions match
                if H_prev * W_prev != L_curr:
                    # If dimensions don't match, adjust spatial dimensions
                    H_prev = int(math.sqrt(L_curr))
                    W_prev = H_prev
                # Reshape sequence to spatial [N, C, H, W] before upsampling
                x = x.transpose(1, 2).reshape(N, C_prev, H_prev, W_prev)
                x = upsample_blk(x) # Upsamples spatially, dim remains current_dim
                # Reshape back to sequence [N, L_new, C_curr]
                N, C_curr, H_curr, W_curr = x.shape
                x = x.permute(0, 2, 3, 1).reshape(N, H_curr * W_curr, C_curr)

            # 2. Prepare Encoder Skip Feature (Project and Reshape to Sequence)
            # 使用之前计算的索引获取编码器特征
            skip_feature = encoder_features[current_processing_depth_idx] # Get feature [N, C_enc, H_enc, W_enc]
            N_skip, C_enc, H_enc, W_enc = skip_feature.shape
            skip_feature_seq = skip_feature.permute(0, 2, 3, 1).reshape(N_skip, H_enc * W_enc, C_enc) # -> [N, L_enc, C_enc]

            if isinstance(encoder_proj_blk, nn.Linear):
                 skip_feature_seq = encoder_proj_blk(skip_feature_seq) # -> [N, L_enc, C_curr]

            # 3. Apply Decoder Stage Blocks (CrossAttention + SelfAttention)
            # x is the query (decoder state), skip_feature_seq provides key/value context
            for blk in stage_blks:
                x = blk(x, skip_feature_seq) # Pass both query and kv context

        # Final processing after last stage
        # x 应该已经是序列格式 [N, L_initial, current_dim]，其中 L_initial 是原始分辨率的patch数量

        # 记录当前特征序列长度，用于检查位置编码匹配
        current_L = x.shape[1]

        # 在最终预测前添加位置编码（如果需要）
        # 注意：现在我们主要使用多尺度位置编码，在每个阶段都添加了位置编码
        # 只有在没有初始化多尺度位置编码的情况下，才使用传入的decoder_pos_embed_full_res
        if not hasattr(self, 'multi_scale_pos_embeds') and decoder_pos_embed_full_res is not None:
            # 检查位置编码与当前特征的维度是否匹配
            if current_L == decoder_pos_embed_full_res.shape[1] and x.shape[2] == decoder_pos_embed_full_res.shape[2]:
                # 添加位置编码
                x = x + decoder_pos_embed_full_res.to(x.dtype).to(x.device)
                # 低频率日志
                if random.random() < 0.01:
                    print(f"使用传入的位置编码（向后兼容模式）: 特征形状 {x.shape}")
            elif random.random() < 0.01:
                print(f"警告: 位置编码维度不匹配，未应用位置编码. 特征: {x.shape}, 位置编码: {decoder_pos_embed_full_res.shape}")

        # 在全分辨率特征上，根据掩码信息有选择地应用decoder_mask_token
        if decoder_mask_token_param is not None and mask_initial_resolution is not None:
            if current_L == mask_initial_resolution.shape[1]:  # 确保序列长度匹配
                # mask_initial_resolution是[N, L_initial]，1表示掩码区域
                masked_indices_bool = mask_initial_resolution.bool().unsqueeze(-1)  # [N, L_initial, 1]

                # 将decoder_mask_token_param扩展到匹配批量大小和patch数量
                # 它是[1, 1, D_decoder]，需要扩展为[N, L_initial, D_decoder]
                # 首先扩展到[1, L_initial, D_decoder]，通过重复单个token
                expanded_mask_tokens_single_batch = decoder_mask_token_param.expand(-1, x.shape[1], -1)
                # 然后扩展到批量大小N
                batch_mask_tokens = expanded_mask_tokens_single_batch.expand(N, -1, -1)

                # 使用torch.where选择性地替换掩码位置的特征
                x = torch.where(masked_indices_bool, batch_mask_tokens.type_as(x), x)

                # 打印调试信息（低频率）
                if random.random() < 0.01:
                    print(f"应用decoder_mask_token到{masked_indices_bool.sum().item()}个位置，占总位置的{masked_indices_bool.sum().item() / (N * current_L) * 100:.2f}%")
            else:
                print(f"警告: 掩码长度与特征长度不匹配. 特征: {current_L}, 掩码: {mask_initial_resolution.shape[1]}")

        # 应用最终的归一化
        x_normalized = self.decoder_norm(x)

        # 应用预测头
        pred = self.decoder_pred(x_normalized)  # [N, L_initial, patch_size^2 * C]

        # 返回预测结果和归一化后的特征（用于PatchNCE损失）
        return pred, x_normalized


# --------------------------------------------------------
# Based on MAE implementation by Meta Platforms, Inc. and Swin Transformer
# --------------------------------------------------------
class MaskedAutoencoderSwin(nn.Module):
    """ Masked Autoencoder with Swin Transformer backbone and Hierarchical Decoder
    """
    def __init__(self, img_size=256, patch_size=4, in_chans=1, # Swin uses patch_size=4 typically
                 embed_dim=96, depths=[2, 2, 6, 2], num_heads=[3, 6, 12, 24], # Swin-T defaults
                 window_size=7, mlp_ratio=4., qkv_bias=True, qk_scale=None,
                 drop_rate=0., attn_drop_rate=0., drop_path_rate=0.1,
                 norm_layer=nn.LayerNorm, ape=False, patch_norm=True, # Swin specific params
                 decoder_embed_dim=512, # Base dimension for decoder stages
                 decoder_depths=[1, 1, 1, 1], # Depth per decoder stage (default: 1 block per stage)
                 decoder_num_heads=[16, 8, 4, 2], # Heads per decoder stage (default: decreasing)
                 decoder_mlp_ratio=4., decoder_norm_layer=nn.LayerNorm, # Decoder specific norm/mlp
                 norm_pix_loss=False,
                 perceptual_loss_weight=0.01, # Add perceptual loss weight
                 perc_layers_resnet=['layer2', 'layer3'], # Add ResNet layers arg
                 perc_norm_ct_resnet=True, # Add ResNet norm flag arg
                 ssim_loss_weight=0.0, # Add SSIM loss weight
                 patchnce_loss_weight=0.0, # Add PatchNCE loss weight
                 nce_proj_dim=256, # Add NCE projector dimension
                 nce_T=0.07): # Add NCE temperature parameter
        super().__init__()

        self.patch_size = patch_size # Store patch size
        self.in_chans = in_chans # Store in_chans
        self.perceptual_loss_weight = perceptual_loss_weight # Store perceptual loss weight
        self.ssim_loss_weight = ssim_loss_weight # Store SSIM loss weight
        self.patchnce_loss_weight = patchnce_loss_weight # Store PatchNCE loss weight
        self.nce_T = nce_T # Store NCE temperature parameter
        self.nce_proj_dim = nce_proj_dim # Store NCE projector dimension

        # --------------------------------------------------------------------------
        # Swin MAE encoder specifics
        self.encoder = timm.create_model(
            'swin_tiny_patch4_window7_224', # Reverted to standard tiny model name
            pretrained=False,
            in_chans=in_chans,
            img_size=img_size, # Timm handles img_size mismatch from 224
            patch_size=patch_size, # Should be 4 for standard Swin
            embed_dim=embed_dim,
            depths=depths,
            num_heads=num_heads,
            window_size=window_size,
            mlp_ratio=mlp_ratio,
            qkv_bias=qkv_bias,
            qk_scale=qk_scale,
            drop_rate=drop_rate,
            attn_drop_rate=attn_drop_rate,
            drop_path_rate=drop_path_rate,
            norm_layer=norm_layer,
            ape=ape, # Absolute Position Embedding
            patch_norm=patch_norm,
            num_classes=0, # No head
            global_pool='' # No pooling
        )

        # --- MAE specific additions/modifications ---
        # Store encoder stage dimensions (needed for decoder skip connections)
        self.encoder_dims = [int(embed_dim * 2**i) for i in range(len(depths))]
        self.actual_encoder_output_dim = self.encoder_dims[-1]
        print(f"Encoder stage dims: {self.encoder_dims}")
        print(f"Final encoder output dim: {self.actual_encoder_output_dim}")

        self.patch_embed = self.encoder.patch_embed
        self.embed_dim = embed_dim # Store embed_dim

        # --- Use grid_size from patch_embed ---
        self.actual_grid_size = self.patch_embed.grid_size
        self.num_patches = self.actual_grid_size[0] * self.actual_grid_size[1]
        print(f"Using grid_size from patch_embed: {self.actual_grid_size}, num_patches: {self.num_patches}")

        # --- Calculate final grid size and patch count after encoder downsampling ---
        downsample_factor = 2**(len(depths) - 1) # Number of patch merging stages = len(depths) - 1
        self.grid_final = (self.actual_grid_size[0] // downsample_factor, self.actual_grid_size[1] // downsample_factor)
        self.num_patches_final = self.grid_final[0] * self.grid_final[1]
        print(f"Calculated final grid size after encoder: {self.grid_final}, num_patches_final: {self.num_patches_final}")


        # --- Absolute Position Embedding (APE) ---
        if ape:
             self.pos_embed = None # Set to None, as timm model handles APE internally when ape=True
             print("Using absolute positional embedding from Swin config (handled internally by timm model).")
        else:
            # Create APE if not provided by Swin config
            self.pos_embed = nn.Parameter(torch.zeros(1, self.num_patches, embed_dim)) # Use self.num_patches
            print("Created absolute positional embedding for Swin MAE.")
            # Initialize APE using sin-cos, use self.actual_grid_size[0]
            pos_embed_data = get_2d_sincos_pos_embed(self.pos_embed.shape[-1], self.actual_grid_size[0], cls_token=False)
            self.pos_embed.data.copy_(torch.from_numpy(pos_embed_data).float().unsqueeze(0))
            print("Initialized custom absolute positional embedding.")


        # --------------------------------------------------------------------------
        # MAE decoder specifics
        # Project encoder features to decoder dimension using the *actual* encoder output dim
        self.decoder_embed = nn.Linear(self.actual_encoder_output_dim, decoder_embed_dim, bias=True)

        # 添加decoder_mask_token - 用于表示被掩码区域的特殊token
        # 这个token将在解码器内部有选择地应用于被掩码的区域，为这些区域提供额外的学习信号
        # 它作为一种正则化机制，帮助模型区分"需要重点重建"的区域和"已有良好特征表示"的区域
        self.decoder_mask_token = nn.Parameter(torch.zeros(1, 1, decoder_embed_dim))
        # 使用截断正态分布初始化decoder_mask_token
        torch.nn.init.trunc_normal_(self.decoder_mask_token, std=.02)

        # 添加decoder_pos_embed - 为解码器提供位置信息
        self.decoder_pos_embed = nn.Parameter(torch.zeros(1, self.num_patches, decoder_embed_dim), requires_grad=False)
        # 初始化decoder_pos_embed使用sin-cos位置编码
        decoder_pos_embed_data = get_2d_sincos_pos_embed(decoder_embed_dim, int(self.num_patches**0.5), cls_token=False)
        self.decoder_pos_embed.data.copy_(torch.from_numpy(decoder_pos_embed_data).float().unsqueeze(0))

        # 注意：由于位置编码是固定的（requires_grad=False），不需要对其应用dropout
        # 我们保留self.decoder_pos_drop以保持向后兼容性，但在forward中不会使用它
        self.decoder_pos_drop = nn.Identity()

        print(f"Added decoder_mask_token and decoder_pos_embed for structured decoder input")

        # Instantiate the Hierarchical Decoder
        # Adjust decoder_num_heads to be compatible with decoder_embed_dim
        adjusted_decoder_num_heads = []
        for head_count in decoder_num_heads:
            if decoder_embed_dim % head_count != 0:
                # Find the closest divisor of decoder_embed_dim
                divisors = [i for i in range(1, decoder_embed_dim + 1) if decoder_embed_dim % i == 0]
                closest_divisor = min(divisors, key=lambda x: abs(x - head_count))
                print(f"Warning: decoder_num_heads value {head_count} is not compatible with decoder_embed_dim {decoder_embed_dim}.")
                print(f"Adjusting to {closest_divisor}.")
                adjusted_decoder_num_heads.append(closest_divisor)
            else:
                adjusted_decoder_num_heads.append(head_count)

        if adjusted_decoder_num_heads != decoder_num_heads:
            print(f"Adjusted decoder_num_heads: {adjusted_decoder_num_heads}")
            decoder_num_heads = adjusted_decoder_num_heads

        self.decoder = HierarchicalDecoder(
            encoder_dims=self.encoder_dims, # Pass encoder stage dims
            decoder_embed_dim=decoder_embed_dim,
            decoder_depths=decoder_depths, # Pass per-stage depths
            decoder_num_heads=decoder_num_heads, # Pass adjusted heads
            mlp_ratio=decoder_mlp_ratio,
            qkv_bias=qkv_bias,
            norm_layer=decoder_norm_layer,
            patch_size=patch_size,
            in_chans=in_chans,
            drop_path_rate=drop_path_rate, # Pass drop path rate to decoder
            # 传递全分辨率位置编码和初始网格尺寸，用于多尺度位置编码
            full_res_decoder_pos_embed=self.decoder_pos_embed,
            initial_grid_size=self.actual_grid_size
        )

        # 设置解码器的grid_final属性
        self.decoder.grid_final = self.grid_final
        print(f"Set decoder.grid_final to {self.grid_final}")

        self.norm_pix_loss = norm_pix_loss

        # Instantiate Enhanced Perceptual Loss using ResNet18
        if self.perceptual_loss_weight > 0:
             self.perceptual_loss = ResNetPerceptualLoss(
                 feature_layer_names=perc_layers_resnet,
                 use_ct_norm=perc_norm_ct_resnet,
                 requires_grad=False
             )
        else:
             self.perceptual_loss = None

        # Instantiate PatchNCE Loss and Projectors
        if self.patchnce_loss_weight > 0:
            # 创建自适应PatchNCE损失
            self.patchnce_loss = AdaptivePatchNCELoss(
                nce_T=self.nce_T,
                initial_sample_ratio=1.0,  # 从100%开始尝试
                min_sample_ratio=0.1,      # 最低接受10%的采样率
                step=0.1                   # 每次降低10%
            )

            # 创建投影器 - 用于将解码器特征投影到对比学习空间
            # 解码器特征投影器（用于重建的patch）
            self.nce_proj_pred = nn.Sequential(
                nn.Linear(decoder_embed_dim, self.nce_proj_dim),
                nn.ReLU(inplace=True),
                nn.Linear(self.nce_proj_dim, self.nce_proj_dim)
            )

            # 目标特征投影器（用于原始图像的patch）
            target_feat_dim = self.patch_size * self.patch_size * self.in_chans # 输入是原始patch像素
            self.nce_proj_target = nn.Sequential(
                nn.Linear(target_feat_dim, self.nce_proj_dim), # 输入维度是 p*p*C
                nn.ReLU(inplace=True),
                nn.Linear(self.nce_proj_dim, self.nce_proj_dim)
            )

            print(f"Initialized PatchNCE loss with temperature {self.nce_T} and projection dimension {self.nce_proj_dim}")
        else:
            self.patchnce_loss = None
            self.nce_proj_pred = None
            self.nce_proj_target = None

        self.initialize_weights() # Call init after defining all layers

    def initialize_weights(self):
        # Initialize APE if we created it (already done in __init__)

        # Initialize linear layers and layer norms
        self.apply(self._init_weights)

    def _init_weights(self, m):
        if isinstance(m, nn.Linear):
            torch.nn.init.xavier_uniform_(m.weight)
            if isinstance(m, nn.Linear) and m.bias is not None:
                nn.init.constant_(m.bias, 0)
        elif isinstance(m, nn.LayerNorm):
            nn.init.constant_(m.bias, 0)
            nn.init.constant_(m.weight, 1.0)

    # Modified forward_encoder: Collect intermediate features for hierarchical decoder and return mask/ids_restore
    def forward_encoder(self, x, mask_ratio=0.75):
        """
        编码器前向传播，收集中间特征并生成掩码信息

        改进说明：
        1. 与标准MAE不同，编码器处理完整图像（不应用掩码）
        2. 掩码仅用于损失计算，不影响编码器的特征提取过程
        3. 这种设计对CT图像特别有效，因为CT图像需要全局上下文来理解结构
        4. 编码器的中间特征被收集用于层次化解码器的跳跃连接

        Args:
            x: 输入图像 [N, C, H, W]
            mask_ratio: 掩码比例，默认0.75

        Returns:
            latent_full: 编码器输出的特征 [N, L_final, D_encoder_output]
            mask: 掩码标记 (1=masked, 0=visible) [N, L_initial]
            intermediate_features: 中间特征列表 [N, C, H, W]
            ids_restore: 用于恢复原始顺序的索引 [N, L_initial]
        """
        # x: [N, C, H, W]
        intermediate_features = [] # List to store features from each stage

        N, C, H, W = x.shape # Use N instead of B
        patch_size = self.patch_size # Use stored patch_size
        embed_dim = self.encoder.embed_dim # Use embed_dim from the loaded Swin model

        # 1. Patch Embedding
        x = self.patch_embed(x) # Output shape depends on timm version/config.
        # Ensure output is [N, L, D]
        if x.dim() == 4:
            x = x.flatten(1, 2) # -> [B, H*W, C] = [B, L, C]
        N, L, D = x.shape # Get L and D directly from the output shape

        # 2. Add Position Embedding (if applicable)
        if self.pos_embed is not None:
            if self.pos_embed.shape[1] != L:
                 raise ValueError(f"Position embedding length mismatch: Expected {L}, Got {self.pos_embed.shape[1]}")
            if self.pos_embed.shape[2] != D:
                 raise ValueError(f"Position embedding dim mismatch: Expected {D}, Got {self.pos_embed.shape[2]}")
            x = x + self.pos_embed # Add APE [N, L, D]

        # 3. 生成随机掩码
        mask, ids_restore = random_masking(N, L, mask_ratio, x.device)

        # 4. Pass the *unmasked* full sequence through Swin stages
        # 注意：与原始MAE不同，我们不对输入进行掩码，而是将完整序列送入编码器
        # 这样可以获得更好的特征表示，掩码仅用于损失计算
        H_grid, W_grid = self.actual_grid_size
        assert H_grid * W_grid == L, f"Stored grid size {H_grid}x{W_grid} does not match sequence length {L}"
        x_spatial = x.reshape(N, H_grid, W_grid, D) # Reshape to [N, H_grid, W_grid, D]

        # Pass through Swin layers and store intermediate outputs
        for i, layer in enumerate(self.encoder.layers):
            x_spatial = layer(x_spatial) # Output: [N, H_out, W_out, C_out]
            # Store the feature map for hierarchical decoder
            # Reshape to [N, C, H, W] for easier handling in decoder
            intermediate_features.append(x_spatial.permute(0, 3, 1, 2).contiguous())

        # Flatten back to sequence for final normalization
        N_out, H_out, W_out, C_out = x_spatial.shape
        x_seq = x_spatial.view(N_out, H_out * W_out, C_out) # Flatten to [N, L_final, C_out]

        # Apply final encoder normalization
        latent_full = self.encoder.norm(x_seq) # [N, L_final, D_encoder_output]

        assert latent_full.shape[1] == self.num_patches_final, \
            f"Encoder output sequence length mismatch: Expected {self.num_patches_final}, Got {latent_full.shape[1]}"
        assert latent_full.shape[-1] == self.actual_encoder_output_dim, \
             f"Encoder final output dim mismatch: Expected {self.actual_encoder_output_dim}, Got {latent_full.shape[-1]}"

        return latent_full, mask, intermediate_features, ids_restore # 返回编码器输出、掩码、中间特征和恢复索引

    # 简化的forward_decoder: 直接将编码器输出投影后传递给层次化解码器
    def forward_decoder(self, latent_full, intermediate_features, decoder_pos_embed_to_add=None,
                       mask_initial_res=None, decoder_mask_token_to_add=None):
        """
        解码器前向传播，简化实现

        Args:
            latent_full: 编码器输出的特征 [N, L_final, D_encoder_output]
            intermediate_features: 编码器中间特征列表
            decoder_pos_embed_to_add: 处理过的解码器位置编码 [1, L_initial, D_decoder]，
                                      将在层次化解码器内部添加到全分辨率特征
            mask_initial_res: 原始分辨率的掩码 [N, L_initial]，1表示掩码区域
            decoder_mask_token_to_add: 解码器掩码令牌 [1, 1, D_decoder]，用于替换掩码区域的特征

        Returns:
            pred: 解码器预测的patch值 [N, L_initial, patch_size^2*C]
            x_normalized: 归一化后的解码器特征 [N, L_initial, D_decoder]，用于PatchNCE损失
        """
        # 1. 投影编码器特征到解码器维度
        x_proj = self.decoder_embed(latent_full)  # [N, L_final, D_decoder]

        # 2. 直接将投影后的特征、位置编码、掩码和掩码令牌传递给层次化解码器
        # 层次化解码器将在特征上采样到全分辨率时添加位置编码，并在掩码位置应用掩码令牌
        # 现在decoder返回两个值：预测结果和归一化后的特征
        pred, x_normalized = self.decoder(x_proj, intermediate_features, decoder_pos_embed_to_add,
                                         mask_initial_res, decoder_mask_token_to_add)

        return pred, x_normalized

    def patchify(self, imgs):
        """ imgs: (N, C, H, W) -> (N, L, patch_size**2 * C) """
        p = self.patch_size
        assert imgs.shape[2] == imgs.shape[3] and imgs.shape[2] % p == 0
        h = w = imgs.shape[2] // p
        c = imgs.shape[1]
        x = imgs.reshape(shape=(imgs.shape[0], c, h, p, w, p))
        x = torch.einsum('nchpwq->nhwpqc', x)
        x = x.reshape(shape=(imgs.shape[0], h * w, p**2 * c))
        return x

    def unpatchify(self, x):
        """ x: (N, L, patch_size**2 * C) -> (N, C, H, W) """
        p = self.patch_size
        h, w = self.actual_grid_size # Use initial grid size for unpatchify
        assert h * w == x.shape[1], f"h*w ({h*w}) from grid_size does not match L ({x.shape[1]})"
        c = x.shape[2] // (p**2)
        assert x.shape[2] == p**2 * c, f"Decoder prediction dim {x.shape[2]} != p*p*C ({p**2 * c})"

        x = x.reshape(shape=(x.shape[0], h, w, p, p, c))
        x = torch.einsum('nhwpqc->nchpwq', x)
        imgs = x.reshape(shape=(x.shape[0], c, h * p, w * p))
        return imgs

    def forward_loss(self, imgs, pred, mask, x_normalized=None):
        """
        计算总损失，包括重建损失、感知损失、SSIM损失和PatchNCE损失

        Args:
            imgs: [N, C, H, W] - 原始图像
            pred: [N, L_initial, p*p*C] - 预测的patch值
            mask: [N, L_initial], 1表示掩码区域
            x_normalized: [N, L_initial, D_decoder] - 归一化后的解码器特征，用于PatchNCE损失
        """
        target = self.patchify(imgs) # Target shape: [N, L_initial, p*p*C]
        if self.norm_pix_loss:
            mean = target.mean(dim=-1, keepdim=True)
            var = target.var(dim=-1, keepdim=True)
            target = (target - mean) / (var + 1.e-6)**.5

        loss = (pred - target) ** 2
        loss = loss.mean(dim=-1)  # [N, L_initial], mean loss per patch

        mask_sum = mask.sum()
        if mask_sum == 0:
             # Avoid division by zero if the mask is empty
             print("Warning: mask_sum is zero in forward_loss.")
             reconstruction_loss = torch.tensor(0.0, device=pred.device, requires_grad=True)
        else:
             reconstruction_loss = (loss * mask).sum() / mask_sum # Mean loss on removed patches

        # Calculate Enhanced Perceptual Loss (only if weight > 0)
        perc_loss = torch.tensor(0.0, device=pred.device)
        pred_img = None  # Initialize to avoid unpatchifying twice

        if self.perceptual_loss is not None and self.perceptual_loss_weight > 0:
            # Ensure ResNet is on the correct device
            self.perceptual_loss = self.perceptual_loss.to(pred.device)

            # Unpatchify predictions and targets for ResNet input
            pred_img = self.unpatchify(pred)  # [N, C, H, W]

            # Create a mask image for focusing perceptual loss on masked regions
            mask_img = mask.reshape(-1, *self.actual_grid_size).unsqueeze(1).float()  # [N, 1, H_grid, W_grid]
            mask_img = F.interpolate(mask_img, size=(pred_img.shape[2], pred_img.shape[3]), mode='nearest')

            # Calculate perceptual loss focused on masked regions using ResNet18
            perc_loss = self.perceptual_loss(pred_img, imgs, mask=mask_img)

        # Calculate SSIM Loss (only if weight > 0)
        struct_loss = torch.tensor(0.0, device=pred.device)
        if self.ssim_loss_weight > 0:
            # SSIM expects input in range [0, 1] or [0, 255] etc.
            # Our loader outputs [-1, 1]. Scale to [0, 1] for SSIM.
            # Unpatchify if not already done for perceptual loss
            if pred_img is None:
                pred_img = self.unpatchify(pred)

            pred_img_scaled = (pred_img + 1.0) / 2.0
            imgs_scaled = (imgs + 1.0) / 2.0

            # Calculate SSIM (value is 0 to 1, higher is better)
            # Use data_range=1.0 because input is scaled to [0, 1]
            ssim_val = ssim(pred_img_scaled, imgs_scaled, data_range=1.0, size_average=True)

            # Convert SSIM value to loss (0 to 1, lower is better)
            struct_loss = 1.0 - ssim_val

        # 计算PatchNCE对比损失（仅当权重>0且提供了特征时）
        nce_loss = torch.tensor(0.0, device=pred.device)
        if self.patchnce_loss_weight > 0 and x_normalized is not None and self.patchnce_loss is not None:
            # 确保投影器在正确的设备上
            if self.nce_proj_pred is not None:
                self.nce_proj_pred = self.nce_proj_pred.to(pred.device)
                self.nce_proj_target = self.nce_proj_target.to(pred.device)

            # 获取原始图像的特征表示
            # 我们需要将原始图像转换为patch序列，然后提取特征
            # 这里我们使用patchify函数将图像转换为patch序列
            target_patches = self.patchify(imgs)  # [N, L_initial, p*p*C]

            # 创建一个与x_normalized相同形状的特征表示
            # 这里我们使用一个简单的线性投影，将target_patches投影到与x_normalized相同的特征空间
            # 在实际应用中，可能需要更复杂的特征提取方法
            N, L, _ = target_patches.shape

            # 只对掩码区域计算对比损失
            if mask_sum > 0:
                # 提取掩码区域的特征
                # 将mask展平为[N*L]
                mask_flat = mask.view(-1)  # [N*L]

                # 将特征展平为[N*L, D]
                x_normalized_flat = x_normalized.reshape(-1, x_normalized.shape[-1])  # [N*L, D_decoder]

                # 只选择掩码区域的特征
                x_masked = x_normalized_flat[mask_flat.bool()]  # [mask_sum, D_decoder]

                # 将target_patches展平为[N*L, p*p*C]
                target_patches_flat = target_patches.reshape(-1, target_patches.shape[-1])  # [N*L, p*p*C]

                # 只选择掩码区域的target_patches
                target_masked = target_patches_flat[mask_flat.bool()]  # [mask_sum, p*p*C]

                # 投影特征到对比学习空间
                # 对于预测特征，我们使用nce_proj_pred
                feat_q = self.nce_proj_pred(x_masked)  # [mask_sum, nce_proj_dim]

                # 修正 feat_k 的来源
                # 使用原始patch像素作为目标特征，而不是解码器特征
                feat_k = self.nce_proj_target(target_masked.float())  # [mask_sum, nce_proj_dim]

                # 计算PatchNCE损失
                # 将特征重塑为[1, num_patches, dim]格式，以适应AdaptivePatchNCELoss的接口
                feat_q_reshaped = feat_q.unsqueeze(0)  # [1, mask_sum, nce_proj_dim]
                feat_k_reshaped = feat_k.unsqueeze(0)  # [1, mask_sum, nce_proj_dim]
                nce_loss = self.patchnce_loss(feat_q_reshaped, feat_k_reshaped)

                # 如果nce_loss是NaN，则将其设为0
                if torch.isnan(nce_loss):
                    print("Warning: NaN detected in PatchNCE loss. Setting to 0.")
                    nce_loss = torch.tensor(0.0, device=pred.device)

        # 组合所有损失
        total_loss = (
            reconstruction_loss +
            self.perceptual_loss_weight * perc_loss +
            self.ssim_loss_weight * struct_loss +
            self.patchnce_loss_weight * nce_loss
        )

        # 打印各个损失的值（低频率）
        if random.random() < 0.01:  # 只有1%的概率打印日志，减少输出
            print(f"Losses: Recon={reconstruction_loss:.4f}, Perc={perc_loss:.4f}, SSIM={struct_loss:.4f}, NCE={nce_loss:.4f}")

        return total_loss # 返回总损失

    def forward(self, imgs, mask_ratio=0.75):
        """
        模型前向传播

        Args:
            imgs: 输入图像 [N, C, H, W]
            mask_ratio: 掩码比例，默认0.75

        Returns:
            loss: 总损失
            pred: 预测的patch值 [N, L_initial, patch_size^2*C]
            mask: 掩码标记 (1=masked, 0=visible) [N, L_initial]
        """
        # 1. 编码器：处理完整图像，获取最终特征、掩码信息、中间特征和恢复索引
        latent_full, mask, intermediate_features, ids_restore = self.forward_encoder(imgs, mask_ratio)

        # 2. 准备位置编码
        # 注意：由于位置编码是固定的（requires_grad=False），不需要对其应用dropout
        # 直接使用原始位置编码
        decoder_pos_embed_processed = self.decoder_pos_embed

        # 3. 解码器：将编码器输出、中间特征、位置编码、掩码和掩码令牌传递给解码器
        # 解码器将在全分辨率特征上有选择地应用掩码令牌，为被掩码区域提供额外的学习信号
        # 现在forward_decoder返回两个值：预测结果和归一化后的特征
        pred, x_normalized = self.forward_decoder(
            latent_full,
            intermediate_features,
            decoder_pos_embed_processed,
            mask,  # 传递原始分辨率的掩码
            self.decoder_mask_token  # 传递可学习的掩码令牌
        )

        # 4. 损失计算（包括重建损失、感知损失、SSIM损失和PatchNCE损失）
        loss = self.forward_loss(imgs, pred, mask, x_normalized)

        return loss, pred, mask # 返回损失、预测和掩码


# --- Training Function ---
def setup_mae_training(model_params, optimizer_params, device):
    """设置 MAE 训练的优化器和调度器"""
    lr = optimizer_params.get('lr', 1.5e-4) # Base LR
    weight_decay = optimizer_params.get('weight_decay', 0.05)
    optimizer = optim.AdamW(model_params, lr=lr, betas=(0.9, 0.95), weight_decay=weight_decay)
    # Cosine scheduler T_max depends on total epochs *after* warmup
    scheduler = CosineAnnealingLR(optimizer, T_max=optimizer_params['epochs_after_warmup'], eta_min=lr/100)
    scaler = torch.cuda.amp.GradScaler(enabled=optimizer_params.get('use_amp', True))
    return optimizer, scheduler, scaler

def train_epoch_mae(model, train_loader, optimizer, scaler, device, writer, epoch, total_epochs, base_lr, warmup_epochs, gradient_accumulation_steps=1, log_interval=50, use_amp=True):
    """MAE 训练循环 with warmup and gradient accumulation"""
    model.train()
    total_loss = 0
    num_batches = len(train_loader)
    processed_batches = 0

    with tqdm(train_loader, unit="batch") as pbar:
        for batch_idx, imgs in enumerate(pbar):
            if imgs is None or imgs.nelement() == 0:
                print(f"Skipping empty batch at index {batch_idx}")
                continue

            global_step = epoch * num_batches + batch_idx
            # Adjust learning rate using linear warmup
            if epoch < warmup_epochs:
                num_warmup_steps = warmup_epochs * num_batches
                if num_warmup_steps > 0:
                    current_step = global_step + 1
                    lr_scale = min(1.0, float(current_step) / num_warmup_steps)
                    new_lr = base_lr * lr_scale
                    for param_group in optimizer.param_groups:
                        param_group['lr'] = new_lr
            # After warmup, the scheduler (stepped per epoch) handles decay.

            imgs = imgs.to(device, non_blocking=True)

            # Only zero gradients when starting a new accumulation cycle
            if (batch_idx % gradient_accumulation_steps == 0) or (gradient_accumulation_steps == 1):
                optimizer.zero_grad()

            with torch.cuda.amp.autocast(enabled=use_amp):
                loss, _, _ = model(imgs, mask_ratio=0.75) # Default mask ratio
                # Normalize loss for accumulation
                loss = loss / gradient_accumulation_steps

            # Accumulate scaled loss
            scaler.scale(loss).backward()

            current_loss = loss.item() * gradient_accumulation_steps # Log the non-normalized loss
            if math.isnan(current_loss):
                print(f"Warning: NaN loss detected at epoch {epoch+1}, batch {batch_idx}. Skipping batch.")
                optimizer.zero_grad()
                continue

            # Only step optimizer at the end of accumulation cycle or if it's the last batch
            if ((batch_idx + 1) % gradient_accumulation_steps == 0) or (batch_idx + 1 == num_batches):
                scaler.step(optimizer)
                scaler.update()

            total_loss += current_loss
            processed_batches += 1
            pbar.set_postfix(loss=f"{current_loss:.4f}", lr=f"{optimizer.param_groups[0]['lr']:.1e}")

            if batch_idx % log_interval == 0:
                writer.add_scalar('Loss/batch', current_loss, global_step)
                writer.add_scalar('LR', optimizer.param_groups[0]['lr'], global_step)

    avg_loss = total_loss / processed_batches if processed_batches > 0 else 0
    return avg_loss


def pretrain_mae(args): # Pass args directly
    """Swin MAE 自监督预训练函数 with Hierarchical Decoder"""
    # 设置随机种子以确保可重复性
    set_seed(args.seed)

    device = torch.device('cuda' if torch.cuda.is_available() else 'cpu')
    print(f"Using device: {device}")
    print(f"Swin MAE Pretraining Parameters: {args}")

    os.makedirs(args.checkpoint_dir, exist_ok=True)
    os.makedirs(args.log_dir, exist_ok=True)
    writer = SummaryWriter(args.log_dir)

    # Instantiate Swin MAE model with Hierarchical Decoder
    model = MaskedAutoencoderSwin(
        img_size=args.img_size,
        patch_size=args.patch_size,
        in_chans=args.in_chans,
        embed_dim=args.swin_embed_dim, # Use Swin specific embed_dim
        depths=args.swin_depths,
        num_heads=args.swin_num_heads,
        window_size=args.swin_window_size,
        mlp_ratio=4.0, # Standard MLP ratio
        norm_layer=nn.LayerNorm,
        ape=args.swin_ape, # Absolute Position Embedding flag
        patch_norm=True, # Usually True for Swin
        decoder_embed_dim=args.decoder_embed_dim,
        decoder_depths=args.decoder_depths,
        decoder_num_heads=args.decoder_num_heads,
        norm_pix_loss=args.norm_pix_loss,
        perceptual_loss_weight=args.perceptual_loss_weight, # Pass perceptual loss weight
        perc_layers_resnet=args.perc_layers_resnet, # Pass ResNet layers
        perc_norm_ct_resnet=args.perc_norm_ct_resnet, # Pass ResNet norm flag
        ssim_loss_weight=args.ssim_loss_weight, # Pass SSIM loss weight
        patchnce_loss_weight=args.patchnce_loss_weight, # Pass PatchNCE loss weight
        nce_proj_dim=args.nce_proj_dim, # Pass NCE projector dimension
        nce_T=args.nce_T # Pass NCE temperature parameter
    ).to(device)

    print(f"Swin MAE Model Parameters: {sum(p.numel() for p in model.parameters() if p.requires_grad):,}")

    # Get MAE data loader
    train_loader = get_mae_loader(
        data_dir=args.data_dir,
        batch_size=args.batch_size,
        num_workers=args.num_workers,
        crop_size=args.img_size, # Crop size must match model img_size
        augment=True
    )

    # Setup optimizer and scheduler
    optimizer_params = {
        'lr': args.lr,
        'epochs_after_warmup': max(0, args.epochs - args.warmup_epochs), # Ensure non-negative T_max
        'weight_decay': args.weight_decay,
        'use_amp': args.use_amp
    }
    optimizer, scheduler, scaler = setup_mae_training(model.parameters(), optimizer_params, device)

    start_epoch = 0
    best_loss = float('inf')

    # Resume logic
    if args.resume and os.path.isfile(args.resume):
        print(f"Resuming from checkpoint: {args.resume}")
        checkpoint = torch.load(args.resume, map_location='cpu')
        msg = model.load_state_dict(checkpoint['model_state_dict'], strict=False)
        print(f"Model load_state_dict message: {msg}")
        if 'optimizer_state_dict' in checkpoint:
             optimizer.load_state_dict(checkpoint['optimizer_state_dict'])
        if 'scheduler_state_dict' in checkpoint and hasattr(scheduler, 'load_state_dict'):
             if scheduler.T_max == optimizer_params['epochs_after_warmup']:
                 scheduler.load_state_dict(checkpoint['scheduler_state_dict'])
             else:
                 print("Warning: Scheduler T_max mismatch, not loading scheduler state.")
        start_epoch = checkpoint.get('epoch', 0)
        best_loss = checkpoint.get('loss', float('inf'))
        if args.use_amp and 'scaler_state_dict' in checkpoint and checkpoint['scaler_state_dict'] is not None:
            scaler.load_state_dict(checkpoint['scaler_state_dict'])
        print(f"Resumed from epoch {start_epoch}")
        if 'scheduler_state_dict' in checkpoint and hasattr(scheduler, 'load_state_dict') and scheduler.T_max == optimizer_params['epochs_after_warmup']:
             scheduler.last_epoch = max(0, start_epoch - args.warmup_epochs) -1


    # Training loop
    for epoch in range(start_epoch, args.epochs):
        print(f"\nEpoch {epoch+1}/{args.epochs}:")
        avg_loss = train_epoch_mae(
            model, train_loader, optimizer, scaler, device, writer, epoch,
            total_epochs=args.epochs, base_lr=args.lr, warmup_epochs=args.warmup_epochs,
            gradient_accumulation_steps=args.gradient_accumulation_steps,
            use_amp=args.use_amp
        )

        if epoch >= args.warmup_epochs:
             if hasattr(scheduler, 'step'):
                 scheduler.step()
             else:
                 print(f"Warning: Scheduler {type(scheduler)} does not have step method.")


        current_lr = optimizer.param_groups[0]['lr']
        print(f"Epoch {epoch+1}/{args.epochs} completed. Average Loss: {avg_loss:.6f}, Current LR: {current_lr:.6f}")
        writer.add_scalar('Loss/train_epoch', avg_loss, epoch)
        writer.add_scalar('LR_epoch', current_lr, epoch)

        is_best = avg_loss < best_loss
        if is_best:
            best_loss = avg_loss
            print(f"New best loss: {best_loss:.6f}")

        if (epoch + 1) % args.save_interval == 0 or is_best:
             saved_args = {k: v for k, v in vars(args).items() if 'swin' in k or k in ['img_size', 'patch_size', 'decoder_embed_dim', 'decoder_depths', 'decoder_num_heads', 'lr', 'warmup_epochs', 'epochs', 'norm_pix_loss', 'seed']}
             checkpoint_data = {
                 'epoch': epoch + 1,
                 'model_state_dict': model.state_dict(),
                 'optimizer_state_dict': optimizer.state_dict(),
                 'scheduler_state_dict': scheduler.state_dict() if hasattr(scheduler, 'state_dict') else None,
                 'scaler_state_dict': scaler.state_dict() if args.use_amp else None,
                 'loss': avg_loss,
                 'args': saved_args
             }
             save_path = f"{args.checkpoint_dir}/checkpoint_epoch{epoch+1}.pth"
             torch.save(checkpoint_data, save_path)
             print(f"Saved checkpoint: {save_path}")
             if is_best:
                 best_path = f"{args.checkpoint_dir}/best_model.pth"
                 torch.save(checkpoint_data, best_path)
                 print(f"Saved best model to: {best_path}")


    writer.close()
    print("Swin MAE Pretraining with Hierarchical Decoder completed!")


# --- Main execution block ---
if __name__ == '__main__':
    import argparse
    parser = argparse.ArgumentParser('Swin MAE with Hierarchical Decoder pretraining script', add_help=False)

    # Random Seed
    parser.add_argument('--seed', default=42, type=int, help='Random seed for reproducibility')

    # Model Parameters (Encoder - Swin Specific)
    parser.add_argument('--img_size', default=256, type=int, help='images input size')
    parser.add_argument('--patch_size', default=4, type=int, help='Swin patch size (usually 4)')
    parser.add_argument('--in_chans', default=1, type=int, help='Input channels')
    parser.add_argument('--swin_embed_dim', default=96, type=int, help='Swin encoder embedding dimension (e.g., 96 for Tiny/Small, 128 for Base)')
    parser.add_argument('--swin_depths', type=int, nargs='+', default=[2, 2, 6, 2], help='Swin encoder depth of each stage')
    parser.add_argument('--swin_num_heads', type=int, nargs='+', default=[3, 6, 12, 24], help='Swin encoder number of attention heads in different stages')
    parser.add_argument('--swin_window_size', type=int, default=7, help='Swin window size')
    parser.add_argument('--swin_ape', action='store_false', default=True, help='Disable absolute position embedding in Swin encoder (default uses APE)')

    # Model Parameters (Hierarchical Decoder)
    parser.add_argument('--decoder_embed_dim', default=512, type=int, help='Decoder embedding dimension')
    parser.add_argument('--decoder_depths', type=int, nargs='+', default=[1, 1, 1, 1], help='Decoder depth for each stage')
    parser.add_argument('--decoder_num_heads', type=int, nargs='+', default=[16, 8, 4, 2], help='Decoder number of attention heads for each stage')
    parser.add_argument('--norm_pix_loss', action='store_true', help='Enable normalized pixel loss') # Keep flag, default is False
    parser.set_defaults(norm_pix_loss=False) # Explicitly set default to False
    parser.add_argument('--perceptual_loss_weight', type=float, default=0.01, help='Weight for ResNet18 perceptual loss (0 to disable)') # Add perceptual loss arg
    parser.add_argument('--perc_layers_resnet', type=str, nargs='+', default=['layer2', 'layer3'], help='ResNet layers for perceptual loss (e.g., layer2 layer3)')
    parser.add_argument('--perc_norm_ct_resnet', action='store_true', default=True, help='Use custom CT normalization for ResNet perceptual loss')
    parser.add_argument('--no_perc_norm_ct_resnet', action='store_false', dest='perc_norm_ct_resnet', help='Disable custom CT normalization for ResNet perceptual loss') # Allows disabling via flag
    parser.add_argument('--ssim_loss_weight', type=float, default=0.0, help='Weight for SSIM loss (0 to disable)')
    parser.add_argument('--patchnce_loss_weight', type=float, default=0.0, help='Weight for PatchNCE contrastive loss (0 to disable)')
    parser.add_argument('--nce_proj_dim', type=int, default=256, help='Projection dimension for PatchNCE features')
    parser.add_argument('--nce_T', type=float, default=0.07, help='Temperature parameter for PatchNCE loss')

    # Training Parameters
    parser.add_argument('--batch_size', default=64, type=int, help='Batch size per GPU')
    parser.add_argument('--epochs', default=200, type=int)
    parser.add_argument('--lr', type=float, default=1.5e-4, help='Base learning rate')
    parser.add_argument('--warmup_epochs', type=int, default=40, help='Epochs to warmup LR')
    parser.add_argument('--weight_decay', type=float, default=0.05)
    parser.add_argument('--use_amp', action='store_true', help='Enable mixed precision training')
    parser.set_defaults(use_amp=True) # Enable AMP by default like original
    parser.add_argument('--gradient_accumulation_steps', type=int, default=1, help='Number of steps to accumulate gradients before updating weights') # Add grad accum arg

    # Dataset Parameters
    parser.add_argument('--data_dir', default='E:/vscode/2号CT数据', type=str, help='dataset path') # Updated path
    parser.add_argument('--num_workers', default=8, type=int)

    # Checkpoint/Log Parameters
    parser.add_argument('--checkpoint_dir', default='checkpoints/swin_mae_hierarchical', help='path where to save checkpoints') # Updated default
    parser.add_argument('--log_dir', default='logs/swin_mae_hierarchical', help='path where to tensorboard log') # Updated default
    parser.add_argument('--save_interval', default=20, type=int, help='Save checkpoint every x epochs')
    parser.add_argument('--resume', default='', help='resume from checkpoint')

    args = parser.parse_args()

    pretrain_mae(args)
