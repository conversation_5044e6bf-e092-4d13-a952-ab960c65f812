#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
Final optimization script for Swin-MAE pretraining based on best results.
This script implements the final push to achieve PSNR > 22.0 and SSIM > 0.65.

Based on best model: checkpoints/swin_mae_hierarchical_random_ssim_nce_w002/best_model.pth
Current performance: PSNR = 21.51, SSIM = 0.626
Target performance: PSNR > 22.0, SSIM > 0.65
"""

import torch
import torch.nn as nn
import torch.optim as optim
from torch.optim.lr_scheduler import CosineAnnealingLR
import argparse
import os
import random
import numpy as np
from tqdm import tqdm
from torch.utils.tensorboard import SummaryWriter

# Import the best performing model architecture
from train_swin_mae_resnet_random_mask_hierarchical import (
    MaskedAutoencoderSwin,
    random_masking,
    HierarchicalDecoder
)
from data.mae_loader import get_mae_loader


def set_seed(seed):
    """Set random seed for reproducibility"""
    random.seed(seed)
    np.random.seed(seed)
    torch.manual_seed(seed)
    torch.cuda.manual_seed_all(seed)
    torch.backends.cudnn.deterministic = True
    torch.backends.cudnn.benchmark = False


def get_args():
    """Parse command line arguments with optimized defaults"""
    parser = argparse.ArgumentParser(description='Final Swin-MAE Optimization')

    # Model architecture (CORRECTED based on actual best performing configuration)
    parser.add_argument('--img_size', type=int, default=256, help='Input image size')
    parser.add_argument('--patch_size', type=int, default=4, help='Patch size')
    parser.add_argument('--in_chans', type=int, default=1, help='Input channels')
    parser.add_argument('--swin_embed_dim', type=int, default=96, help='Swin embedding dimension')
    parser.add_argument('--decoder_embed_dim', type=int, default=128, help='Decoder embedding dimension (actual best)')
    parser.add_argument('--decoder_depths', nargs='+', type=int, default=[1, 1, 1, 1], help='Decoder depths (actual best)')
    parser.add_argument('--decoder_num_heads', nargs='+', type=int, default=[8, 8, 4, 2], help='Decoder attention heads (actual best)')

    # Loss function weights (CORRECTED based on actual best results)
    parser.add_argument('--perceptual_loss_weight', type=float, default=0.005, help='Perceptual loss weight')
    parser.add_argument('--ssim_loss_weight', type=float, default=0.05, help='SSIM loss weight (actual best)')
    parser.add_argument('--patchnce_loss_weight', type=float, default=0.005, help='PatchNCE loss weight (actual best)')
    parser.add_argument('--nce_proj_dim', type=int, default=256, help='NCE projection dimension')
    parser.add_argument('--nce_T', type=float, default=0.07, help='NCE temperature')

    # Training parameters (CORRECTED to match actual best configuration)
    parser.add_argument('--epochs', type=int, default=200, help='Training epochs (actual best)')
    parser.add_argument('--batch_size', type=int, default=8, help='Batch size')
    parser.add_argument('--gradient_accumulation_steps', type=int, default=16, help='Gradient accumulation')
    parser.add_argument('--lr', type=float, default=1.5e-4, help='Learning rate')
    parser.add_argument('--warmup_epochs', type=int, default=40, help='Warmup epochs')
    parser.add_argument('--weight_decay', type=float, default=0.05, help='Weight decay')
    parser.add_argument('--grad_clip_norm', type=float, default=1.0, help='Gradient clipping norm')

    # System parameters
    parser.add_argument('--seed', type=int, default=42, help='Random seed')
    parser.add_argument('--use_amp', action='store_true', default=True, help='Use mixed precision')
    parser.add_argument('--num_workers', type=int, default=4, help='DataLoader workers')
    parser.add_argument('--data_dir', type=str, default='data/2号CT数据', help='Data directory')

    # Checkpointing (CORRECTED to match actual paths)
    parser.add_argument('--resume_from', type=str,
                       default='checkpoints/swin_mae_hierarchical_random_ssim_nce_w002/best_model.pth',
                       help='Resume from checkpoint')
    parser.add_argument('--checkpoint_dir', type=str,
                       default='checkpoints/swin_mae_hierarchical_random_ssim_nce_w002',
                       help='Checkpoint directory (for compatibility)')
    parser.add_argument('--save_dir', type=str,
                       default='checkpoints/swin_mae_final_optimization',
                       help='Save directory')
    parser.add_argument('--log_dir', type=str,
                       default='logs/swin_mae_final_optimization',
                       help='Log directory')

    return parser.parse_args()


def create_model(args):
    """Create the Swin-MAE model with optimized configuration"""
    model = MaskedAutoencoderSwin(
        img_size=args.img_size,
        patch_size=args.patch_size,
        in_chans=args.in_chans,
        embed_dim=96,  # Swin-T default
        depths=[2, 2, 6, 2],  # Swin-T default
        num_heads=[3, 6, 12, 24],  # Swin-T default
        window_size=7,
        mlp_ratio=4.0,
        norm_layer=nn.LayerNorm,
        ape=False,
        patch_norm=True,
        decoder_embed_dim=args.decoder_embed_dim,
        decoder_depths=args.decoder_depths,
        decoder_num_heads=args.decoder_num_heads,
        norm_pix_loss=False,
        perceptual_loss_weight=args.perceptual_loss_weight,
        ssim_loss_weight=args.ssim_loss_weight,
        patchnce_loss_weight=args.patchnce_loss_weight,
        nce_proj_dim=args.nce_proj_dim,
        nce_T=args.nce_T
    )
    return model


def load_checkpoint(model, checkpoint_path, device):
    """Load checkpoint and return epoch and best loss"""
    if os.path.exists(checkpoint_path):
        print(f"Loading checkpoint from {checkpoint_path}")
        checkpoint = torch.load(checkpoint_path, map_location=device)

        # Handle different checkpoint formats
        if 'model_state_dict' in checkpoint:
            model.load_state_dict(checkpoint['model_state_dict'])
            epoch = checkpoint.get('epoch', 0)
            best_loss = checkpoint.get('best_loss', float('inf'))
        else:
            model.load_state_dict(checkpoint)
            epoch = 0
            best_loss = float('inf')

        print(f"Loaded checkpoint from epoch {epoch}, best_loss: {best_loss:.6f}")
        return epoch, best_loss
    else:
        print(f"No checkpoint found at {checkpoint_path}, starting from scratch")
        return 0, float('inf')


def save_checkpoint(model, optimizer, scheduler, epoch, loss, best_loss, save_dir, is_best=False):
    """Save model checkpoint"""
    os.makedirs(save_dir, exist_ok=True)

    checkpoint = {
        'epoch': epoch,
        'model_state_dict': model.state_dict(),
        'optimizer_state_dict': optimizer.state_dict(),
        'scheduler_state_dict': scheduler.state_dict(),
        'loss': loss,
        'best_loss': best_loss,
    }

    # Save regular checkpoint
    checkpoint_path = os.path.join(save_dir, f'checkpoint_epoch_{epoch}.pth')
    torch.save(checkpoint, checkpoint_path)

    # Save best model
    if is_best:
        best_path = os.path.join(save_dir, 'best_model.pth')
        torch.save(checkpoint, best_path)
        print(f"New best model saved with loss: {loss:.6f}")


def train_epoch(model, dataloader, optimizer, scaler, device, epoch, args, writer):
    """Train for one epoch"""
    model.train()
    total_loss = 0.0
    num_batches = len(dataloader)

    pbar = tqdm(dataloader, desc=f'Epoch {epoch+1}/{args.epochs}')

    for batch_idx, (images, _) in enumerate(pbar):
        images = images.to(device, non_blocking=True)

        with torch.cuda.amp.autocast(enabled=args.use_amp):
            loss, _, _ = model(images, mask_ratio=0.75)

        # Scale loss for gradient accumulation
        loss = loss / args.gradient_accumulation_steps

        # Backward pass
        scaler.scale(loss).backward()

        # Update weights every gradient_accumulation_steps
        if (batch_idx + 1) % args.gradient_accumulation_steps == 0:
            # Gradient clipping
            scaler.unscale_(optimizer)
            torch.nn.utils.clip_grad_norm_(model.parameters(), args.grad_clip_norm)

            scaler.step(optimizer)
            scaler.update()
            optimizer.zero_grad()

        total_loss += loss.item() * args.gradient_accumulation_steps

        # Update progress bar
        pbar.set_postfix({
            'Loss': f'{loss.item() * args.gradient_accumulation_steps:.6f}',
            'LR': f'{optimizer.param_groups[0]["lr"]:.2e}'
        })

        # Log to tensorboard
        global_step = epoch * num_batches + batch_idx
        if batch_idx % 100 == 0:
            writer.add_scalar('Loss/batch', loss.item() * args.gradient_accumulation_steps, global_step)
            writer.add_scalar('LR', optimizer.param_groups[0]['lr'], global_step)

    avg_loss = total_loss / num_batches
    return avg_loss


def main():
    args = get_args()

    # Set seed for reproducibility
    set_seed(args.seed)

    # Setup device
    device = torch.device('cuda' if torch.cuda.is_available() else 'cpu')
    print(f"Using device: {device}")

    # Create directories
    os.makedirs(args.save_dir, exist_ok=True)
    os.makedirs(args.log_dir, exist_ok=True)

    # Setup logging
    writer = SummaryWriter(args.log_dir)

    # Create model
    model = create_model(args)
    model = model.to(device)

    # Print model info
    param_count = sum(p.numel() for p in model.parameters() if p.requires_grad)
    print(f"Model parameters: {param_count:,}")

    # Load checkpoint if exists
    start_epoch, best_loss = load_checkpoint(model, args.resume_from, device)

    # Setup optimizer and scheduler
    optimizer = optim.AdamW(
        model.parameters(),
        lr=args.lr,
        weight_decay=args.weight_decay,
        betas=(0.9, 0.95)
    )

    # Cosine annealing scheduler with warmup
    scheduler = CosineAnnealingLR(
        optimizer,
        T_max=args.epochs - args.warmup_epochs,
        eta_min=1e-6
    )

    # Mixed precision scaler
    scaler = torch.cuda.amp.GradScaler(enabled=args.use_amp)

    # Data loader
    train_loader = get_mae_loader(
        data_dir=args.data_dir,
        batch_size=args.batch_size,
        num_workers=args.num_workers,
        crop_size=args.img_size,
        augment=True
    )

    print(f"Starting training from epoch {start_epoch}")
    print(f"Target: PSNR > 22.0, SSIM > 0.65")
    print(f"Current best loss: {best_loss:.6f}")

    # Training loop
    for epoch in range(start_epoch, args.epochs):
        # Warmup learning rate
        if epoch < args.warmup_epochs:
            lr_scale = (epoch + 1) / args.warmup_epochs
            for param_group in optimizer.param_groups:
                param_group['lr'] = args.lr * lr_scale
        else:
            scheduler.step()

        # Train one epoch
        avg_loss = train_epoch(model, train_loader, optimizer, scaler, device, epoch, args, writer)

        # Log epoch results
        writer.add_scalar('Loss/epoch', avg_loss, epoch)
        writer.add_scalar('LR/epoch', optimizer.param_groups[0]['lr'], epoch)

        print(f"Epoch {epoch+1}/{args.epochs}, Loss: {avg_loss:.6f}, LR: {optimizer.param_groups[0]['lr']:.2e}")

        # Save checkpoint
        is_best = avg_loss < best_loss
        if is_best:
            best_loss = avg_loss

        if (epoch + 1) % 10 == 0 or is_best:
            save_checkpoint(model, optimizer, scheduler, epoch + 1, avg_loss, best_loss, args.save_dir, is_best)

    # Save final model
    final_path = os.path.join(args.save_dir, 'final_model.pth')
    torch.save({
        'epoch': args.epochs,
        'model_state_dict': model.state_dict(),
        'best_loss': best_loss,
    }, final_path)

    writer.close()
    print(f"Training completed! Best loss: {best_loss:.6f}")
    print(f"Models saved to: {args.save_dir}")


if __name__ == '__main__':
    main()
