# -*- coding: utf-8 -*-
"""
Dataloader for the Super-Resolution (SR) training phase.
Handles loading High-Resolution (HR) images and synthetically
generating Low-Resolution (LR) counterparts for paired training.
"""

import torch
from torch.utils.data import Dataset, DataLoader
import os
from PIL import Image
import torchvision.transforms as T
import torchvision.transforms.functional as TF
import random
import numpy as np
import kornia.filters as KF # For Gaussian Blur
import kornia.geometry.transform as KGT # For resizing, rotation
import kornia.augmentation as KA # For noise
import math # For math operations like pi
import sys # Added for path manipulation

# --- BSRGAN-style Degradation ---

def random_anisotropic_gaussian_kernel(kernel_size=21, sigma_min=0.2, sigma_max=4.0, angle_range=(-math.pi, math.pi)):
    """ Generates a random anisotropic Gaussian kernel """
    # Randomly sample parameters
    sigma_x = random.uniform(sigma_min, sigma_max)
    sigma_y = random.uniform(sigma_min, sigma_max)
    angle = random.uniform(angle_range[0], angle_range[1])

    # Create meshgrid
    center = kernel_size // 2
    x = torch.arange(kernel_size) - center
    y = torch.arange(kernel_size) - center
    xx, yy = torch.meshgrid(x, y, indexing='ij')

    # Rotate coordinates
    cos_a, sin_a = math.cos(angle), math.sin(angle)
    xx_rot = cos_a * xx + sin_a * yy
    yy_rot = -sin_a * xx + cos_a * yy

    # Calculate anisotropic Gaussian
    kernel = torch.exp(
        -( (xx_rot**2 / (2.0 * sigma_x**2)) + (yy_rot**2 / (2.0 * sigma_y**2)) )
    )
    # Normalize
    kernel = kernel / kernel.sum()
    return kernel.unsqueeze(0).unsqueeze(0) # Shape [1, 1, K, K]

def random_isotropic_gaussian_kernel(kernel_size=21, sigma_min=0.2, sigma_max=4.0):
     """ Generates a random isotropic Gaussian kernel """
     sigma = random.uniform(sigma_min, sigma_max)
     kernel = KF.get_gaussian_kernel2d((kernel_size, kernel_size), (sigma, sigma))
     return kernel.unsqueeze(0).unsqueeze(0) # Shape [1, 1, K, K]

def degrade_bsrgan_style(hr_tensor, config):
    """
    Applies a BSRGAN-style degradation pipeline with randomized parameters.
    Order: Blur -> Downsample -> Noise (can be shuffled)

    Args:
        hr_tensor (torch.Tensor): High-resolution image tensor (C, H, W), range [0, 1].
        config (dict): Configuration dictionary containing degradation parameters under 'data'.

    Returns:
        torch.Tensor: Low-resolution image tensor (C, H/sf, W/sf), range [0, 1].
    """
    data_config = config['data']
    scale_factor = data_config['scale_factor']
    device = hr_tensor.device

    # --- Degradation Parameters (from config or defaults) ---
    # Blur
    blur_prob = data_config.get('blur_prob', 1.0)
    blur_kernel_size = data_config.get('blur_kernel_size', 21)
    blur_sigma_range = data_config.get('blur_sigma_range', [0.2, 3.0])
    aniso_prob = data_config.get('aniso_prob', 0.5) # Probability of anisotropic blur vs isotropic
    # Downsampling
    downsample_prob = data_config.get('downsample_prob', 1.0)
    downsample_methods = data_config.get('downsample_methods', ['bicubic', 'bilinear', 'nearest'])
    # Noise
    noise_prob = data_config.get('noise_prob', 1.0)
    noise_gaussian_sigma_range = data_config.get('noise_gaussian_sigma_range', [1, 30]) # Sigma in range [0, 255]

    # Shuffle order? (Optional, BSRGAN does this)
    shuffle_prob = data_config.get('shuffle_prob', 0.5)
    degradation_order = ['blur', 'downsample', 'noise']
    if random.random() < shuffle_prob:
        random.shuffle(degradation_order)

    # --- Apply Degradations ---
    current_tensor = hr_tensor.float().unsqueeze(0) # Add batch dim for kornia/KA

    for step in degradation_order:
        if step == 'blur' and random.random() < blur_prob:
            if random.random() < aniso_prob:
                kernel = random_anisotropic_gaussian_kernel(
                    kernel_size=blur_kernel_size,
                    sigma_min=blur_sigma_range[0],
                    sigma_max=blur_sigma_range[1]
                ).to(device)
            else:
                kernel = random_isotropic_gaussian_kernel(
                    kernel_size=blur_kernel_size,
                    sigma_min=blur_sigma_range[0],
                    sigma_max=blur_sigma_range[1]
                ).to(device)
            # Apply kernel using filter2d
            current_tensor = KF.filter2d(current_tensor, kernel, border_type='reflect')

        elif step == 'downsample' and random.random() < downsample_prob:
            method_str = random.choice(downsample_methods)
            if method_str == 'bicubic':
                interp_mode = T.InterpolationMode.BICUBIC
            elif method_str == 'bilinear':
                interp_mode = T.InterpolationMode.BILINEAR
            else: # nearest
                interp_mode = T.InterpolationMode.NEAREST

            lr_h = current_tensor.shape[2] // scale_factor
            lr_w = current_tensor.shape[3] // scale_factor
            # Use TF.resize which handles tensors (C, H, W) - remove batch dim
            current_tensor_chw = current_tensor.squeeze(0)
            downsampled_tensor = TF.resize(current_tensor_chw, size=[lr_h, lr_w], interpolation=interp_mode, antialias=(interp_mode != T.InterpolationMode.NEAREST))
            current_tensor = downsampled_tensor.unsqueeze(0) # Add batch dim back

        elif step == 'noise' and random.random() < noise_prob:
            sigma = random.uniform(noise_gaussian_sigma_range[0], noise_gaussian_sigma_range[1])
            # Kornia expects sigma in range [0, 1] if input is [0, 1]
            sigma_normalized = sigma / 255.0
            # Apply Gaussian noise
            current_tensor = KA.RandomGaussianNoise(mean=0.0, std=sigma_normalized, p=1.0)(current_tensor)

    # Remove batch dimension and clamp final output
    lr_tensor = torch.clamp(current_tensor.squeeze(0), 0, 1)

    return lr_tensor

class SRDataset(Dataset):
    """
    Dataset for SR training using synthetic LR generation (Strategy A).
    Loads HR images, generates LR counterparts, and provides paired patches.
    """
    def __init__(self, config):
        self.data_config = config['data']
        self.model_config = config['model']

        # --- Resolve Paths ---
        # Get project root (assuming sr_dataloader.py is in Self-supervised training network/data/)
        PROJECT_ROOT = os.path.abspath(os.path.join(os.path.dirname(__file__), '..', '..'))
        if PROJECT_ROOT not in sys.path:
             sys.path.insert(0, PROJECT_ROOT)

        # Construct absolute paths from relative paths in config
        self.hr_dir = os.path.join(PROJECT_ROOT, self.data_config['hr_dir'])
        # Also resolve LR dir path if needed later, though not used for loading here
        # self.lr_dir = os.path.join(PROJECT_ROOT, self.data_config['lr_dir'])
        print(f"Resolved HR directory: {self.hr_dir}")

        self.patch_size_hr = self.data_config['patch_size_hr']
        self.scale_factor = self.data_config['scale_factor'] # e.g., 4 if HR is 4x LR resolution
        self.patch_size_lr = self.patch_size_hr // self.scale_factor
        # Store full config for degradation function
        self.config = config
        self.input_channels = self.model_config.get('in_channels', 1) # Get expected channels

        # Check resolved HR directory
        if not os.path.isdir(self.hr_dir):
             print(f"Error initializing SRDataset: Resolved HR directory not found: {self.hr_dir}")
             print("Please check 'hr_dir' in your config file and ensure it's relative to the project root.")
             raise FileNotFoundError(f"Resolved HR directory not found: {self.hr_dir}")

        self.hr_files = sorted([
            os.path.join(self.hr_dir, f) for f in os.listdir(self.hr_dir)
            if f.lower().endswith(('.png', '.jpg', '.jpeg', '.tif', '.bmp'))
        ])

        if not self.hr_files:
            raise FileNotFoundError(f"No HR image files found in {self.hr_dir}")

        # Basic transform: ToTensor (scales to [0, 1]) and Normalize to [-1, 1]
        self.normalize = T.Normalize((0.5,) * self.input_channels, (0.5,) * self.input_channels)

        print(f"SRDataset (Synthetic LR) initialized. Found {len(self.hr_files)} HR images in {self.hr_dir}.")
        print(f"HR Patch Size: {self.patch_size_hr}, LR Patch Size: {self.patch_size_lr}, Scale: {self.scale_factor}")

    def __len__(self):
        return len(self.hr_files)

    def __getitem__(self, idx):
        hr_img_path = self.hr_files[idx]
        try:
            # Load HR image
            hr_image_pil = Image.open(hr_img_path)
            # Ensure correct number of channels (e.g., grayscale 'L' or RGB 'RGB')
            if self.input_channels == 1 and hr_image_pil.mode != 'L':
                hr_image_pil = hr_image_pil.convert('L')
            elif self.input_channels == 3 and hr_image_pil.mode != 'RGB':
                 hr_image_pil = hr_image_pil.convert('RGB')
            elif hr_image_pil.mode not in ['L', 'RGB']: # Handle other modes if necessary
                 hr_image_pil = hr_image_pil.convert('L' if self.input_channels == 1 else 'RGB')


            # Convert HR to tensor [0, 1]
            hr_tensor = TF.to_tensor(hr_image_pil) # Shape: [C, H, W]

            # --- Synthetic LR Generation using BSRGAN-style ---
            lr_tensor = degrade_bsrgan_style(
                hr_tensor.float(), # Expects [0, 1] range
                self.config
            ) # Shape: [C, H/sf, W/sf]

            # --- Paired Random Cropping ---
            # Get random crop parameters from HR image
            i, j, h, w = T.RandomCrop.get_params(hr_tensor, output_size=(self.patch_size_hr, self.patch_size_hr))
            # Apply crop to HR tensor
            hr_patch = TF.crop(hr_tensor, i, j, h, w)

            # Calculate corresponding crop parameters for LR tensor
            lr_i = i // self.scale_factor
            lr_j = j // self.scale_factor
            lr_h = h // self.scale_factor
            lr_w = w // self.scale_factor
            # Apply crop to LR tensor
            lr_patch = TF.crop(lr_tensor, lr_i, lr_j, lr_h, lr_w)

            # --- Normalization ---
            # Normalize both patches to [-1, 1]
            hr_patch = self.normalize(hr_patch)
            lr_patch = self.normalize(lr_patch)

            # --- Augmentation (Optional) ---
            # Apply same random horizontal/vertical flip to both patches
            if random.random() > 0.5:
                hr_patch = TF.hflip(hr_patch)
                lr_patch = TF.hflip(lr_patch)
            if random.random() > 0.5:
                 hr_patch = TF.vflip(hr_patch)
                 lr_patch = TF.vflip(lr_patch)
            # Add rotation if needed

            return lr_patch, hr_patch

        except Exception as e:
            print(f"Error processing image {hr_img_path} at index {idx}: {e}")
            # Return dummy tensors on error to avoid crashing the loader
            dummy_lr = torch.zeros(self.input_channels, self.patch_size_lr, self.patch_size_lr)
            dummy_hr = torch.zeros(self.input_channels, self.patch_size_hr, self.patch_size_hr)
            return dummy_lr, dummy_hr


def get_sr_dataloader(config, batch_size, num_workers=4, shuffle=True):
    """
    Creates and returns a DataLoader for the SRDataset.
    """
    try:
        dataset = SRDataset(config)
    except FileNotFoundError as e:
        print(f"Error initializing SRDataset: {e}")
        print("Please check 'hr_dir' in your config file.")
        return None # Return None if dataset init fails

    if len(dataset) == 0:
        print("Warning: SRDataset is empty. Check HR image directory and file types.")
        return None

    dataloader = DataLoader(
        dataset,
        batch_size=batch_size,
        shuffle=shuffle,
        num_workers=num_workers,
        pin_memory=True,
        drop_last=True # Important for consistent batch sizes during training
    )
    print(f"SR DataLoader created. Batches per epoch: {len(dataloader)}")
    return dataloader

# Example usage (for testing)
if __name__ == '__main__':
    print("Testing SR Dataloader (Synthetic LR)...")
    # Create dummy config for testing
    dummy_config = {
        'data': {
            'hr_dir': './dummy_hr_data_sr', # Use a different dummy dir name
            'patch_size_hr': 128,
            'scale_factor': 4,
            'degradation_kernel_size': 5,
            'degradation_sigma': 1.5,
        },
        'model': {
             'in_channels': 1 # Assuming grayscale
        }
    }
    dummy_data_config = dummy_config['data']
    patch_size_lr = dummy_data_config['patch_size_hr'] // dummy_data_config['scale_factor']

    # Create dummy directory and a few dummy images
    os.makedirs(dummy_data_config['hr_dir'], exist_ok=True)
    try:
        # Create images large enough for cropping
        Image.new('L', (200, 200)).save(os.path.join(dummy_data_config['hr_dir'], 'hr_1.png'))
        Image.new('L', (256, 192)).save(os.path.join(dummy_data_config['hr_dir'], 'hr_2.tif'))
        print("Dummy HR images created.")

        sr_loader_test = get_sr_dataloader(dummy_config, batch_size=2, num_workers=0)

        if sr_loader_test:
            # Fetch a batch
            print("Fetching a batch...")
            lr_batch_test, hr_batch_test = next(iter(sr_loader_test))
            print("LR Batch shape:", lr_batch_test.shape) # Should be [2, 1, 32, 32]
            print("HR Batch shape:", hr_batch_test.shape) # Should be [2, 1, 128, 128]
            print("LR Batch min/max:", lr_batch_test.min().item(), lr_batch_test.max().item()) # Should be approx [-1, 1]
            print("HR Batch min/max:", hr_batch_test.min().item(), hr_batch_test.max().item()) # Should be approx [-1, 1]
            print("Data loading test successful.")
        else:
            print("Dataloader creation failed.")

    except Exception as e:
        print(f"Error during dataloader test: {e}")
    finally:
        # Clean up dummy files/dirs
        import shutil
        if os.path.exists(dummy_data_config['hr_dir']):
            shutil.rmtree(dummy_data_config['hr_dir'])
        print("Cleaned up dummy data.")
