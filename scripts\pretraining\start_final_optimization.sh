#!/bin/bash
# Quick start script for final Swin-MAE optimization
# This script launches the final optimization to push PSNR from 21.51 to 22.0+

echo "🚀 Starting Final Swin-MAE Optimization"
echo "========================================"
echo "Current best: PSNR=21.51, SSIM=0.626"
echo "Target: PSNR>22.0, SSIM>0.65"
echo "========================================"

# Activate Python environment
echo "Activating Python environment..."
# Uncomment the line below if you need to activate a specific environment
# source activate pytorchEnv

# Check GPU availability
echo "Checking GPU availability..."
python -c "import torch; print(f'CUDA available: {torch.cuda.is_available()}'); print(f'GPU count: {torch.cuda.device_count()}'); print(f'Current device: {torch.cuda.current_device() if torch.cuda.is_available() else \"CPU\"}')"

# Create necessary directories
echo "Creating directories..."
mkdir -p checkpoints/swin_mae_final_optimization
mkdir -p logs/swin_mae_final_optimization

# Start training with CORRECTED parameters (matching actual best configuration)
echo "Starting final optimization training..."
python scripts/pretraining/train_swin_mae_final_optimization.py \
    --seed 42 \
    --epochs 200 \
    --batch_size 8 \
    --gradient_accumulation_steps 16 \
    --patch_size 4 \
    --swin_embed_dim 96 \
    --decoder_embed_dim 128 \
    --decoder_depths 1 1 1 1 \
    --decoder_num_heads 8 8 4 2 \
    --perceptual_loss_weight 0.005 \
    --ssim_loss_weight 0.05 \
    --patchnce_loss_weight 0.005 \
    --nce_proj_dim 256 \
    --nce_T 0.07 \
    --lr 1.5e-4 \
    --warmup_epochs 40 \
    --weight_decay 0.05 \
    --grad_clip_norm 1.0 \
    --use_amp \
    --resume_from checkpoints/swin_mae_hierarchical_random_ssim_nce_w002/best_model.pth \
    --save_dir checkpoints/swin_mae_final_optimization \
    --log_dir logs/swin_mae_final_optimization \
    --data_dir data/2号CT数据

echo "Training completed! Check results in:"
echo "- Checkpoints: checkpoints/swin_mae_final_optimization/"
echo "- Logs: logs/swin_mae_final_optimization/"
echo "- TensorBoard: tensorboard --logdir logs/swin_mae_final_optimization"
