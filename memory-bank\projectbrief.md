# CT图像超分辨率项目简介

## 项目目标

开发一个基于深度学习的CT图像超分辨率重建系统，特别针对岩心CT图像，通过自监督预训练和监督训练相结合的方式，提高图像分辨率和质量。

## 核心需求

1. **自监督预训练**：
   - 使用Swin-MAE（Swin Transformer与Masked Autoencoder结合）架构
   - 在数据集#2（2700×2700像素，1218个切片，0.03889mm体素大小）上进行预训练
   - 实现有效的掩蔽和重建策略

2. **监督训练**：
   - 使用数据集#3（897个切片，0.00956mm体素大小）作为高分辨率参考
   - 微调预训练模型以实现超分辨率重建

3. **损失函数优化**：
   - 实现并评估多种损失函数，包括：
     - 感知损失（Perceptual Loss）
     - 特征模仿（Feature Mimicking）
     - 对比损失（Contrastive Loss）
   - 针对CT图像（灰度图）特性进行优化

4. **评估指标**：
   - 定量评估：PSNR、SSIM等标准图像质量评估指标
   - 定性评估：视觉质量和细节保留

## 项目范围

- **包含**：
  - Swin-MAE模型的实现和优化
  - 多种损失函数的实现和比较
  - 针对CT图像特性的特殊处理方法
  - 预训练和监督训练流程的开发

- **不包含**：
  - 实时处理系统
  - 部署到临床环境
  - 多模态融合

## 关键挑战

1. 针对CT图像（灰度图）的特征提取器选择和优化
2. 适合CT图像的"教师"模型选择或训练
3. 有效处理单通道CT图像用于感知损失计算
4. 在有限计算资源下处理大尺寸CT图像

## 成功标准

1. 相比基线方法，超分辨率重建图像在PSNR和SSIM上有显著提升
2. 重建图像保留更多细节，特别是岩心结构的细微特征
3. 模型训练时间和推理时间在可接受范围内
4. 实现的方法可以推广到其他类似的CT图像超分辨率任务
