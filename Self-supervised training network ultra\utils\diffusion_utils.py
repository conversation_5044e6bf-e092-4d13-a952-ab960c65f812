# -*- coding: utf-8 -*-
"""
Utility functions for the diffusion process, including noise scheduling
and the forward noising process (q_sample).
Based on implementations from DDPM, DDIM, and guided-diffusion.
"""

import torch
import torch.nn.functional as F
import math

def linear_beta_schedule(timesteps, beta_start=1e-4, beta_end=0.02):
    """ Linear schedule from DDPM paper. """
    return torch.linspace(beta_start, beta_end, timesteps)

def cosine_beta_schedule(timesteps, s=0.008):
    """
    Cosine schedule as proposed in Improved Denoising Diffusion Probabilistic Models.
    https://arxiv.org/abs/2102.09672
    """
    steps = timesteps + 1
    x = torch.linspace(0, timesteps, steps)
    alphas_cumprod = torch.cos(((x / timesteps) + s) / (1 + s) * math.pi * 0.5) ** 2
    alphas_cumprod = alphas_cumprod / alphas_cumprod[0]
    betas = 1 - (alphas_cumprod[1:] / alphas_cumprod[:-1])
    return torch.clip(betas, 0.0001, 0.9999)

def get_diffusion_schedule(schedule_name, timesteps):
    """ Returns the beta schedule and related variables. """
    if schedule_name == 'linear':
        betas = linear_beta_schedule(timesteps)
    elif schedule_name == 'cosine':
        betas = cosine_beta_schedule(timesteps)
    else:
        raise NotImplementedError(f"Unknown schedule: {schedule_name}")

    alphas = 1. - betas
    alphas_cumprod = torch.cumprod(alphas, axis=0)
    alphas_cumprod_prev = F.pad(alphas_cumprod[:-1], (1, 0), value=1.0)
    sqrt_recip_alphas = torch.sqrt(1.0 / alphas)
    sqrt_alphas_cumprod = torch.sqrt(alphas_cumprod)
    sqrt_one_minus_alphas_cumprod = torch.sqrt(1. - alphas_cumprod)
    posterior_variance = betas * (1. - alphas_cumprod_prev) / (1. - alphas_cumprod)

    schedule_dict = {
        "betas": betas,
        "alphas": alphas,
        "alphas_cumprod": alphas_cumprod,
        "alphas_cumprod_prev": alphas_cumprod_prev,
        "sqrt_recip_alphas": sqrt_recip_alphas,
        "sqrt_alphas_cumprod": sqrt_alphas_cumprod,
        "sqrt_one_minus_alphas_cumprod": sqrt_one_minus_alphas_cumprod,
        "posterior_variance": posterior_variance,
    }
    return schedule_dict

def extract(a, t, x_shape):
    """ Extracts values from a for given indices t and reshapes to match x_shape. """
    batch_size = t.shape[0]
    out = a.gather(-1, t.to(a.device)) # Get values corresponding to timesteps t
    return out.reshape(batch_size, *((1,) * (len(x_shape) - 1))).to(t.device)

def q_sample(x_start, t, schedule_dict, noise=None):
    """
    Forward diffusion process: q(x_t | x_0).
    Adds noise to x_start according to the schedule at timestep t.

    Args:
        x_start (torch.Tensor): The initial clean image (x_0).
        t (torch.Tensor): Timesteps for each image in the batch.
        schedule_dict (dict): Dictionary containing diffusion schedule tensors.
        noise (torch.Tensor, optional): Optional noise tensor. If None, generated randomly.

    Returns:
        torch.Tensor: The noised image at timestep t (x_t).
    """
    if noise is None:
        noise = torch.randn_like(x_start)

    sqrt_alphas_cumprod_t = extract(schedule_dict["sqrt_alphas_cumprod"], t, x_start.shape)
    sqrt_one_minus_alphas_cumprod_t = extract(schedule_dict["sqrt_one_minus_alphas_cumprod"], t, x_start.shape)

    noised_image = sqrt_alphas_cumprod_t * x_start + sqrt_one_minus_alphas_cumprod_t * noise
    return noised_image

def predict_xstart_from_noise(x_t, t, noise, schedule_dict):
    """
    Predicts the starting image (x_0) given the noisy image (x_t) and the predicted noise.
    Uses the formula: x_0 = (x_t - sqrt(1 - alpha_cumprod_t) * noise) / sqrt(alpha_cumprod_t)
    """
    sqrt_recip_alphas_cumprod_t = extract(1.0 / schedule_dict["sqrt_alphas_cumprod"], t, x_t.shape)
    sqrt_recipm1_alphas_cumprod_t = extract(torch.sqrt(1.0 / schedule_dict["alphas_cumprod"] - 1), t, x_t.shape) # Equivalent to sqrt(1-alpha_cumprod)/sqrt(alpha_cumprod)

    # Simplified formula: x_0 = sqrt_recip(alphas_cumprod_t) * x_t - sqrt_recipm1(alphas_cumprod_t) * noise
    pred_xstart = sqrt_recip_alphas_cumprod_t * x_t - sqrt_recipm1_alphas_cumprod_t * noise
    return pred_xstart


# --- Sampling Functions ---

@torch.no_grad()
def ddim_sample_step(model, x, t, prev_t, condition, schedule_dict, eta=0.0):
    """
    Perform one step of DDIM sampling (reverse process).
    x_t -> x_{t-1}

    Args:
        model: The diffusion model.
        x (torch.Tensor): Current noisy image (x_t).
        t (torch.Tensor): Current timestep.
        prev_t (torch.Tensor): Previous timestep.
        condition (torch.Tensor): Conditioning input (e.g., LR image).
        schedule_dict (dict): Dictionary containing diffusion schedule tensors.
        eta (float): DDIM parameter controlling stochasticity (0=deterministic DDIM, 1=DDPM).

    Returns:
        torch.Tensor: Denoised image at previous timestep (x_{t-1}).
        torch.Tensor: Predicted starting image (x_0).
    """
    # Predict noise (epsilon) using the model
    pred_noise = model(x, t, condition=condition)

    # Predict x_0 using the formula derived from q_sample
    pred_xstart = predict_xstart_from_noise(x, t, pred_noise, schedule_dict)
    pred_xstart = torch.clamp(pred_xstart, -1.0, 1.0) # Clamp to valid range

    # Get schedule parameters for current and previous timesteps
    alpha_cumprod_t = extract(schedule_dict["alphas_cumprod"], t, x.shape)
    alpha_cumprod_t_prev = extract(schedule_dict["alphas_cumprod"], prev_t, x.shape)
    sqrt_one_minus_alpha_cumprod_t = extract(schedule_dict["sqrt_one_minus_alphas_cumprod"], t, x.shape)
    sqrt_one_minus_alpha_cumprod_t_prev = extract(schedule_dict["sqrt_one_minus_alphas_cumprod"], prev_t, x.shape)

    # Calculate standard deviation of noise for the previous step
    sigma = eta * torch.sqrt((1 - alpha_cumprod_t_prev) / (1 - alpha_cumprod_t) * (1 - alpha_cumprod_t / alpha_cumprod_t_prev))

    # Calculate the direction pointing to x_t
    pred_dir_xt = torch.sqrt(1 - alpha_cumprod_t_prev - sigma**2) * pred_noise

    # Calculate x_{t-1}
    x_prev = torch.sqrt(alpha_cumprod_t_prev) * pred_xstart + pred_dir_xt + sigma * torch.randn_like(x)

    return x_prev, pred_xstart


@torch.no_grad()
def ddim_sample_loop(model, shape, condition, device, schedule_dict, num_train_timesteps, sampling_timesteps=50, eta=0.0):
    """
    DDIM sampling loop.

    Args:
        model: The diffusion model.
        shape (tuple): Shape of the image to generate (B, C, H, W).
        condition (torch.Tensor): Conditioning input.
        device: Target device.
        schedule_dict (dict): Diffusion schedule tensors.
        num_train_timesteps (int): Total timesteps used during training.
        sampling_timesteps (int): Number of steps for sampling.
        eta (float): DDIM eta parameter.

    Returns:
        torch.Tensor: Generated image.
    """
    batch_size = shape[0]
    img = torch.randn(shape, device=device) # Start with random noise (x_T)

    # Define the sampling timesteps (spaced out subset of training timesteps)
    times = torch.linspace(-1, num_train_timesteps - 1, steps=sampling_timesteps + 1)
    times = list(reversed(times.int().tolist())) # T-1, T-1-k, ..., 0
    time_pairs = list(zip(times[:-1], times[1:])) # [(T-1, T-1-k), (T-1-k, T-1-2k), ..., (k, 0)]

    print(f"Running DDIM Sampling with {sampling_timesteps} steps...")
    for time, time_next in tqdm(time_pairs, desc="DDIM Sampling"):
        time_cond = torch.full((batch_size,), time, device=device, dtype=torch.long)
        prev_time_cond = torch.full((batch_size,), time_next, device=device, dtype=torch.long)

        img, _ = ddim_sample_step(
            model, img, time_cond, prev_time_cond, condition, schedule_dict, eta=eta
        )

    print("DDIM Sampling finished.")
    return img # Final denoised image x_0


# Placeholder for DDPM sampling (can be added if needed)
# def p_sample_loop_placeholder(...)
