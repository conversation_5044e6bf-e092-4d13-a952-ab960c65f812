#!/usr/bin/env python3
"""
测试数据加载器修复效果
验证预过滤和错误处理是否正常工作
"""

import torch
import sys
import os
import tempfile
import shutil
from PIL import Image
import numpy as np

# 添加项目路径
sys.path.append(os.path.dirname(os.path.abspath(__file__)))

from data.sr_dataloader import SRDataset, get_sr_dataloader, filter_none_collate

def create_test_images(test_dir, patch_size_hr=256):
    """创建测试图像"""
    os.makedirs(test_dir, exist_ok=True)
    
    # 创建不同尺寸的测试图像
    test_images = [
        ("valid_large.tif", 2700, 2700),      # 正常大小
        ("valid_medium.tif", 512, 512),       # 中等 大小，应该有效
        ("invalid_small.tif", 128, 128),      # 太小，应该被过滤
        ("invalid_tiny.tif", 64, 64),         # 非常小，应该被过滤
        ("valid_square.tif", 300, 300),       # 刚好够大
    ]
    
    created_files = []
    for filename, width, height in test_images:
        # 创建随机图像
        img_array = np.random.randint(0, 65536, (height, width), dtype=np.uint16)
        img = Image.fromarray(img_array, mode='I;16')
        
        filepath = os.path.join(test_dir, filename)
        img.save(filepath)
        created_files.append(filepath)
        print(f"Created test image: {filename} ({width}x{height})")
    
    return created_files

def test_image_filtering():
    """测试图像预过滤功能"""
    print("🔧 测试图像预过滤功能...")
    
    # 创建临时目录和测试图像
    with tempfile.TemporaryDirectory() as temp_dir:
        test_images = create_test_images(temp_dir, patch_size_hr=256)
        
        # 创建测试配置
        config = {
            'data': {
                'hr_dir': temp_dir,
                'patch_size_hr': 256,
                'scale_factor': 4,
            },
            'model': {
                'in_channels': 1
            }
        }
        
        try:
            # 测试数据集初始化
            dataset = SRDataset(config)
            
            print(f"✅ 数据集初始化成功")
            print(f"   总图像数: {len(test_images)}")
            print(f"   有效图像数: {len(dataset.hr_files)}")
            print(f"   过滤掉: {len(test_images) - len(dataset.hr_files)} 个图像")
            
            # 验证过滤结果
            valid_files = [os.path.basename(f) for f in dataset.hr_files]
            print(f"   有效文件: {valid_files}")
            
            # 应该过滤掉小图像
            assert "invalid_small.tif" not in valid_files, "小图像应该被过滤"
            assert "invalid_tiny.tif" not in valid_files, "微小图像应该被过滤"
            
            print("✅ 图像过滤测试通过")
            
        except Exception as e:
            print(f"❌ 图像过滤测试失败: {e}")
            return False
    
    return True

def test_dataloader_with_errors():
    """测试数据加载器的错误处理"""
    print("\n🔧 测试数据加载器错误处理...")
    
    with tempfile.TemporaryDirectory() as temp_dir:
        # 创建一些有效图像和一些会导致错误的图像
        test_images = create_test_images(temp_dir, patch_size_hr=256)
        
        # 创建一个损坏的图像文件
        corrupted_file = os.path.join(temp_dir, "corrupted.tif")
        with open(corrupted_file, 'w') as f:
            f.write("This is not an image")
        
        config = {
            'data': {
                'hr_dir': temp_dir,
                'patch_size_hr': 256,
                'scale_factor': 4,
            },
            'model': {
                'in_channels': 1
            }
        }
        
        try:
            # 创建数据加载器
            dataloader = get_sr_dataloader(config, batch_size=4, num_workers=0)
            
            if dataloader is None:
                print("❌ 数据加载器创建失败")
                return False
            
            print(f"✅ 数据加载器创建成功")
            
            # 测试批次加载
            batch_count = 0
            sample_count = 0
            
            for batch_idx, batch in enumerate(dataloader):
                if batch_idx >= 3:  # 只测试前几个批次
                    break
                
                lr_batch, hr_batch = batch
                batch_count += 1
                sample_count += lr_batch.shape[0]
                
                print(f"   批次 {batch_idx + 1}: LR shape {lr_batch.shape}, HR shape {hr_batch.shape}")
                
                # 验证tensor形状
                assert lr_batch.dim() == 4, f"LR tensor应该是4维，得到{lr_batch.dim()}维"
                assert hr_batch.dim() == 4, f"HR tensor应该是4维，得到{hr_batch.dim()}维"
                assert lr_batch.shape[2] == 64, f"LR高度应该是64，得到{lr_batch.shape[2]}"
                assert lr_batch.shape[3] == 64, f"LR宽度应该是64，得到{lr_batch.shape[3]}"
                assert hr_batch.shape[2] == 256, f"HR高度应该是256，得到{hr_batch.shape[2]}"
                assert hr_batch.shape[3] == 256, f"HR宽度应该是256，得到{hr_batch.shape[3]}"
            
            print(f"✅ 数据加载器测试通过")
            print(f"   处理了 {batch_count} 个批次，{sample_count} 个样本")
            
        except Exception as e:
            print(f"❌ 数据加载器测试失败: {e}")
            return False
    
    return True

def test_collate_function():
    """测试自定义collate函数"""
    print("\n🔧 测试自定义collate函数...")
    
    try:
        # 创建测试数据
        valid_sample1 = (torch.randn(1, 64, 64), torch.randn(1, 256, 256))
        valid_sample2 = (torch.randn(1, 64, 64), torch.randn(1, 256, 256))
        none_sample = None
        
        # 测试包含None的批次
        batch_with_none = [valid_sample1, none_sample, valid_sample2, none_sample]
        
        result = filter_none_collate(batch_with_none)
        lr_batch, hr_batch = result
        
        print(f"   输入批次大小: {len(batch_with_none)} (包含 {batch_with_none.count(None)} 个None)")
        print(f"   输出批次大小: {lr_batch.shape[0]}")
        
        assert lr_batch.shape[0] == 2, f"应该有2个有效样本，得到{lr_batch.shape[0]}个"
        assert hr_batch.shape[0] == 2, f"应该有2个有效样本，得到{hr_batch.shape[0]}个"
        
        # 测试全部为None的批次
        all_none_batch = [None, None, None]
        result_empty = filter_none_collate(all_none_batch)
        lr_empty, hr_empty = result_empty
        
        print(f"   全None批次结果: LR shape {lr_empty.shape}, HR shape {hr_empty.shape}")
        
        print("✅ 自定义collate函数测试通过")
        
    except Exception as e:
        print(f"❌ 自定义collate函数测试失败: {e}")
        return False
    
    return True

if __name__ == "__main__":
    print("="*60)
    print("🧪 数据加载器修复效果测试")
    print("="*60)
    
    success_count = 0
    total_tests = 3
    
    if test_image_filtering():
        success_count += 1
    
    if test_dataloader_with_errors():
        success_count += 1
    
    if test_collate_function():
        success_count += 1
    
    print("\n" + "="*60)
    print(f"✅ 测试完成: {success_count}/{total_tests} 个测试通过")
    
    if success_count == total_tests:
        print("🎉 所有测试都通过了！数据加载器修复成功！")
    else:
        print("⚠️ 部分测试失败，需要进一步检查")
    print("="*60)
