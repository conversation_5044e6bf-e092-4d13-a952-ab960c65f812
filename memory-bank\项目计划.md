# CT图像超分辨率项目计划

## 项目概述

本项目旨在通过自监督预训练和监督训练相结合的方式，提高CT图像的分辨率。特别是针对岩心CT图像的超分辨率重建，以获得更高质量的图像用于后续分析。
总体任务：针对非重叠岩心CT数据，使用不同的学习方法从2号CT数据中学习通用特征表示，为后续监督训练提供良好的初始化，最终的任务是用3号的CT数据来提高2号CT原始CT图像的分辨率。基础资料相关如下：1.3号数据为连续的897张二维岩心原始CT图像，其分辨率为2700x2700，并且体素大小为0.00956mm。 2.2号数据为连续的1218张二维岩心原始CT图像，其分辨率为2700x2700，并且体素大小为0.03889mm。 3.空间位置：这两个数据是一个完整岩心的不同部位的两个数据集，且两个数据集没有重叠的部位。


### 数据集信息

- **数据集#2**：用于自监督预训练
  - 1218个切片
  - 2700×2700像素分辨率
  - 体素大小：0.03889mm
  
- **数据集#3**：用于监督训练（作为高分辨率参考）
  - 897个切片
  - 体素大小：0.00956mm

### 项目目标

1. 利用Swin-MAE进行自监督预训练，学习CT图像的特征表示
2. 通过监督训练阶段，使用数据集#3作为高分辨率参考，提高数据集#2的分辨率
3. 探索并实现多种损失函数，以提高超分辨率重建的质量

## 实现细节

### 模型架构

- **基础模型**：Swin-MAE（Swin Transformer与Masked Autoencoder结合）
  - 编码器：Swin Transformer
  - 解码器：轻量级解码器，用于重建被掩蔽的图像区域

### 训练策略

1. **第一阶段**：自监督预训练
   - 使用数据集#2（2700×2700像素CT图像）
   - 采用Masked Autoencoder方法，随机掩蔽图像区域并重建
   - 主要文件：`train_swin_mae_refactored.py`和`train_swin_mae.py`
   - 数据加载：`E:/vscode/非配位超分辨/data/mae_loader.py`

2. **第二阶段**：监督训练
   - 使用数据集#3作为高分辨率参考
   - 微调预训练模型以实现超分辨率重建

### 损失函数探索

1. **感知损失（Perceptual Loss）**
   - 使用预训练特征提取器（如VGG）计算特征空间中的差异
   - 针对CT图像（灰度图）的特殊处理：考虑创建不同对比度通道而非简单通道复制
   - 寻找更适合CT图像的特征提取器替代VGG

2. **特征模仿（Feature Mimicking）**
   - 选择合适的"教师"模型，让MAE编码器/解码器模仿其特征输出
   - 需要解决的问题：选择或训练适合CT图像的教师模型

3. **对比损失（Contrastive Loss）**
   - 构建合适的正/负样本对
   - 在特征空间中拉近相似样本，推远不同样本

## 当前挑战与解决方案

1. **针对CT图像的特征提取器**
   - 挑战：标准VGG等模型主要针对RGB自然图像训练
   - 解决方案：探索专门针对医学/CT图像的预训练特征提取器，或考虑自定义特征提取方法

2. **教师模型的选择**
   - 挑战：缺乏针对CT图像预训练的教师模型
   - 解决方案：研究如何选择或训练适合的教师模型

3. **灰度图像处理**
   - 挑战：如何有效处理单通道CT图像用于感知损失计算
   - 解决方案：探索创建多对比度通道的方法，或寻找专门针对灰度图像的特征提取器

## 未来计划

1. 改进预训练阶段的感知损失实现
2. 研究并实现适合CT图像的特征模仿方法
3. 探索对比损失在CT图像超分辨率中的应用
4. 完成自监督预训练后，进入监督训练阶段
5. 评估不同损失函数组合对超分辨率重建质量的影响

## 参考资源

- Swin Transformer相关论文和实现
- MAE（Masked Autoencoder）相关研究
- 医学图像超分辨率相关文献
- 特征模仿和对比学习在图像处理中的应用

## 项目进展记录

### [日期]
- 初步实现Swin-MAE自监督预训练
- 讨论了多种损失函数的可能性

### [未来更新]
- 待记录项目的具体进展和实验结果
