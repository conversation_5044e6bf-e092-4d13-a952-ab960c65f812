import torch
import torch.nn as nn
import torchvision

class DeformConv(nn.Module):

    def __init__(self, in_channels, groups, kernel_size=(3,3), padding=1, stride=1, dilation=1, bias=True):
        super(DeformConv, self).__init__()
        
        self.offset_net = nn.Conv2d(in_channels=in_channels,
                                    out_channels=2 * kernel_size[0] * kernel_size[1],
                                    kernel_size=kernel_size,
                                    padding=padding,
                                    stride=stride,
                                    dilation=dilation,
                                    bias=True)

        self.deform_conv = torchvision.ops.DeformConv2d(in_channels=in_channels,
                                                        out_channels=in_channels,
                                                        kernel_size=kernel_size,
                                                        padding=padding,
                                                        groups=groups,
                                                        stride=stride,
                                                        dilation=dilation,
                                                        bias=False)

    def forward(self, x):
        offsets = self.offset_net(x)
        out = self.deform_conv(x, offsets)
        return out

class deformable_LKA(nn.Module):
    def __init__(self, dim):
        super().__init__()
        self.conv0 = DeformConv(dim, kernel_size=(5,5), padding=2, groups=dim)
        self.conv_spatial = DeformConv(dim, kernel_size=(7,7), stride=1, padding=9, groups=dim, dilation=3)
        self.conv1 = nn.Conv2d(dim, dim, 1)


    def forward(self, x):
        u = x.clone()
        attn = self.conv0(x)
        attn = self.conv_spatial(attn)
        attn = self.conv1(attn)

        return u * attn

class deformable_LKA_Attention(nn.Module):
    def __init__(self, d_model):
        super().__init__()

        self.proj_1 = nn.Conv2d(d_model, d_model, 1)
        self.activation = nn.GELU()
        self.spatial_gating_unit = deformable_LKA(d_model)
        self.proj_2 = nn.Conv2d(d_model, d_model, 1)

    def forward(self, x):
        shorcut = x.clone()
        x = self.proj_1(x)
        x = self.activation(x)
        x = self.spatial_gating_unit(x)
        x = self.proj_2(x)
        x = x + shorcut
        return x

# class DeformConv_3x3(nn.Module):
#
#     def __init__(self, in_channels, groups, kernel_size=(3,3), padding=1, stride=1, dilation=1, bias=True):
#         super(DeformConv, self).__init__()
#
#         self.offset_net = nn.Conv2d(in_channels=in_channels,
#                                     out_channels=2 * kernel_size[0] * kernel_size[1],
#                                     kernel_size=3,
#                                     padding=1,
#                                     stride=1,
#                                     bias=True)
#
#         self.deform_conv = torchvision.ops.DeformConv2d(in_channels=in_channels,
#                                                         out_channels=in_channels,
#                                                         kernel_size=kernel_size,
#                                                         padding=padding,
#                                                         groups=groups,
#                                                         stride=stride,
#                                                         dilation=dilation,
#                                                         bias=False)
#
#     def forward(self, x):
#         offsets = self.offset_net(x)
#         out = self.deform_conv(x, offsets)
#         return out
    

# class DeformConv_experimental(nn.Module):
#
#     def __init__(self, in_channels, groups, kernel_size=(3,3), padding=1, stride=1, dilation=1, bias=True):
#         super(DeformConv_experimental, self).__init__()
#
#         self.conv_channel_adjust = nn.Conv2d(in_channels=in_channels, out_channels=2 * kernel_size[0] * kernel_size[1], kernel_size=(1,1))
#
#         self.offset_net = nn.Conv2d(in_channels=2 * kernel_size[0] * kernel_size[1],
#                                     out_channels=2 * kernel_size[0] * kernel_size[1],
#                                     kernel_size=3,
#                                     padding=1,
#                                     stride=1,
#                                     groups=2 * kernel_size[0] * kernel_size[1],
#                                     bias=True)
#
#         self.deform_conv = torchvision.ops.DeformConv2d(in_channels=in_channels,
#                                                         out_channels=in_channels,
#                                                         kernel_size=kernel_size,
#                                                         padding=padding,
#                                                         groups=groups,
#                                                         stride=stride,
#                                                         dilation=dilation,
#                                                         bias=False)
#
#     def forward(self, x):
#         x_chan = self.conv_channel_adjust(x)
#         offsets = self.offset_net(x_chan)
#         out = self.deform_conv(x, offsets)
#         return out




# class deformable_LKA_experimental(nn.Module):
#     def __init__(self, dim):
#         super().__init__()
#         self.conv0 = DeformConv_experimental(dim, kernel_size=(5,5), padding=2, groups=dim)
#         self.conv_spatial = DeformConv_experimental(dim, kernel_size=(7,7), stride=1, padding=9, groups=dim, dilation=3)
#         self.conv1 = nn.Conv2d(dim, dim, 1)
#
#
#     def forward(self, x):
#         u = x.clone()
#         attn = self.conv0(x)
#         attn = self.conv_spatial(attn)
#         attn = self.conv1(attn)
#
#         return u * attn




# class deformable_LKA_Attention_experimental(nn.Module):
#     def __init__(self, d_model):
#         super().__init__()
#
#         self.proj_1 = nn.Conv2d(d_model, d_model, 1)
#         self.activation = nn.GELU()
#         self.spatial_gating_unit = deformable_LKA_experimental(d_model)
#         self.proj_2 = nn.Conv2d(d_model, d_model, 1)
#
#     def forward(self, x):
#         shorcut = x.clone()
#         x = self.proj_1(x)
#         x = self.activation(x)
#         x = self.spatial_gating_unit(x)
#         x = self.proj_2(x)
#         x = x + shorcut
#         return x
    
# from fvcore.nn import FlopCountAnalysis
#
# if __name__ == '__main__':
#     input = torch.rand(1,96,56,56).cuda(0)
#     lka_layer_exp = deformable_LKA_Attention_experimental(d_model=96).cuda(0)
#     lka_layer = deformable_LKA_Attention(d_model=96).cuda(0)
#
#     output = lka_layer(input)
#
#     flops = FlopCountAnalysis(lka_layer, input)
#     flops_exp = FlopCountAnalysis(lka_layer_exp, input)
#
#     n_parameters = sum(p.numel() for p in lka_layer.parameters() if p.requires_grad)
#     n_parameters_exp = sum(p.numel() for p in lka_layer_exp.parameters() if p.requires_grad)
#
#     model_flops = flops.total()
#     model_flops_exp = flops_exp.total()
#
#     print(f"Total trainable parameters: {round(n_parameters * 1e-3, 4)} K")
#     print(f"MAdds: {round(model_flops * 1e-6, 4)} M")
#     print(f"Total trainable parameters exp: {round(n_parameters_exp * 1e-3, 4)} K")
#     print(f"MAdds exp: {round(model_flops_exp * 1e-6, 4)} M")
#
#
#     print(f"output shape: {output.shape}")