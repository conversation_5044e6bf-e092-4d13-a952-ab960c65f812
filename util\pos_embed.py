import numpy as np

# --------------------------------------------------------
# 2D sine-cosine position embedding
# References:
# MoCo v3: https://github.com/facebookresearch/moco-v3
# MAE: https://github.com/facebookresearch/mae
# --------------------------------------------------------

def get_2d_sincos_pos_embed(embed_dim, grid_size, cls_token=False):
    """
    Generates 2D sine-cosine positional embeddings.

    Args:
        embed_dim (int): The dimension of the embeddings.
        grid_size (int): The size of the grid (assuming a square grid).
        cls_token (bool): Whether to add an embedding for the [CLS] token.

    Returns:
        np.ndarray: Positional embeddings with shape (grid_size*grid_size + cls_token, embed_dim)
                    or (1 + grid_size*grid_size, embed_dim) if cls_token is True.
    """
    # grid_size can be int or tuple (H_patch, W_patch)
    if isinstance(grid_size, int):
        grid_h_size = grid_w_size = grid_size
    else:
        grid_h_size, grid_w_size = grid_size

    grid_h = np.arange(grid_h_size, dtype=np.float32)
    grid_w = np.arange(grid_w_size, dtype=np.float32)
    grid = np.meshgrid(grid_w, grid_h)  # here w goes first
    grid = np.stack(grid, axis=0)

    # Use unpacked grid dimensions for reshape
    grid = grid.reshape([2, 1, grid_h_size, grid_w_size])
    pos_embed = get_2d_sincos_pos_embed_from_grid(embed_dim, grid)
    if cls_token:
        pos_embed = np.concatenate([np.zeros([1, embed_dim]), pos_embed], axis=0)
    return pos_embed


def get_2d_sincos_pos_embed_from_grid(embed_dim, grid):
    """
    Generates 2D sine-cosine positional embeddings from a grid.

    Args:
        embed_dim (int): The dimension of the embeddings.
        grid (np.ndarray): Grid coordinates with shape (2, 1, grid_size, grid_size).

    Returns:
        np.ndarray: Positional embeddings with shape (grid_size*grid_size, embed_dim).
    """
    assert embed_dim % 2 == 0

    # use half of dimensions to encode grid_h
    emb_h = get_1d_sincos_pos_embed_from_grid(embed_dim // 2, grid[0])  # (H*W, D/2)
    # use half of dimensions to encode grid_w
    emb_w = get_1d_sincos_pos_embed_from_grid(embed_dim // 2, grid[1])  # (H*W, D/2)

    emb = np.concatenate([emb_h, emb_w], axis=1) # (H*W, D)
    return emb


def get_1d_sincos_pos_embed_from_grid(embed_dim, pos):
    """
    Generates 1D sine-cosine positional embeddings from grid positions.

    Args:
        embed_dim (int): The dimension of the embeddings.
        pos (np.ndarray): Grid positions with shape (1, H, W) or (M,).

    Returns:
        np.ndarray: Positional embeddings with shape (M, embed_dim) or (H*W, embed_dim).
    """
    assert embed_dim % 2 == 0
    omega = np.arange(embed_dim // 2, dtype=np.float32)
    omega /= embed_dim / 2.
    omega = 1. / 10000**omega  # (D/2,)

    pos = pos.reshape(-1)  # (M,)
    out = np.einsum('m,d->md', pos, omega)  # (M, D/2), outer product

    emb_sin = np.sin(out) # (M, D/2)
    emb_cos = np.cos(out) # (M, D/2)

    emb = np.concatenate([emb_sin, emb_cos], axis=1)  # (M, D)
    return emb
