# -*- coding: utf-8 -*-
"""
Dataloader for the Super-Resolution (SR) training phase.
Handles loading High-Resolution (HR) images and synthetically
generating Low-Resolution (LR) counterparts for paired training.
"""

import torch
import torch.nn.functional as F
from torch.utils.data import Dataset, DataLoader
import os
from PIL import Image
import torchvision.transforms as T
import torchvision.transforms.functional as TF
import random
import numpy as np
import kornia.filters as KF # For Gaussian Blur
import kornia.geometry.transform as KGT # For resizing, rotation
import kornia.augmentation as KA # For noise
import math # For math operations like pi
import sys # Added for path manipulation
from multiprocessing import Pool, cpu_count
from functools import partial
import time
from tqdm import tqdm

# --- 并行图像验证函数 ---

def validate_single_image(args):
    """验证单个图像是否能提供有效patch（用于并行处理）"""
    img_path, patch_size_hr, image_size, valid_radius = args

    try:
        with Image.open(img_path) as img:
            width, height = img.size

            # 基本尺寸检查
            if width < patch_size_hr or height < patch_size_hr:
                return None

            # 转换为numpy进行详细检查
            img_array = np.array(img)
            if len(img_array.shape) == 3:
                img_array = img_array[:, :, 0]  # 取第一个通道

            # 生成圆形mask
            h, w = img_array.shape
            center_y, center_x = h // 2, w // 2
            radius_ratio = min(h, w) / image_size if image_size > 0 else 1.0
            current_valid_radius = valid_radius * radius_ratio
            Y, X = np.ogrid[:h, :w]
            dist_from_center = np.sqrt((X - center_x)**2 + (Y - center_y)**2)
            mask = dist_from_center <= current_valid_radius

            # 找到有效区域
            valid_coords = np.where(mask)
            if len(valid_coords[0]) == 0:
                return None

            # 检查有效区域的边界
            min_h, max_h = valid_coords[0].min(), valid_coords[0].max()
            min_w, max_w = valid_coords[1].min(), valid_coords[1].max()

            # 确保有效区域足够大，需要更严格的检查
            valid_height = max_h - min_h + 1
            valid_width = max_w - min_w + 1

            # 更严格的尺寸要求：需要至少2倍的patch_size来确保有足够的裁剪空间
            min_required_size = int(patch_size_hr * 2.0)  # 从1.5改为2.0，更严格

            # 调试信息：记录第一个图像的详细信息
            if 'FdkRecon-ushort-2700x2700x27001080.tif' in img_path:
                print(f"DEBUG: {os.path.basename(img_path)}")
                print(f"  图像尺寸: {w}x{h}")
                print(f"  有效区域: {valid_width}x{valid_height}")
                print(f"  要求尺寸: {min_required_size}")
                print(f"  通过检查: {valid_height >= min_required_size and valid_width >= min_required_size}")

            if valid_height >= min_required_size and valid_width >= min_required_size:
                return img_path
            else:
                return None

    except Exception as e:
        return None

# --- BSRGAN-style Degradation ---

def random_anisotropic_gaussian_kernel(kernel_size=21, sigma_min=0.2, sigma_max=4.0, angle_range=(-math.pi, math.pi)):
    """ Generates a random anisotropic Gaussian kernel """
    # Randomly sample parameters
    sigma_x = random.uniform(sigma_min, sigma_max)
    sigma_y = random.uniform(sigma_min, sigma_max)
    angle = random.uniform(angle_range[0], angle_range[1])

    # Create meshgrid
    center = kernel_size // 2
    x = torch.arange(kernel_size) - center
    y = torch.arange(kernel_size) - center
    xx, yy = torch.meshgrid(x, y, indexing='ij')

    # Rotate coordinates
    cos_a, sin_a = math.cos(angle), math.sin(angle)
    xx_rot = cos_a * xx + sin_a * yy
    yy_rot = -sin_a * xx + cos_a * yy

    # Calculate anisotropic Gaussian
    kernel = torch.exp(
        -( (xx_rot**2 / (2.0 * sigma_x**2)) + (yy_rot**2 / (2.0 * sigma_y**2)) )
    )
    # Normalize
    kernel = kernel / kernel.sum()
    return kernel.unsqueeze(0).unsqueeze(0) # Shape [1, 1, K, K]

def random_isotropic_gaussian_kernel(kernel_size=21, sigma_min=0.2, sigma_max=4.0):
     """ Generates a random isotropic Gaussian kernel """
     sigma = random.uniform(sigma_min, sigma_max)
     kernel = KF.get_gaussian_kernel2d((kernel_size, kernel_size), (sigma, sigma))
     return kernel.unsqueeze(0).unsqueeze(0) # Shape [1, 1, K, K]

def degrade_bsrgan_style(hr_tensor, config):
    """
    Applies a BSRGAN-style degradation pipeline with randomized parameters.
    Order: Blur -> Downsample -> Noise (can be shuffled)

    Args:
        hr_tensor (torch.Tensor): High-resolution image tensor (C, H, W), range [0, 1].
        config (dict): Configuration dictionary containing degradation parameters under 'data'.

    Returns:
        torch.Tensor: Low-resolution image tensor (C, H/sf, W/sf), range [0, 1].
    """
    data_config = config['data']
    scale_factor = data_config['scale_factor']
    device = hr_tensor.device

    # --- Degradation Parameters (from config or defaults) ---
    # Blur
    blur_prob = data_config.get('blur_prob', 1.0)
    blur_kernel_size = data_config.get('blur_kernel_size', 21)
    blur_sigma_range = data_config.get('blur_sigma_range', [0.2, 3.0])
    aniso_prob = data_config.get('aniso_prob', 0.5) # Probability of anisotropic blur vs isotropic
    # Downsampling
    downsample_prob = data_config.get('downsample_prob', 1.0)
    downsample_methods = data_config.get('downsample_methods', ['bicubic', 'bilinear', 'nearest'])
    # Noise
    noise_prob = data_config.get('noise_prob', 1.0)
    noise_gaussian_sigma_range = data_config.get('noise_gaussian_sigma_range', [1, 30]) # Sigma in range [0, 255]

    # Shuffle order? (Optional, BSRGAN does this)
    shuffle_prob = data_config.get('shuffle_prob', 0.5)
    degradation_order = ['blur', 'downsample', 'noise']
    if random.random() < shuffle_prob:
        random.shuffle(degradation_order)

    # --- Apply Degradations ---
    current_tensor = hr_tensor.float().unsqueeze(0) # Add batch dim for kornia/KA

    for step in degradation_order:
        if step == 'blur' and random.random() < blur_prob:
            if random.random() < aniso_prob:
                kernel = random_anisotropic_gaussian_kernel(
                    kernel_size=blur_kernel_size,
                    sigma_min=blur_sigma_range[0],
                    sigma_max=blur_sigma_range[1]
                ).to(device)
            else:
                kernel = random_isotropic_gaussian_kernel(
                    kernel_size=blur_kernel_size,
                    sigma_min=blur_sigma_range[0],
                    sigma_max=blur_sigma_range[1]
                ).to(device)
            # Apply kernel using filter2d
            current_tensor = KF.filter2d(current_tensor, kernel, border_type='reflect')

        elif step == 'downsample' and random.random() < downsample_prob:
            method_str = random.choice(downsample_methods)
            if method_str == 'bicubic':
                interp_mode = T.InterpolationMode.BICUBIC
            elif method_str == 'bilinear':
                interp_mode = T.InterpolationMode.BILINEAR
            else: # nearest
                interp_mode = T.InterpolationMode.NEAREST

            # 计算目标LR尺寸
            target_h = hr_tensor.shape[1] // scale_factor  # 使用原始HR尺寸计算
            target_w = hr_tensor.shape[2] // scale_factor

            # 确保目标尺寸合理
            if target_h < 16 or target_w < 16:
                print(f"DEBUG: 跳过下采样 - 目标尺寸太小: {target_h}x{target_w}")
                continue

            # 直接下采样到目标尺寸，而不是基于当前tensor尺寸
            current_tensor_chw = current_tensor.squeeze(0)
            downsampled_tensor = TF.resize(current_tensor_chw, size=[target_h, target_w], interpolation=interp_mode, antialias=(interp_mode != T.InterpolationMode.NEAREST))
            current_tensor = downsampled_tensor.unsqueeze(0) # Add batch dim back

        elif step == 'noise' and random.random() < noise_prob:
            sigma = random.uniform(noise_gaussian_sigma_range[0], noise_gaussian_sigma_range[1])
            # Kornia expects sigma in range [0, 1] if input is [0, 1]
            sigma_normalized = sigma / 255.0
            # Apply Gaussian noise
            current_tensor = KA.RandomGaussianNoise(mean=0.0, std=sigma_normalized, p=1.0)(current_tensor)

    # Remove batch dimension and clamp final output
    lr_tensor = torch.clamp(current_tensor.squeeze(0), 0, 1)

    # 最终尺寸检查和修正
    target_h = hr_tensor.shape[1] // scale_factor
    target_w = hr_tensor.shape[2] // scale_factor

    if lr_tensor.shape[1] != target_h or lr_tensor.shape[2] != target_w:
        print(f"DEBUG: 最终尺寸修正 - 当前: {lr_tensor.shape}, 目标: [C, {target_h}, {target_w}]")
        lr_tensor = TF.resize(lr_tensor, size=[target_h, target_w], interpolation=T.InterpolationMode.BICUBIC, antialias=True)

    return lr_tensor

class SRDataset(Dataset):
    """
    Dataset for SR training using synthetic LR generation (Strategy A).
    Loads HR images, generates LR counterparts, and provides paired patches.
    Enhanced with circular mask processing to avoid CT background regions.
    """
    def __init__(self, config):
        self.data_config = config['data']
        self.model_config = config['model']

        # --- Resolve Paths ---
        # Get project root (assuming sr_dataloader.py is in Self-supervised training network/data/)
        PROJECT_ROOT = os.path.abspath(os.path.join(os.path.dirname(__file__), '..', '..'))
        if PROJECT_ROOT not in sys.path:
             sys.path.insert(0, PROJECT_ROOT)

        # Construct absolute paths from relative paths in config
        self.hr_dir = os.path.join(PROJECT_ROOT, self.data_config['hr_dir'])
        # Also resolve LR dir path if needed later, though not used for loading here
        # self.lr_dir = os.path.join(PROJECT_ROOT, self.data_config['lr_dir'])
        print(f"Resolved HR directory: {self.hr_dir}")

        self.patch_size_hr = self.data_config['patch_size_hr']
        self.scale_factor = self.data_config['scale_factor'] # e.g., 4 if HR is 4x LR resolution
        self.patch_size_lr = self.patch_size_hr // self.scale_factor
        # Store full config for degradation function
        self.config = config
        self.input_channels = self.model_config.get('in_channels', 1) # Get expected channels

        # --- Circular Mask Parameters (following ct_loader.py pattern) ---
        self.image_size = 2700  # Original CT image size
        self.center = self.image_size // 2
        self.valid_radius = int(self.image_size * 0.45)  # Valid radius for CT region
        print(f"Circular mask parameters: center=({self.center}, {self.center}), radius={self.valid_radius}")

        # Check resolved HR directory
        if not os.path.isdir(self.hr_dir):
             print(f"Error initializing SRDataset: Resolved HR directory not found: {self.hr_dir}")
             print("Please check 'hr_dir' in your config file and ensure it's relative to the project root.")
             raise FileNotFoundError(f"Resolved HR directory not found: {self.hr_dir}")

        # Load all HR image file paths
        all_hr_files = sorted([
            os.path.join(self.hr_dir, f) for f in os.listdir(self.hr_dir)
            if f.lower().endswith(('.png', '.jpg', '.jpeg', '.tif', '.bmp'))
        ])

        if not all_hr_files:
            raise FileNotFoundError(f"No HR image files found in {self.hr_dir}")

        # Pre-filter images that can provide valid patches
        print(f"\n🔍 Pre-filtering {len(all_hr_files)} images for valid patch extraction...")

        # 尝试加载缓存（使用更严格的缓存key）
        cache_file = os.path.join(self.hr_dir, f'.valid_images_cache_{self.patch_size_hr}_strict.txt')
        print(f"📁 检查缓存文件: {os.path.basename(cache_file)}")

        # 暂时禁用缓存，强制重新验证以确保修复生效
        print("⚠️  强制重新验证所有图像（忽略缓存）")
        cached_valid_files = None

        if cached_valid_files is not None:
            print(f"🚀 从缓存快速加载了 {len(cached_valid_files)} 个有效图像 (跳过验证)")
            self.hr_files = cached_valid_files
        else:
            self.hr_files = self._filter_valid_images(all_hr_files)
            # 保存缓存
            self._save_cache(cache_file, self.hr_files)

        if not self.hr_files:
            raise ValueError(f"No valid images found that can provide {self.patch_size_hr}x{self.patch_size_hr} patches")

        # Basic transform: ToTensor (scales to [0, 1]) and Normalize to [-1, 1]
        self.normalize = T.Normalize((0.5,) * self.input_channels, (0.5,) * self.input_channels)

        print(f"SRDataset (Synthetic LR) initialized. Found {len(self.hr_files)} valid HR images (filtered out {len(all_hr_files) - len(self.hr_files)} invalid images).")
        print(f"HR Patch Size: {self.patch_size_hr}, LR Patch Size: {self.patch_size_lr}, Scale: {self.scale_factor}")
        print(f"Circular mask enabled to avoid CT background regions.")

    def _filter_valid_images(self, image_paths):
        """并行预过滤图像，只保留能提供有效patch的图像"""
        print(f"🔍 开始验证 {len(image_paths)} 张图像的有效性...")
        start_time = time.time()

        # 准备并行处理的参数
        args_list = [
            (img_path, self.patch_size_hr, self.image_size, self.valid_radius)
            for img_path in image_paths
        ]

        # 使用多进程并行处理
        num_processes = min(cpu_count(), 8)  # 限制最大进程数
        print(f"⚡ 使用 {num_processes} 个进程进行并行验证...")

        try:
            with Pool(processes=num_processes) as pool:
                # 使用imap_unordered获取结果，支持进度条
                results = []
                with tqdm(total=len(args_list),
                         desc="🖼️  验证图像",
                         unit="张",
                         ncols=100,
                         bar_format='{l_bar}{bar}| {n_fmt}/{total_fmt} [{elapsed}<{remaining}, {rate_fmt}]') as pbar:

                    for result in pool.imap_unordered(validate_single_image, args_list, chunksize=max(1, len(args_list)//num_processes//4)):
                        results.append(result)
                        pbar.update(1)

                        # 实时显示有效图像统计
                        valid_count = sum(1 for r in results if r is not None)
                        pbar.set_postfix({
                            '有效': f'{valid_count}/{len(results)}',
                            '比例': f'{valid_count/len(results)*100:.1f}%' if results else '0%'
                        })

            # 过滤掉None结果
            valid_images = [img_path for img_path in results if img_path is not None]

            elapsed_time = time.time() - start_time
            print(f"✅ 并行验证完成！耗时 {elapsed_time:.2f} 秒")
            print(f"📊 有效图像: {len(valid_images)}/{len(image_paths)} ({len(valid_images)/len(image_paths)*100:.1f}%)")
            print(f"⚡ 处理速度: {len(image_paths)/elapsed_time:.1f} 张/秒")

            return valid_images

        except Exception as e:
            print(f"❌ 并行处理失败，回退到串行处理: {e}")
            # 回退到串行处理
            return self._filter_valid_images_serial(image_paths)

    def _filter_valid_images_serial(self, image_paths):
        """串行预过滤图像（备用方案）"""
        valid_images = []
        print(f"🔄 串行验证 {len(image_paths)} 张图像...")
        start_time = time.time()

        # 使用tqdm显示进度条
        with tqdm(image_paths,
                 desc="🖼️  验证图像",
                 unit="张",
                 ncols=100,
                 bar_format='{l_bar}{bar}| {n_fmt}/{total_fmt} [{elapsed}<{remaining}, {rate_fmt}]') as pbar:

            for img_path in pbar:
                try:
                    # 详细检查图像是否能提供有效patch
                    with Image.open(img_path) as img:
                        width, height = img.size

                        # 基本尺寸检查
                        if width < self.patch_size_hr or height < self.patch_size_hr:
                            continue

                        # 转换为numpy进行更详细的检查
                        img_array = np.array(img)
                        if len(img_array.shape) == 3:
                            img_array = img_array[:, :, 0]  # 取第一个通道

                        # 应用圆形mask
                        mask = self.get_circle_mask(img_array.shape)

                        # 找到有效区域
                        valid_coords = np.where(mask > 0)
                        if len(valid_coords[0]) == 0:
                            continue

                        # 检查有效区域的边界
                        min_h, max_h = valid_coords[0].min(), valid_coords[0].max()
                        min_w, max_w = valid_coords[1].min(), valid_coords[1].max()

                        # 确保有效区域足够大，能提供至少一个完整的patch
                        valid_height = max_h - min_h + 1
                        valid_width = max_w - min_w + 1

                        if valid_height >= self.patch_size_hr and valid_width >= self.patch_size_hr:
                            valid_images.append(img_path)

                except Exception as e:
                    continue

                # 实时更新进度条信息
                pbar.set_postfix({
                    '有效': f'{len(valid_images)}',
                    '比例': f'{len(valid_images)/(pbar.n+1)*100:.1f}%' if pbar.n >= 0 else '0%'
                })

        elapsed_time = time.time() - start_time
        print(f"✅ 串行验证完成！耗时 {elapsed_time:.2f} 秒")
        print(f"📊 有效图像: {len(valid_images)}/{len(image_paths)} ({len(valid_images)/len(image_paths)*100:.1f}%)")
        print(f"⚡ 处理速度: {len(image_paths)/elapsed_time:.1f} 张/秒")
        return valid_images

    def _load_cache(self, cache_file, all_files):
        """加载有效图像缓存"""
        try:
            if not os.path.exists(cache_file):
                return None

            # 检查缓存文件的修改时间
            cache_mtime = os.path.getmtime(cache_file)

            # 检查是否有新的图像文件
            newest_img_mtime = max(os.path.getmtime(f) for f in all_files)

            if cache_mtime < newest_img_mtime:
                print("⚠️  检测到新的图像文件，缓存已过期")
                return None

            # 读取缓存
            with open(cache_file, 'r', encoding='utf-8') as f:
                cached_files = [line.strip() for line in f if line.strip()]

            # 验证缓存的文件是否仍然存在
            valid_cached_files = [f for f in cached_files if os.path.exists(f)]

            if len(valid_cached_files) != len(cached_files):
                print("⚠️  部分缓存文件不存在，重新验证")
                return None

            return valid_cached_files

        except Exception as e:
            print(f"加载缓存失败: {e}")
            return None

    def _save_cache(self, cache_file, valid_files):
        """保存有效图像缓存"""
        try:
            with open(cache_file, 'w', encoding='utf-8') as f:
                for file_path in valid_files:
                    f.write(file_path + '\n')
            print(f"💾 已保存 {len(valid_files)} 个有效图像到缓存")
        except Exception as e:
            print(f"保存缓存失败: {e}")

    def accurate_bsrgan_degrade(self, hr_tensor):
        """
        准确的BSRGAN降质过程：修复原始BSRGAN的尺寸问题
        hr_tensor: [C, H, W] tensor in [0, 1] range
        Returns: [C, H/sf, W/sf] LR tensor
        """
        # 计算目标LR尺寸（基于原始HR尺寸，不会改变）
        target_h = hr_tensor.shape[1] // self.scale_factor
        target_w = hr_tensor.shape[2] // self.scale_factor

        # 确保目标尺寸合理
        if target_h < 16 or target_w < 16:
            print(f"WARNING: Target LR size too small: {target_h}x{target_w}, using simple resize")
            return TF.resize(hr_tensor, size=[max(target_h, 16), max(target_w, 16)],
                           interpolation=T.InterpolationMode.BICUBIC, antialias=True)

        current_tensor = hr_tensor.unsqueeze(0)  # Add batch dimension

        # 获取降质参数
        data_config = self.config.get('data', {})

        # 状态跟踪：确保每种降质只执行一次
        applied_operations = {'blur': False, 'noise': False, 'downsample': False}

        # 1. 第一阶段：模糊处理
        blur_prob = data_config.get('blur_prob', 0.8)
        if random.random() < blur_prob:
            blur_type = random.choice(['gaussian', 'anisotropic'])
            if blur_type == 'gaussian':
                # 各向同性高斯模糊
                sigma = random.uniform(0.2, 3.0)
                kernel_size = int(2 * math.ceil(2 * sigma) + 1)
                current_tensor = KF.gaussian_blur2d(current_tensor, (kernel_size, kernel_size), (sigma, sigma))
            else:
                # 各向异性高斯模糊
                sigma_x = random.uniform(0.5, 4.0)
                sigma_y = random.uniform(0.5, 4.0)
                angle = random.uniform(0, math.pi)
                kernel_size = 21
                kernel = self._create_anisotropic_gaussian_kernel(kernel_size, sigma_x, sigma_y, angle)
                current_tensor = F.conv2d(current_tensor, kernel, padding=kernel_size//2)
            applied_operations['blur'] = True

        # 2. 第二阶段：噪声添加
        noise_prob = data_config.get('noise_prob', 0.8)
        if random.random() < noise_prob:
            noise_type = random.choice(['gaussian', 'poisson'])
            if noise_type == 'gaussian':
                # 高斯噪声
                noise_level = random.uniform(1, 30) / 255.0  # 更真实的噪声范围
                noise = torch.randn_like(current_tensor) * noise_level
                current_tensor = current_tensor + noise
            else:
                # 泊松噪声模拟
                # 将图像转换到更高的动态范围进行泊松采样
                scale = random.uniform(10, 100)
                current_tensor_scaled = current_tensor * scale
                current_tensor_noisy = torch.poisson(current_tensor_scaled) / scale
                current_tensor = current_tensor_noisy
            current_tensor = torch.clamp(current_tensor, 0, 1)
            applied_operations['noise'] = True

        # 3. 第三阶段：下采样（关键修复：确保只执行一次且尺寸正确）
        downsample_prob = data_config.get('downsample_prob', 1.0)  # 通常总是需要下采样
        if random.random() < downsample_prob:
            # 多种下采样方法
            downsample_methods = ['bicubic', 'bilinear', 'area', 'nearest']
            method = random.choice(downsample_methods)

            if method == 'bicubic':
                interp_mode = T.InterpolationMode.BICUBIC
            elif method == 'bilinear':
                interp_mode = T.InterpolationMode.BILINEAR
            elif method == 'area':
                # 使用area插值的近似实现
                interp_mode = T.InterpolationMode.BILINEAR  # PyTorch没有直接的area模式
            else:  # nearest
                interp_mode = T.InterpolationMode.NEAREST

            # 关键修复：直接下采样到目标尺寸，不基于当前tensor尺寸
            current_tensor_chw = current_tensor.squeeze(0)
            current_tensor = TF.resize(current_tensor_chw, size=[target_h, target_w],
                                     interpolation=interp_mode,
                                     antialias=(interp_mode != T.InterpolationMode.NEAREST)).unsqueeze(0)
            applied_operations['downsample'] = True

        # 4. 可选的JPEG压缩模拟
        jpeg_prob = data_config.get('jpeg_prob', 0.3)
        if random.random() < jpeg_prob:
            # 简化的JPEG压缩效果：轻微的块效应和质量损失
            quality_factor = random.uniform(0.8, 0.95)
            current_tensor = current_tensor * quality_factor
            current_tensor = torch.clamp(current_tensor, 0, 1)

        # 5. 最终处理
        lr_tensor = current_tensor.squeeze(0)

        # 最终尺寸验证和修正
        if lr_tensor.shape[1] != target_h or lr_tensor.shape[2] != target_w:
            print(f"DEBUG: 最终尺寸修正 - 当前: {lr_tensor.shape}, 目标: [C, {target_h}, {target_w}]")
            lr_tensor = TF.resize(lr_tensor, size=[target_h, target_w],
                                 interpolation=T.InterpolationMode.BICUBIC, antialias=True)

        return lr_tensor

    def _create_anisotropic_gaussian_kernel(self, kernel_size, sigma_x, sigma_y, angle):
        """创建各向异性高斯核 - 优化版本"""
        # 使用缓存的网格（如果可能）
        if not hasattr(self, '_cached_meshgrid') or self._cached_meshgrid[0] != kernel_size:
            center = kernel_size // 2
            x = torch.arange(kernel_size, dtype=torch.float32) - center
            y = torch.arange(kernel_size, dtype=torch.float32) - center
            xx, yy = torch.meshgrid(x, y, indexing='ij')
            self._cached_meshgrid = (kernel_size, xx, yy)
        else:
            _, xx, yy = self._cached_meshgrid

        # 旋转坐标（优化：预计算三角函数）
        cos_a, sin_a = math.cos(angle), math.sin(angle)
        xx_rot = cos_a * xx + sin_a * yy
        yy_rot = -sin_a * xx + cos_a * yy

        # 各向异性高斯（优化：避免重复计算）
        sigma_x_sq = sigma_x * sigma_x * 2
        sigma_y_sq = sigma_y * sigma_y * 2
        kernel = torch.exp(-(xx_rot**2 / sigma_x_sq + yy_rot**2 / sigma_y_sq))
        kernel = kernel / kernel.sum()

        return kernel.unsqueeze(0).unsqueeze(0)

    def fast_bsrgan_degrade(self, hr_tensor):
        """
        高性能的BSRGAN降质：保持准确性但优化速度
        hr_tensor: [C, H, W] tensor in [0, 1] range
        Returns: [C, H/sf, W/sf] LR tensor
        """
        # 计算目标LR尺寸
        target_h = hr_tensor.shape[1] // self.scale_factor
        target_w = hr_tensor.shape[2] // self.scale_factor

        # 确保目标尺寸合理
        if target_h < 16 or target_w < 16:
            return TF.resize(hr_tensor, size=[max(target_h, 16), max(target_w, 16)],
                           interpolation=T.InterpolationMode.BICUBIC, antialias=True)

        current_tensor = hr_tensor.unsqueeze(0)
        data_config = self.config.get('data', {})

        # 优化1：简化模糊处理（使用kornia的高效实现）
        blur_prob = data_config.get('blur_prob', 0.6)
        if random.random() < blur_prob:
            # 只使用高效的各向同性高斯模糊
            sigma = random.uniform(0.5, 2.5)
            kernel_size = int(2 * math.ceil(2 * sigma) + 1)
            current_tensor = KF.gaussian_blur2d(current_tensor, (kernel_size, kernel_size), (sigma, sigma))

        # 优化2：简化噪声处理（避免泊松噪声）
        noise_prob = data_config.get('noise_prob', 0.6)
        if random.random() < noise_prob:
            # 只使用高效的高斯噪声
            noise_level = random.uniform(1, 20) / 255.0
            noise = torch.randn_like(current_tensor) * noise_level
            current_tensor = torch.clamp(current_tensor + noise, 0, 1)

        # 优化3：高效下采样
        downsample_method = random.choice(['bicubic', 'bilinear'])
        interp_mode = T.InterpolationMode.BICUBIC if downsample_method == 'bicubic' else T.InterpolationMode.BILINEAR

        current_tensor_chw = current_tensor.squeeze(0)
        lr_tensor = TF.resize(current_tensor_chw, size=[target_h, target_w],
                             interpolation=interp_mode, antialias=True)

        # 最终尺寸验证
        if lr_tensor.shape[1] != target_h or lr_tensor.shape[2] != target_w:
            lr_tensor = TF.resize(lr_tensor, size=[target_h, target_w],
                                 interpolation=T.InterpolationMode.BICUBIC, antialias=True)

        return lr_tensor

    def rock_ct_adaptive_bsrgan_degrade(self, hr_tensor):
        """
        砂砾岩岩石CT图像适应性BSRGAN降质：针对岩石CT特性优化
        hr_tensor: [C, H, W] tensor in [0, 1] range (已归一化的岩石CT图像)
        Returns: [C, H/sf, W/sf] LR tensor
        """
        # 计算目标LR尺寸
        target_h = hr_tensor.shape[1] // self.scale_factor
        target_w = hr_tensor.shape[2] // self.scale_factor

        if target_h < 16 or target_w < 16:
            return TF.resize(hr_tensor, size=[max(target_h, 16), max(target_w, 16)],
                           interpolation=T.InterpolationMode.BICUBIC, antialias=True)

        current_tensor = hr_tensor.unsqueeze(0)
        data_config = self.config.get('data', {})

        # CT适应性参数
        ct_noise_prob = data_config.get('ct_noise_prob', 0.8)
        ct_blur_prob = data_config.get('ct_blur_prob', 0.6)
        ct_artifact_prob = data_config.get('ct_artifact_prob', 0.3)

        # 1. CT特异性模糊：主要是重建算法和部分容积效应
        if random.random() < ct_blur_prob:
            blur_type = random.choice(['gaussian', 'motion', 'defocus'])

            if blur_type == 'gaussian':
                # 轻微的高斯模糊，模拟重建平滑
                sigma = random.uniform(0.3, 1.5)  # CT图像模糊程度较小
                kernel_size = int(2 * math.ceil(2 * sigma) + 1)
                current_tensor = KF.gaussian_blur2d(current_tensor, (kernel_size, kernel_size), (sigma, sigma))

            elif blur_type == 'motion':
                # 轻微运动模糊，模拟患者移动
                kernel_size = random.randint(3, 7)
                if kernel_size % 2 == 0:
                    kernel_size += 1
                # 随机方向的运动模糊
                angle = random.uniform(0, 2 * math.pi)
                motion_kernel = self._create_motion_blur_kernel(kernel_size, angle)
                current_tensor = F.conv2d(current_tensor, motion_kernel, padding=kernel_size//2)

            elif blur_type == 'defocus':
                # 散焦模糊，模拟层厚效应
                sigma = random.uniform(0.5, 1.2)
                kernel_size = int(2 * math.ceil(2 * sigma) + 1)
                current_tensor = KF.gaussian_blur2d(current_tensor, (kernel_size, kernel_size), (sigma, sigma))

        # 2. CT特异性噪声：主要是量子噪声和电子噪声
        if random.random() < ct_noise_prob:
            noise_type = random.choice(['quantum', 'electronic', 'mixed'])

            if noise_type == 'quantum':
                # 量子噪声：泊松分布，与信号强度相关
                # 将[0,1]映射到合理的光子计数范围
                photon_scale = random.uniform(50, 500)  # 模拟不同剂量
                current_tensor_scaled = current_tensor * photon_scale
                # 添加泊松噪声
                noisy_scaled = torch.poisson(current_tensor_scaled)
                current_tensor = noisy_scaled / photon_scale

            elif noise_type == 'electronic':
                # 电子噪声：高斯分布，与信号无关
                noise_level = random.uniform(0.5, 8.0) / 255.0  # CT噪声水平较低
                noise = torch.randn_like(current_tensor) * noise_level
                current_tensor = current_tensor + noise

            elif noise_type == 'mixed':
                # 混合噪声：量子噪声 + 电子噪声
                # 量子噪声（主要）
                photon_scale = random.uniform(100, 300)
                current_tensor_scaled = current_tensor * photon_scale
                noisy_scaled = torch.poisson(current_tensor_scaled)
                current_tensor = noisy_scaled / photon_scale

                # 电子噪声（次要）
                noise_level = random.uniform(0.5, 3.0) / 255.0
                noise = torch.randn_like(current_tensor) * noise_level
                current_tensor = current_tensor + noise

            current_tensor = torch.clamp(current_tensor, 0, 1)

        # 3. CT特异性伪影模拟（可选）
        if random.random() < ct_artifact_prob:
            artifact_type = random.choice(['ring', 'streak', 'beam_hardening'])

            if artifact_type == 'ring':
                # 环形伪影：模拟探测器缺陷
                current_tensor = self._add_ring_artifacts(current_tensor)

            elif artifact_type == 'streak':
                # 条纹伪影：模拟金属伪影
                current_tensor = self._add_streak_artifacts(current_tensor)

            elif artifact_type == 'beam_hardening':
                # 射束硬化：模拟非线性衰减
                current_tensor = self._add_beam_hardening(current_tensor)

        # 4. CT适应性下采样：保持边缘锐度
        downsample_method = random.choice(['bicubic', 'lanczos'])  # 避免过度平滑
        if downsample_method == 'bicubic':
            interp_mode = T.InterpolationMode.BICUBIC
        else:
            # 使用双三次作为lanczos的近似
            interp_mode = T.InterpolationMode.BICUBIC

        current_tensor_chw = current_tensor.squeeze(0)
        lr_tensor = TF.resize(current_tensor_chw, size=[target_h, target_w],
                             interpolation=interp_mode, antialias=True)

        # 5. CT值范围保护：确保在合理范围内
        lr_tensor = torch.clamp(lr_tensor, 0, 1)

        # 最终尺寸验证
        if lr_tensor.shape[1] != target_h or lr_tensor.shape[2] != target_w:
            lr_tensor = TF.resize(lr_tensor, size=[target_h, target_w],
                                 interpolation=T.InterpolationMode.BICUBIC, antialias=True)

        return lr_tensor

    def sandstone_ct_adaptive_degrade(self, hr_tensor):
        """
        砂砾岩CT图像专用降质：针对岩石CT成像特性优化（GPU加速版）
        hr_tensor: [C, H, W] tensor in [0, 1] range
        Returns: [C, H/sf, W/sf] LR tensor
        """
        # 计算目标LR尺寸
        target_h = hr_tensor.shape[1] // self.scale_factor
        target_w = hr_tensor.shape[2] // self.scale_factor

        if target_h < 16 or target_w < 16:
            return TF.resize(hr_tensor, size=[max(target_h, 16), max(target_w, 16)],
                           interpolation=T.InterpolationMode.BICUBIC, antialias=True)

        # 移动到GPU进行计算（如果可用）
        device = hr_tensor.device
        current_tensor = hr_tensor.unsqueeze(0).to(device)
        data_config = self.config.get('data', {})

        # 砂砾岩CT特异性参数
        rock_noise_prob = data_config.get('rock_noise_prob', 0.9)
        rock_artifact_prob = data_config.get('rock_artifact_prob', 0.7)
        rock_blur_prob = data_config.get('rock_blur_prob', 0.5)

        # 1. 岩石CT特异性噪声：主要是量子噪声和散射噪声
        if random.random() < rock_noise_prob:
            noise_type = random.choice(['quantum_limited', 'scatter_noise', 'electronic'])

            if noise_type == 'quantum_limited':
                # 量子限制噪声：与X射线剂量相关
                # 砂砾岩通常需要较高剂量以穿透高密度矿物
                photon_count = random.uniform(200, 1000)  # 较高的光子计数
                current_tensor_scaled = current_tensor * photon_count
                noisy_scaled = torch.poisson(current_tensor_scaled)
                current_tensor = noisy_scaled / photon_count

            elif noise_type == 'scatter_noise':
                # 散射噪声：由于高密度矿物的康普顿散射
                scatter_intensity = random.uniform(0.02, 0.08)
                # 散射噪声通常是低频的，与局部密度相关
                scatter_noise = scatter_intensity * current_tensor * torch.randn_like(current_tensor)
                current_tensor = current_tensor + scatter_noise

            elif noise_type == 'electronic':
                # 电子噪声：探测器固有噪声
                noise_level = random.uniform(1.0, 6.0) / 255.0
                noise = torch.randn_like(current_tensor) * noise_level
                current_tensor = current_tensor + noise

            current_tensor = torch.clamp(current_tensor, 0, 1)

        # 2. 岩石CT特异性伪影
        if random.random() < rock_artifact_prob:
            artifact_type = random.choice(['beam_hardening', 'ring_artifacts', 'partial_volume'])

            if artifact_type == 'beam_hardening':
                # 射束硬化：砂砾岩中矿物密度差异大
                current_tensor = self._add_rock_beam_hardening(current_tensor)

            elif artifact_type == 'ring_artifacts':
                # 环形伪影：探测器响应不均匀
                current_tensor = self._add_rock_ring_artifacts(current_tensor)

            elif artifact_type == 'partial_volume':
                # 部分容积效应：小于体素的孔隙和颗粒
                current_tensor = self._add_partial_volume_effect(current_tensor)

        # 3. 岩石CT特异性模糊：主要是重建算法平滑和部分容积效应
        if random.random() < rock_blur_prob:
            blur_type = random.choice(['reconstruction_blur', 'partial_volume_blur'])

            if blur_type == 'reconstruction_blur':
                # 重建算法引起的轻微平滑
                sigma = random.uniform(0.2, 0.8)  # 较小的模糊
                kernel_size = int(2 * math.ceil(2 * sigma) + 1)
                current_tensor = KF.gaussian_blur2d(current_tensor, (kernel_size, kernel_size), (sigma, sigma))

            elif blur_type == 'partial_volume_blur':
                # 部分容积效应引起的边界模糊
                sigma = random.uniform(0.3, 1.0)
                kernel_size = int(2 * math.ceil(2 * sigma) + 1)
                # 使用轻微的各向异性模糊模拟部分容积效应
                sigma_x = sigma * random.uniform(0.8, 1.2)
                sigma_y = sigma * random.uniform(0.8, 1.2)
                current_tensor = KF.gaussian_blur2d(current_tensor, (kernel_size, kernel_size), (sigma_x, sigma_y))

        # 4. 岩石CT适应性下采样：保持孔隙结构的锐度
        # 砂砾岩的孔隙边界很重要，使用保边缘的下采样方法
        downsample_method = random.choice(['bicubic', 'lanczos'])
        interp_mode = T.InterpolationMode.BICUBIC  # 保持边缘锐度

        current_tensor_chw = current_tensor.squeeze(0)
        lr_tensor = TF.resize(current_tensor_chw, size=[target_h, target_w],
                             interpolation=interp_mode, antialias=True)

        # 5. 岩石CT值范围保护
        lr_tensor = torch.clamp(lr_tensor, 0, 1)

        # 最终尺寸验证
        if lr_tensor.shape[1] != target_h or lr_tensor.shape[2] != target_w:
            lr_tensor = TF.resize(lr_tensor, size=[target_h, target_w],
                                 interpolation=T.InterpolationMode.BICUBIC, antialias=True)

        return lr_tensor

    def fast_sandstone_ct_degrade(self, hr_tensor):
        """
        快速砂砾岩CT降质：优化训练速度，保持质量
        hr_tensor: [C, H, W] tensor in [0, 1] range
        Returns: [C, H/sf, W/sf] LR tensor
        """
        # 计算目标LR尺寸
        target_h = hr_tensor.shape[1] // self.scale_factor
        target_w = hr_tensor.shape[2] // self.scale_factor

        if target_h < 16 or target_w < 16:
            return TF.resize(hr_tensor, size=[max(target_h, 16), max(target_w, 16)],
                           interpolation=T.InterpolationMode.BICUBIC, antialias=True)

        # 移动到GPU进行计算（如果可用）
        device = hr_tensor.device
        current_tensor = hr_tensor.unsqueeze(0).to(device)

        # 简化的砂砾岩CT特异性参数（减少随机性，提高速度）
        rock_noise_prob = 0.8  # 固定概率，减少随机计算
        rock_blur_prob = 0.4   # 降低模糊概率，提高速度

        # 1. 简化的岩石CT噪声：只使用高效的高斯噪声
        if random.random() < rock_noise_prob:
            # 只使用高斯噪声，避免泊松噪声的CPU计算
            noise_level = random.uniform(2.0, 8.0) / 255.0
            noise = torch.randn_like(current_tensor) * noise_level
            current_tensor = torch.clamp(current_tensor + noise, 0, 1)

        # 2. 简化的模糊：只使用高效的高斯模糊
        if random.random() < rock_blur_prob:
            # 使用kornia的高效高斯模糊
            sigma = random.uniform(0.3, 1.0)
            kernel_size = int(2 * math.ceil(2 * sigma) + 1)
            current_tensor = KF.gaussian_blur2d(current_tensor, (kernel_size, kernel_size), (sigma, sigma))

        # 3. 高效下采样：直接使用双三次插值
        current_tensor_chw = current_tensor.squeeze(0)
        lr_tensor = TF.resize(current_tensor_chw, size=[target_h, target_w],
                             interpolation=T.InterpolationMode.BICUBIC, antialias=True)

        # 4. 范围保护
        lr_tensor = torch.clamp(lr_tensor, 0, 1)

        # 最终尺寸验证
        if lr_tensor.shape[1] != target_h or lr_tensor.shape[2] != target_w:
            lr_tensor = TF.resize(lr_tensor, size=[target_h, target_w],
                                 interpolation=T.InterpolationMode.BICUBIC, antialias=True)

        return lr_tensor

    def balanced_sandstone_ct_degrade(self, hr_tensor):
        """
        平衡的砂砾岩CT降质：在速度和质量之间取得平衡
        hr_tensor: [C, H, W] tensor in [0, 1] range
        Returns: [C, H/sf, W/sf] LR tensor
        """
        # 计算目标LR尺寸
        target_h = hr_tensor.shape[1] // self.scale_factor
        target_w = hr_tensor.shape[2] // self.scale_factor

        if target_h < 16 or target_w < 16:
            return TF.resize(hr_tensor, size=[max(target_h, 16), max(target_w, 16)],
                           interpolation=T.InterpolationMode.BICUBIC, antialias=True)

        # GPU加速计算
        device = hr_tensor.device
        current_tensor = hr_tensor.unsqueeze(0).to(device)
        data_config = self.config.get('data', {})

        # 获取降质强度设置
        intensity = data_config.get('degradation_intensity', 'medium')
        use_gpu_accel = data_config.get('use_gpu_acceleration', True)

        # 根据强度调整参数
        if intensity == 'light':
            noise_prob, artifact_prob, blur_prob = 0.6, 0.3, 0.3
        elif intensity == 'heavy':
            noise_prob, artifact_prob, blur_prob = 0.9, 0.8, 0.6
        else:  # medium
            noise_prob, artifact_prob, blur_prob = 0.8, 0.5, 0.4

        # 1. 智能噪声处理：根据GPU加速设置选择噪声类型
        if random.random() < noise_prob:
            if use_gpu_accel:
                # GPU加速：使用高效的高斯噪声
                noise_level = random.uniform(2.0, 8.0) / 255.0
                noise = torch.randn_like(current_tensor) * noise_level
                current_tensor = torch.clamp(current_tensor + noise, 0, 1)
            else:
                # 完整质量：使用泊松噪声（CPU计算）
                noise_type = random.choice(['quantum_limited', 'electronic'])
                if noise_type == 'quantum_limited':
                    photon_count = random.uniform(200, 800)
                    current_tensor_scaled = current_tensor * photon_count
                    noisy_scaled = torch.poisson(current_tensor_scaled)
                    current_tensor = noisy_scaled / photon_count
                else:
                    noise_level = random.uniform(1.0, 6.0) / 255.0
                    noise = torch.randn_like(current_tensor) * noise_level
                    current_tensor = current_tensor + noise
                current_tensor = torch.clamp(current_tensor, 0, 1)

        # 2. 选择性伪影处理：只在非GPU加速模式下添加复杂伪影
        if random.random() < artifact_prob and not use_gpu_accel:
            artifact_type = random.choice(['beam_hardening', 'ring_artifacts'])
            if artifact_type == 'beam_hardening':
                current_tensor = self._add_rock_beam_hardening(current_tensor)
            elif artifact_type == 'ring_artifacts':
                current_tensor = self._add_rock_ring_artifacts(current_tensor)

        # 3. 适度模糊：保持边缘信息
        if random.random() < blur_prob:
            sigma = random.uniform(0.3, 1.0)
            kernel_size = int(2 * math.ceil(2 * sigma) + 1)
            current_tensor = KF.gaussian_blur2d(current_tensor, (kernel_size, kernel_size), (sigma, sigma))

        # 4. 高质量下采样
        current_tensor_chw = current_tensor.squeeze(0)
        lr_tensor = TF.resize(current_tensor_chw, size=[target_h, target_w],
                             interpolation=T.InterpolationMode.BICUBIC, antialias=True)

        # 5. 范围保护
        lr_tensor = torch.clamp(lr_tensor, 0, 1)

        # 最终尺寸验证
        if lr_tensor.shape[1] != target_h or lr_tensor.shape[2] != target_w:
            lr_tensor = TF.resize(lr_tensor, size=[target_h, target_w],
                                 interpolation=T.InterpolationMode.BICUBIC, antialias=True)

        return lr_tensor

    def _add_rock_beam_hardening(self, tensor):
        """添加砂砾岩特异性射束硬化效应（优化版）"""
        _, _, h, w = tensor.shape
        center_y, center_x = h // 2, w // 2

        # 使用缓存的径向距离网格
        cache_key = (h, w)
        if not hasattr(self, '_radius_cache') or self._radius_cache.get('key') != cache_key:
            y, x = torch.meshgrid(torch.arange(h), torch.arange(w), indexing='ij')
            radius = torch.sqrt((x - center_x)**2 + (y - center_y)**2)
            max_radius = math.sqrt(center_x**2 + center_y**2)
            normalized_radius = radius / max_radius
            self._radius_cache = {'key': cache_key, 'normalized_radius': normalized_radius, 'radius': radius}
        else:
            normalized_radius = self._radius_cache.get('normalized_radius')
            if normalized_radius is None:
                # 如果缓存中没有normalized_radius，重新计算
                y, x = torch.meshgrid(torch.arange(h), torch.arange(w), indexing='ij')
                radius = torch.sqrt((x - center_x)**2 + (y - center_y)**2)
                max_radius = math.sqrt(center_x**2 + center_y**2)
                normalized_radius = radius / max_radius
                self._radius_cache['normalized_radius'] = normalized_radius
                self._radius_cache['radius'] = radius

        # 砂砾岩的射束硬化更复杂，因为矿物密度差异大
        hardening_intensity = random.uniform(0.08, 0.15)  # 比一般CT更强
        # 非线性杯状效应 + 局部密度相关的变化
        cupping_effect = hardening_intensity * (normalized_radius**2 - 0.3)

        # 添加与局部密度相关的变化（高密度区域硬化更严重）
        density_factor = tensor.squeeze(0).squeeze(0)  # 获取密度信息
        density_hardening = 0.05 * density_factor * (normalized_radius**1.5)

        total_hardening = cupping_effect + density_hardening
        artifact = total_hardening.unsqueeze(0).unsqueeze(0).to(tensor.device)
        return torch.clamp(tensor + artifact, 0, 1)

    def _add_rock_ring_artifacts(self, tensor):
        """添加砂砾岩CT特异性环形伪影（优化版）"""
        _, _, h, w = tensor.shape
        center_y, center_x = h // 2, w // 2

        # 复用缓存的径向坐标
        cache_key = (h, w)
        if not hasattr(self, '_radius_cache') or self._radius_cache.get('key') != cache_key:
            y, x = torch.meshgrid(torch.arange(h), torch.arange(w), indexing='ij')
            radius = torch.sqrt((x - center_x)**2 + (y - center_y)**2)
            max_radius = math.sqrt(center_x**2 + center_y**2)
            normalized_radius = radius / max_radius
            self._radius_cache = {'key': cache_key, 'normalized_radius': normalized_radius, 'radius': radius}
        else:
            radius = self._radius_cache.get('radius')
            if radius is None:
                # 如果缓存中没有radius，重新计算
                y, x = torch.meshgrid(torch.arange(h), torch.arange(w), indexing='ij')
                radius = torch.sqrt((x - center_x)**2 + (y - center_y)**2)
                self._radius_cache['radius'] = radius

        # 砂砾岩CT的环形伪影通常更明显，因为需要高能量X射线
        ring_intensity = random.uniform(0.03, 0.12)
        ring_frequency = random.uniform(0.05, 0.25)  # 较低频率

        # 多频率环形伪影叠加
        ring_pattern = ring_intensity * torch.sin(radius * ring_frequency)
        if random.random() < 0.5:  # 50%概率添加第二个频率
            ring_frequency2 = random.uniform(0.1, 0.4)
            ring_pattern += 0.5 * ring_intensity * torch.sin(radius * ring_frequency2)

        artifact = ring_pattern.unsqueeze(0).unsqueeze(0).to(tensor.device)
        return torch.clamp(tensor + artifact, 0, 1)

    def _add_partial_volume_effect(self, tensor):
        """添加部分容积效应：模拟小于体素的孔隙和颗粒"""
        # 部分容积效应主要表现为边界的模糊和灰度值的平均化

        # 创建随机的小尺度结构模拟
        pv_intensity = random.uniform(0.02, 0.08)

        # 使用小核的卷积模拟部分容积效应
        kernel_size = random.choice([3, 5])
        kernel = torch.ones(1, 1, kernel_size, kernel_size) / (kernel_size * kernel_size)
        kernel = kernel.to(tensor.device)

        # 应用部分容积平均
        pv_effect = F.conv2d(tensor, kernel, padding=kernel_size//2)

        # 混合原始图像和部分容积效应
        alpha = pv_intensity
        result = (1 - alpha) * tensor + alpha * pv_effect

        return torch.clamp(result, 0, 1)

    def _create_motion_blur_kernel(self, kernel_size, angle):
        """创建运动模糊核"""
        kernel = torch.zeros(kernel_size, kernel_size)
        center = kernel_size // 2

        # 创建线性运动模糊
        for i in range(kernel_size):
            x = i - center
            y = int(x * math.tan(angle))
            if abs(y) <= center:
                kernel[center + y, i] = 1.0

        # 归一化
        if kernel.sum() > 0:
            kernel = kernel / kernel.sum()
        else:
            kernel[center, center] = 1.0

        return kernel.unsqueeze(0).unsqueeze(0)

    def _add_ring_artifacts(self, tensor):
        """添加环形伪影（模拟探测器缺陷）"""
        _, _, h, w = tensor.shape
        center_y, center_x = h // 2, w // 2

        # 创建径向坐标
        y, x = torch.meshgrid(torch.arange(h), torch.arange(w), indexing='ij')
        radius = torch.sqrt((x - center_x)**2 + (y - center_y)**2)

        # 添加轻微的径向变化
        ring_intensity = random.uniform(0.02, 0.08)
        ring_frequency = random.uniform(0.1, 0.3)
        ring_pattern = ring_intensity * torch.sin(radius * ring_frequency)

        # 应用环形伪影
        artifact = ring_pattern.unsqueeze(0).unsqueeze(0).to(tensor.device)
        return torch.clamp(tensor + artifact, 0, 1)

    def _add_streak_artifacts(self, tensor):
        """添加条纹伪影（模拟金属伪影）"""
        _, _, h, w = tensor.shape

        # 随机选择条纹方向和强度
        streak_intensity = random.uniform(0.05, 0.15)
        num_streaks = random.randint(1, 3)

        artifact = torch.zeros_like(tensor)
        for _ in range(num_streaks):
            # 创建条纹模式（简化实现）
            streak_width = random.randint(1, 3)

            # 创建条纹模式（简化实现）
            if random.random() < 0.5:  # 水平条纹
                streak_pos = random.randint(streak_width, h - streak_width)
                artifact[:, :, streak_pos:streak_pos+streak_width, :] += streak_intensity
            else:  # 垂直条纹
                streak_pos = random.randint(streak_width, w - streak_width)
                artifact[:, :, :, streak_pos:streak_pos+streak_width] += streak_intensity

        return torch.clamp(tensor + artifact, 0, 1)

    def _add_beam_hardening(self, tensor):
        """添加射束硬化效应（模拟非线性衰减）"""
        # 射束硬化通常表现为中心亮、边缘暗的杯状伪影
        _, _, h, w = tensor.shape
        center_y, center_x = h // 2, w // 2

        # 创建径向距离
        y, x = torch.meshgrid(torch.arange(h), torch.arange(w), indexing='ij')
        radius = torch.sqrt((x - center_x)**2 + (y - center_y)**2)
        max_radius = math.sqrt(center_x**2 + center_y**2)
        normalized_radius = radius / max_radius

        # 创建杯状效应
        hardening_intensity = random.uniform(0.05, 0.12)
        cupping_effect = hardening_intensity * (normalized_radius**2 - 0.5)

        # 应用射束硬化
        artifact = cupping_effect.unsqueeze(0).unsqueeze(0).to(tensor.device)
        return torch.clamp(tensor + artifact, 0, 1)

    def simple_resize_degrade(self, hr_tensor):
        """
        简单的降质方法：仅进行下采样
        hr_tensor: [C, H, W] tensor in [0, 1] range
        Returns: [C, H/sf, W/sf] LR tensor
        """
        target_h = hr_tensor.shape[1] // self.scale_factor
        target_w = hr_tensor.shape[2] // self.scale_factor

        # 确保目标尺寸合理
        if target_h < 16 or target_w < 16:
            target_h = max(target_h, 16)
            target_w = max(target_w, 16)

        # 简单的双三次下采样
        lr_tensor = TF.resize(hr_tensor, size=[target_h, target_w],
                             interpolation=T.InterpolationMode.BICUBIC, antialias=True)

        return lr_tensor

    def get_circle_mask(self, image_shape):
        """生成二维圆形mask based on shape (following ct_loader.py pattern)"""
        h, w = image_shape
        center_y, center_x = h // 2, w // 2
        # Adjust radius based on actual image size relative to assumed 2700
        radius_ratio = min(h, w) / self.image_size if self.image_size > 0 else 1.0
        current_valid_radius = self.valid_radius * radius_ratio
        Y, X = np.ogrid[:h, :w]
        dist_from_center = np.sqrt((X - center_x)**2 + (Y - center_y)**2)
        return dist_from_center <= current_valid_radius

    def get_random_valid_coords(self, image_shape, patch_size):
        """获取有效区域内随机patch的左上角坐标 (following ct_loader.py pattern)"""
        mask = self.get_circle_mask(image_shape)
        valid_indices = np.argwhere(mask)
        if len(valid_indices) == 0 or image_shape[0] < patch_size or image_shape[1] < patch_size:
            return None, None

        attempts = 0
        max_attempts = 50
        h, w = image_shape

        while attempts < max_attempts:
            attempts += 1
            center_idx = random.choice(valid_indices)
            center_y, center_x = center_idx
            h_start = center_y - patch_size // 2
            w_start = center_x - patch_size // 2
            h_end = h_start + patch_size
            w_end = w_start + patch_size

            if (h_start >= 0 and w_start >= 0 and h_end <= h and w_end <= w):
                patch_mask = mask[h_start:h_end, w_start:w_end]
                if np.mean(patch_mask) >= 0.75: # Ensure patch is mostly valid (stricter than ct_loader.py)
                    return h_start, w_start
        return None, None

    def __len__(self):
        return len(self.hr_files)

    def __getitem__(self, idx):
        hr_img_path = self.hr_files[idx]
        try:
            # Load HR image
            hr_image_pil = Image.open(hr_img_path)
            # Ensure correct number of channels (e.g., grayscale 'L' or RGB 'RGB')
            if self.input_channels == 1 and hr_image_pil.mode != 'L':
                hr_image_pil = hr_image_pil.convert('L')
            elif self.input_channels == 3 and hr_image_pil.mode != 'RGB':
                 hr_image_pil = hr_image_pil.convert('RGB')
            elif hr_image_pil.mode not in ['L', 'RGB']: # Handle other modes if necessary
                 hr_image_pil = hr_image_pil.convert('L' if self.input_channels == 1 else 'RGB')

            # Convert HR to tensor [0, 1]
            hr_tensor = TF.to_tensor(hr_image_pil) # Shape: [C, H, W]

            # --- Synthetic LR Generation using BSRGAN-style ---
            # 根据配置选择降质方法
            degradation_method = self.data_config.get('degradation_method', 'accurate_bsrgan')

            if degradation_method == 'sandstone_ct':
                # 使用砂砾岩CT专用降质（推荐用于岩石CT图像）
                lr_tensor = self.sandstone_ct_adaptive_degrade(hr_tensor.float())
            elif degradation_method == 'fast_sandstone_ct':
                # 使用快速砂砾岩CT降质（优化训练速度）
                lr_tensor = self.fast_sandstone_ct_degrade(hr_tensor.float())
            elif degradation_method == 'balanced_sandstone_ct':
                # 使用平衡的砂砾岩CT降质（速度和质量平衡）
                lr_tensor = self.balanced_sandstone_ct_degrade(hr_tensor.float())
            elif degradation_method == 'ct_adaptive':
                # 使用通用CT适应性降质
                lr_tensor = self.ct_adaptive_bsrgan_degrade(hr_tensor.float())
            elif degradation_method == 'accurate_bsrgan':
                # 使用修复后的准确BSRGAN降质（慢但完整）
                lr_tensor = self.accurate_bsrgan_degrade(hr_tensor.float())
            elif degradation_method == 'fast_bsrgan':
                # 使用高性能BSRGAN降质（快速且准确）
                lr_tensor = self.fast_bsrgan_degrade(hr_tensor.float())
            elif degradation_method == 'original_bsrgan':
                # 使用原始BSRGAN降质（可能有尺寸问题）
                lr_tensor = degrade_bsrgan_style(hr_tensor.float(), self.config)
            else:
                # 默认使用简单降质
                lr_tensor = self.simple_resize_degrade(hr_tensor.float())

            # Shape: [C, H/sf, W/sf]

            # 调试信息：检查LR tensor尺寸
            if lr_tensor.shape[-1] < 100:  # 如果LR图像太小，记录调试信息
                print(f"DEBUG: 异常小的LR tensor - HR: {hr_tensor.shape}, LR: {lr_tensor.shape}, 文件: {os.path.basename(hr_img_path)}")

            # --- Enhanced Paired Random Cropping with Circular Mask ---
            # Convert tensor to numpy for mask processing
            hr_numpy = hr_tensor.squeeze(0).numpy() if hr_tensor.shape[0] == 1 else hr_tensor.permute(1, 2, 0).numpy()

            # Get valid crop coordinates using circular mask
            h_start, w_start = self.get_random_valid_coords(hr_numpy.shape[:2], self.patch_size_hr)

            if h_start is None:
                # 检查图像是否足够大
                if hr_numpy.shape[0] < self.patch_size_hr or hr_numpy.shape[1] < self.patch_size_hr:
                    # 图像太小，直接跳过（这种情况应该在预过滤中被排除）
                    raise ValueError(f"Image too small: {hr_numpy.shape} < {self.patch_size_hr} (should have been filtered)")

                # 如果圆形mask区域没有足够的有效patch，直接跳过
                # 不再使用center crop作为fallback，因为这可能产生无效的patch
                raise ValueError(f"No valid patch coordinates found in circular mask region for {hr_img_path}")

            # Apply crop to HR tensor
            hr_patch = TF.crop(hr_tensor, h_start, w_start, self.patch_size_hr, self.patch_size_hr)

            # Calculate corresponding crop parameters for LR tensor
            lr_h_start = h_start // self.scale_factor
            lr_w_start = w_start // self.scale_factor

            # 检查LR tensor尺寸
            if (lr_tensor.shape[-2] < self.patch_size_lr or
                lr_tensor.shape[-1] < self.patch_size_lr or
                lr_h_start + self.patch_size_lr > lr_tensor.shape[-2] or
                lr_w_start + self.patch_size_lr > lr_tensor.shape[-1]):
                raise ValueError(f"LR patch out of bounds: LR shape {lr_tensor.shape}, patch size {self.patch_size_lr}")

            # Apply crop to LR tensor
            lr_patch = TF.crop(lr_tensor, lr_h_start, lr_w_start, self.patch_size_lr, self.patch_size_lr)

            # --- Normalization ---
            # Normalize both patches to [-1, 1]
            hr_patch = self.normalize(hr_patch)
            lr_patch = self.normalize(lr_patch)

            # --- Augmentation (Optional) ---
            # Apply same random horizontal/vertical flip to both patches
            if random.random() > 0.5:
                hr_patch = TF.hflip(hr_patch)
                lr_patch = TF.hflip(lr_patch)
            if random.random() > 0.5:
                 hr_patch = TF.vflip(hr_patch)
                 lr_patch = TF.vflip(lr_patch)
            # Add rotation if needed

            return lr_patch, hr_patch

        except Exception as e:
            # 使用错误处理工具简化日志输出
            try:
                # 尝试导入错误处理工具
                from utils.error_handler import log_data_error
                log_data_error(hr_img_path, idx, e)
            except ImportError:
                # 如果导入失败，使用简化的错误处理
                error_msg = str(e)
                if len(error_msg) > 200:  # 截断过长的错误信息
                    error_msg = error_msg[:200] + "... [错误信息已截断]"
                print(f"处理图像时出错 {hr_img_path} (索引 {idx}): {error_msg}")

            # Return None to skip this sample (will be handled by collate function)
            return None


def filter_none_collate(batch):
    """自定义collate函数，过滤掉None值"""
    # 过滤掉None值
    batch = [item for item in batch if item is not None]

    if len(batch) == 0:
        # 如果整个batch都是None，创建一个最小的有效batch
        # 使用全零tensor，但保持正确的维度
        dummy_lr = torch.zeros(1, 1, 32, 32)  # 最小batch size = 1
        dummy_hr = torch.zeros(1, 1, 128, 128)
        return dummy_lr, dummy_hr

    # 如果batch太小，用第一个样本复制填充到最小batch size
    min_batch_size = 2  # 最小batch size
    while len(batch) < min_batch_size:
        batch.append(batch[0])  # 复制第一个样本

    # 使用默认的collate函数处理有效样本
    from torch.utils.data.dataloader import default_collate
    return default_collate(batch)


def get_sr_dataloader(config, batch_size, num_workers=4, shuffle=True):
    """
    Creates and returns a DataLoader for the SRDataset.
    """
    try:
        dataset = SRDataset(config)
    except FileNotFoundError as e:
        print(f"Error initializing SRDataset: {e}")
        print("Please check 'hr_dir' in your config file.")
        return None # Return None if dataset init fails

    if len(dataset) == 0:
        print("Warning: SRDataset is empty. Check HR image directory and file types.")
        return None

    dataloader = DataLoader(
        dataset,
        batch_size=batch_size,
        shuffle=shuffle,
        num_workers=num_workers,
        pin_memory=True,
        drop_last=True, # Important for consistent batch sizes during training
        collate_fn=filter_none_collate # Use custom collate function to handle None values
    )
    print(f"SR DataLoader created. Batches per epoch: {len(dataloader)}")
    return dataloader

# Example usage (for testing)
if __name__ == '__main__':
    print("Testing SR Dataloader (Synthetic LR)...")
    # Create dummy config for testing
    dummy_config = {
        'data': {
            'hr_dir': './dummy_hr_data_sr', # Use a different dummy dir name
            'patch_size_hr': 128,
            'scale_factor': 4,
            'degradation_kernel_size': 5,
            'degradation_sigma': 1.5,
        },
        'model': {
             'in_channels': 1 # Assuming grayscale
        }
    }
    dummy_data_config = dummy_config['data']
    patch_size_lr = dummy_data_config['patch_size_hr'] // dummy_data_config['scale_factor']

    # Create dummy directory and a few dummy images
    os.makedirs(dummy_data_config['hr_dir'], exist_ok=True)
    try:
        # Create images large enough for cropping
        Image.new('L', (200, 200)).save(os.path.join(dummy_data_config['hr_dir'], 'hr_1.png'))
        Image.new('L', (256, 192)).save(os.path.join(dummy_data_config['hr_dir'], 'hr_2.tif'))
        print("Dummy HR images created.")

        sr_loader_test = get_sr_dataloader(dummy_config, batch_size=2, num_workers=0)

        if sr_loader_test:
            # Fetch a batch
            print("Fetching a batch...")
            lr_batch_test, hr_batch_test = next(iter(sr_loader_test))
            print("LR Batch shape:", lr_batch_test.shape) # Should be [2, 1, 32, 32]
            print("HR Batch shape:", hr_batch_test.shape) # Should be [2, 1, 128, 128]
            print("LR Batch min/max:", lr_batch_test.min().item(), lr_batch_test.max().item()) # Should be approx [-1, 1]
            print("HR Batch min/max:", hr_batch_test.min().item(), hr_batch_test.max().item()) # Should be approx [-1, 1]
            print("Data loading test successful.")
        else:
            print("Dataloader creation failed.")

    except Exception as e:
        print(f"Error during dataloader test: {e}")
    finally:
        # Clean up dummy files/dirs
        import shutil
        if os.path.exists(dummy_data_config['hr_dir']):
            shutil.rmtree(dummy_data_config['hr_dir'])
        print("Cleaned up dummy data.")
