import torch
import torchvision
import numpy as np
import PIL
from PIL import Image
import os

def check_environment():
    """检查环境配置"""
    print("环境检查报告:")
    print("-" * 50)
    
    # 检查CUDA
    print(f"CUDA是否可用: {torch.cuda.is_available()}")
    if torch.cuda.is_available():
        print(f"CUDA版本: {torch.version.cuda}")
        print(f"当前设备: {torch.cuda.get_device_name(0)}")
        print(f"显存总量: {torch.cuda.get_device_properties(0).total_memory / 1024**3:.2f} GB")
        print(f"当前显存使用: {torch.cuda.memory_allocated(0) / 1024**3:.2f} GB")
        print(f"显存缓存: {torch.cuda.memory_reserved(0) / 1024**3:.2f} GB")
    
    # 检查PyTorch
    print(f"PyTorch版本: {torch.__version__}")
    print(f"torchvision版本: {torchvision.__version__}")
    
    # 检查其他库
    print(f"NumPy版本: {np.__version__}")
    print(f"PIL版本: {PIL.__version__}")
    
    # 检查目录结构
    required_dirs = ["checkpoints/enhanced", "checkpoints/pretrain", "logs/enhanced", "logs/pretrain"]
    for d in required_dirs:
        if not os.path.exists(d):
            print(f"创建目录: {d}")
            os.makedirs(d, exist_ok=True)
        else:
            print(f"目录已存在: {d}")
    
    print("-" * 50)
    print("环境检查完成!")

if __name__ == "__main__":
    check_environment()