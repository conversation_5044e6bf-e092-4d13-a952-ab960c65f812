import torch
import torchvision.transforms as transforms
import random
import torch.nn.functional as F

class RandomElasticTransform(object):
    """随机弹性变换"""
    def __init__(self, alpha=50, sigma=5):
        self.alpha = alpha
        self.sigma = sigma

    def __call__(self, img):
        if isinstance(img, torch.Tensor):
            img = transforms.ToPILImage()(img)

        random_state = random.Random()
        img = transforms.RandomAffine(
            degrees=0,
            translate=None,
            scale=None,
            shear=None,
            resample=0,  # PIL.Image.BILINEAR
            fillcolor=0
        )(img)
        
        if random_state.random() < 0.5:
            img = transforms.ToTensor()(img)
            
            # 生成随机位移场
            shape = img.shape
            dx = torch.rand(shape) * 2 - 1
            dy = torch.rand(shape) * 2 - 1
            
            # 平滑位移场
            dx = F.interpolate(dx.unsqueeze(0), scale_factor=self.sigma, mode='bicubic').squeeze(0)
            dy = F.interpolate(dy.unsqueeze(0), scale_factor=self.sigma, mode='bicubic').squeeze(0)
            
            # 归一化位移场
            dx = dx * self.alpha / shape[1]
            dy = dy * self.alpha / shape[2]
            
            # 应用位移场
            x = torch.arange(shape[1]).view(-1, 1).repeat(1, shape[2])
            y = torch.arange(shape[2]).repeat(shape[1], 1)
            x = x.float() + dx.squeeze()
            y = y.float() + dy.squeeze()
            
            # 归一化坐标
            x = 2 * x / (shape[1] - 1) - 1
            y = 2 * y / (shape[2] - 1) - 1
            grid = torch.stack([x, y], dim=-1).unsqueeze(0)
            
            # 重采样
            img = F.grid_sample(img.unsqueeze(0), grid, mode='bilinear', padding_mode='zeros').squeeze(0)
        
        return transforms.ToTensor()(img)

class RandomNoise(object):
    """随机噪声"""
    def __init__(self, mean=0, std=0.01):
        self.mean = mean
        self.std = std

    def __call__(self, img):
        noise = torch.randn(img.size()) * self.std + self.mean
        return img + noise

class DynamicDataAugmentation(transforms.Compose):
    """动态数据增强"""
    def __init__(self, transforms):
        super().__init__(transforms)

    def __call__(self, img):
        if random.random() < 0.5:  # 50%概率应用增强
            return super().__call__(img)
        else:
            return transforms.ToTensor()(img) if isinstance(img, Image.Image) else img
