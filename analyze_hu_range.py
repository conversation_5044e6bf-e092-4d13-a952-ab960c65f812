import os
import glob
import argparse
import numpy as np
import tifffile
from tqdm import tqdm
import random
import cv2 # Added for masking

def analyze_hu_values(input_dir, sample_size=None):
    """
    Analyzes HU value statistics (min, max, mean, std, percentiles)
    for TIF files in a directory.

    Args:
        input_dir (str): Path to the directory containing TIF files.
        sample_size (int, optional): Number of files to sample for analysis.
                                     If None, analyzes all files. Defaults to None.
    """
    tif_files = glob.glob(os.path.join(input_dir, '*.tif')) + \
                glob.glob(os.path.join(input_dir, '*.tiff'))

    if not tif_files:
        print(f"Error: No .tif or .tiff files found in '{input_dir}'")
        return

    if sample_size is not None and sample_size < len(tif_files):
        print(f"Sampling {sample_size} files out of {len(tif_files)}...")
        tif_files = random.sample(tif_files, sample_size)
    else:
        print(f"Analyzing all {len(tif_files)} files...")
        sample_size = len(tif_files) # For reporting purposes

    all_pixel_values = []
    global_min = float('inf')
    global_max = float('-inf')
    total_sum = 0
    total_pixels = 0
    pixel_values_for_percentiles = [] # Store values for percentile calculation

    print("Reading files, applying mask, and calculating basic stats...")
    # First pass: Calculate min, max, sum, count after applying mask
    for fpath in tqdm(tif_files, desc="Pass 1/2: Basic Stats"):
        try:
            img = tifffile.imread(fpath)
            if img.ndim != 2:
                print(f"\nWarning: Skipping non-2D file {os.path.basename(fpath)} (shape: {img.shape})")
                continue

            # --- Apply Circular Mask ---
            h, w = img.shape
            center_x, center_y = w // 2, h // 2
            image_radius = min(h, w) / 2
            mask_radius = int(image_radius * 0.43) # Calculate mask radius

            mask = np.zeros_like(img, dtype=np.uint8)
            cv2.circle(mask, (center_x, center_y), mask_radius, (255), thickness=-1)

            # Select pixels within the mask
            masked_pixels = img[mask == 255]
            # --- End Masking ---

            if masked_pixels.size == 0:
                 print(f"\nWarning: No pixels selected by mask in file {os.path.basename(fpath)}. Skipping.")
                 continue

            img_min = np.min(masked_pixels)
            img_max = np.max(masked_pixels)
            global_min = min(global_min, img_min)
            global_max = max(global_max, img_max)
            total_sum += np.sum(masked_pixels)
            total_pixels += masked_pixels.size
            # Store values for percentile calculation - potentially memory intensive!
            # Consider sampling pixels if memory becomes an issue
            pixel_values_for_percentiles.extend(masked_pixels.flatten()) # Use masked pixels
        except Exception as e:
            print(f"\nWarning: Could not read or process file {os.path.basename(fpath)}. Error: {e}")
            continue # Skip problematic files

    if total_pixels == 0:
        print("Error: No valid pixel data found in the analyzed files.")
        return

    mean_value = total_sum / total_pixels

    print("\nCalculating percentiles (this might take a while)...")
    # Calculate percentiles using the collected values
    try:
        # Convert to numpy array for efficient percentile calculation
        pixel_values_np = np.array(pixel_values_for_percentiles)
        percentile_1 = np.percentile(pixel_values_np, 1)
        percentile_99 = np.percentile(pixel_values_np, 99)
    except MemoryError:
        print("\nWarning: Not enough memory to calculate exact percentiles for all pixels.")
        print("Consider using a smaller sample_size or implementing pixel sampling within files.")
        percentile_1 = None
        percentile_99 = None
    except Exception as e:
         print(f"\nWarning: Could not calculate percentiles. Error: {e}")
         percentile_1 = None
         percentile_99 = None

    # Calculate standard deviation (requires another pass or storing all values)
    # For simplicity and memory efficiency with large datasets, we'll skip exact std dev for now.
    # If needed, it could be calculated using the mean and sum of squares,
    # but that requires storing sum of squares during the first pass.

    print("\n--- HU Value Analysis Results ---")
    print(f"Directory: {input_dir}")
    print(f"Files Analyzed: {sample_size}")
    print(f"Total Pixels Analyzed: {total_pixels}")
    print(f"Overall Minimum HU: {global_min:.2f}")
    print(f"Overall Maximum HU: {global_max:.2f}")
    print(f"Overall Mean HU: {mean_value:.2f}")
    # print(f"Overall Std Dev HU: {std_dev:.2f}") # Std Dev calculation omitted for memory efficiency

    if percentile_1 is not None and percentile_99 is not None:
        print(f"1st Percentile HU: {percentile_1:.2f}")
        print(f"99th Percentile HU: {percentile_99:.2f}")
        print("\nRecommendation:")
        print(f"Consider using --clip_min {percentile_1:.2f} and --clip_max {percentile_99:.2f} for training.")
    else:
        print("\nPercentiles could not be calculated (likely due to memory constraints).")
        print("Recommendation:")
        print(f"Start with the default or observed min/max: --clip_min {global_min:.2f} --clip_max {global_max:.2f}")
        print("Adjust based on visual inspection or domain knowledge.")
    print("---------------------------------")


if __name__ == "__main__":
    parser = argparse.ArgumentParser(description="Analyze HU value range in a directory of TIF files.")
    parser.add_argument('--input_dir', type=str, required=True,
                        help='Directory containing the TIF files.')
    parser.add_argument('--sample_size', type=int, default=None,
                        help='Number of files to sample for analysis (default: analyze all).')

    args = parser.parse_args()

    analyze_hu_values(args.input_dir, args.sample_size)
