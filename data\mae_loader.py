import os
import numpy as np
from PIL import Image
import torch
from torch.utils.data import Dataset, DataLoader
from torchvision import transforms
import random
import torchvision.transforms.functional as TF
import cv2 # Ensure cv2 is imported

# Helper function to filter None samples from the batch (for single tensor output)
def filter_none_tensor(batch):
    batch = list(filter(lambda x: x is not None, batch))
    if not batch:
        print("Warning: Entire batch filtered out.")
        # Return an empty tensor or handle as appropriate for your model input
        return torch.empty(0)
    # Use default_collate for a batch of tensors
    return torch.utils.data.dataloader.default_collate(batch)

class MAEDataset(Dataset):
    """Dataset for Masked Autoencoder (MAE) pretraining."""
    def __init__(self, data_dir, image_size=2700, crop_size=256, augment=True, clip_min=25912.00, clip_max=49833.00):
        super().__init__()
        self.data_paths = self._get_sorted_paths(data_dir)
        self.image_size = image_size # Original image size for mask calculation
        self.crop_size = crop_size   # Size of the random crop to feed to the model
        self.augment = augment
        self.center = self.image_size // 2
        # Adjust valid radius based on crop size relative to image size?
        # Let's keep it relative to original size for finding the initial center.
        self.valid_radius = int(self.image_size * 0.45)
        self.clip_min = clip_min
        self.clip_max = clip_max

        # self.transform and _get_mae_transform removed as transforms are handled in __getitem__

    # _get_mae_transform method removed

    def _get_sorted_paths(self, directory):
        files = [f for f in os.listdir(directory) if f.endswith('.tif')]
        files.sort()
        return [os.path.join(directory, f) for f in files]

    # _load_image method removed as loading is handled in __getitem__ with cv2

    def get_circle_mask(self, image_shape):
        """Generates a 2D circular mask based on the original image size."""
        h, w = image_shape
        center_y, center_x = h // 2, w // 2
        # Use the original image_size for consistent radius calculation
        radius_ratio = min(h, w) / self.image_size if self.image_size > 0 else 1.0
        current_valid_radius = self.valid_radius # Use original valid radius
        Y, X = np.ogrid[:h, :w]
        # Use float32 explicitly throughout the calculation
        center_x_f32 = np.float32(center_x)
        center_y_f32 = np.float32(center_y)
        X_f32 = X.astype(np.float32)
        Y_f32 = Y.astype(np.float32)
        dist_sq = np.square(X_f32 - center_x_f32) + np.square(Y_f32 - center_y_f32)
        # Note: np.sqrt might still return float64 if input is float32,
        # but the large intermediate array 'dist_sq' should now be float32.
        dist_from_center = np.sqrt(dist_sq)
        # Note: np.sqrt might still return float64 if input is float32,
        # but the large intermediate array 'dist_sq' should now be float32.
        dist_from_center = np.sqrt(dist_sq)
        return dist_from_center <= current_valid_radius

    def get_random_valid_coords(self, image_shape, crop_size):
        """
        Gets top-left coordinates for a random valid crop that is fully contained within the circle mask.
        Uses a memory-efficient sampling approach by checking the condition directly on the sampled crop.
        """
        h, w = image_shape
        if h < crop_size or w < crop_size:
             print(f"Warning: Image size {image_shape} < crop size {crop_size}. Skipping.")
             return None, None

        center_y, center_x = h // 2, w // 2
        # Use the original image_size for consistent radius calculation
        # radius_ratio = min(h, w) / self.image_size if self.image_size > 0 else 1.0 # Not needed if using original radius
        current_valid_radius = self.valid_radius # Use original valid radius
        sq_radius = current_valid_radius**2 # Compare squared distances

        attempts = 0
        max_attempts = 500 # Increase attempts further

        while attempts < max_attempts:
            attempts += 1
            # Sample a potential top-left corner (h_start, w_start)
            h_start = random.randint(0, h - crop_size)
            w_start = random.randint(0, w - crop_size)
            h_end = h_start + crop_size
            w_end = w_start + crop_size

            # Check if the *center* of the crop is within the radius (quick pre-check)
            crop_center_y = h_start + crop_size // 2
            crop_center_x = w_start + crop_size // 2
            if (crop_center_x - center_x)**2 + (crop_center_y - center_y)**2 > sq_radius:
                 continue # Center is outside, likely the whole crop is

            # More precise check: Check if the furthest corner (top-left in this case relative to center) is within radius
            # This is an approximation, assuming the circle center is near image center.
            # A truly robust check would involve checking all 4 corners or iterating pixels.
            # Let's check the top-left corner of the crop relative to the image center.
            # Calculate squared distance of the top-left corner of the crop from the image center
            dist_sq_tl = (w_start - center_x)**2 + (h_start - center_y)**2
            dist_sq_tr = (w_end - 1 - center_x)**2 + (h_start - center_y)**2
            dist_sq_bl = (w_start - center_x)**2 + (h_end - 1 - center_y)**2
            dist_sq_br = (w_end - 1 - center_x)**2 + (h_end - 1 - center_y)**2

            # If the furthest corner is within the radius, assume the crop is likely valid enough
            # This avoids creating the large mask array.
            if max(dist_sq_tl, dist_sq_tr, dist_sq_bl, dist_sq_br) <= sq_radius:
                 return h_start, w_start # Found a likely valid crop

            # --- Alternative (More Accurate but potentially slower): Check all pixels in crop ---
            # Y_crop, X_crop = np.ogrid[h_start:h_end, w_start:w_end]
            # center_x_f32 = np.float32(center_x)
            # center_y_f32 = np.float32(center_y)
            # X_crop_f32 = X_crop.astype(np.float32)
            # Y_crop_f32 = Y_crop.astype(np.float32)
            # dist_sq_crop = np.square(X_crop_f32 - center_x_f32) + np.square(Y_crop_f32 - center_y_f32)
            # if np.all(dist_sq_crop <= sq_radius):
            #      return h_start, w_start
            # --- End Alternative ---


        print(f"Warning: Could not find suitable crop location fully within the valid mask after {max_attempts} attempts for image shape {image_shape} and crop size {crop_size}. Skipping.")
        return None, None

    def __len__(self):
        return len(self.data_paths)

    def __getitem__(self, idx):
        img_path = self.data_paths[idx]

        # --- 1. Load TIF using cv2.imdecode ---
        try:
            with open(img_path, 'rb') as f:
                file_bytes = np.frombuffer(f.read(), dtype=np.uint8)
            img_cv = cv2.imdecode(file_bytes, cv2.IMREAD_UNCHANGED)
            if img_cv is None:
                raise ValueError(f"cv2.imdecode returned None for {img_path}")
            # Handle potential multi-channel TIF (take first channel if grayscale)
            if img_cv.ndim == 3:
                img_cv = img_cv[:, :, 0]
            if img_cv.dtype != np.uint16: # Assuming 16-bit TIFs based on previous logs
                 print(f"Warning: Expected uint16, got {img_cv.dtype} for {img_path}")
        except Exception as e_load:
            print(f"Warning: Failed to load image {img_path}: {e_load}. Skipping.")
            return None

        # Get original image shape
        original_shape = img_cv.shape[:2] # (H, W)

        # --- 2. Get random valid coordinates for the crop ---
        # (Assuming get_random_valid_coords works with numpy shape)
        h_start, w_start = self.get_random_valid_coords(original_shape, self.crop_size)
        if h_start is None:
            print(f"Warning: Could not get valid crop coords for {img_path}. Skipping.")
            return None

        # --- 3. Crop the NumPy array ---
        h_end, w_end = h_start + self.crop_size, w_start + self.crop_size
        img_cropped_np = img_cv[h_start:h_end, w_start:w_end].astype(np.float32) # Convert to float32 early

        # --- 4. Clip HU values and Normalize to [-1.0, 1.0] ---
        # Use instance attributes for clipping
        # clip_min = -1000.0 # Removed hardcoded
        # clip_max = 2000.0 # Removed hardcoded

        # Clip values
        img_clipped_np = np.clip(img_cropped_np, self.clip_min, self.clip_max)

        # Normalize clipped values to [-1, 1]
        denominator = self.clip_max - self.clip_min
        if denominator > 1e-6: # Avoid division by zero or near-zero
            img_normalized_neg1_1 = -1.0 + 2.0 * (img_clipped_np - self.clip_min) / denominator
        else:
            # Handle case where clip_max is very close to clip_min
            # Set to 0 (midpoint of [-1, 1])
            img_normalized_neg1_1 = np.zeros_like(img_clipped_np, dtype=np.float32)
            print(f"Warning: Clip range denominator near zero ({denominator}) for patch in {img_path}. Setting normalized to 0.")

        # Ensure the output is float32
        img_normalized_neg1_1 = img_normalized_neg1_1.astype(np.float32)

        # --- 5. Convert to Tensor ---
        # Add channel dimension: (H, W) -> (1, H, W)
        img_tensor_neg1_1 = torch.from_numpy(img_normalized_neg1_1).unsqueeze(0)

        # --- 6. Apply Augmentation (if enabled) ---
        if self.augment:
            if random.random() > 0.5:
                img_tensor_neg1_1 = TF.hflip(img_tensor_neg1_1)
            # Add other minimal MAE augmentations here if needed

        # --- 7. Return the [-1, 1] normalized tensor ---
        return img_tensor_neg1_1


def get_mae_loader(data_dir, batch_size=64, num_workers=4, image_size=2700, crop_size=256, augment=True, clip_min=25912.00, clip_max=49833.00):
    """获取 MAE 预训练的数据加载器 (使用基于图像块实际范围的归一化)"""
    dataset = MAEDataset(
        data_dir=data_dir,
        image_size=image_size,
        crop_size=crop_size,
        augment=augment,
        clip_min=clip_min, # Pass clip values
        clip_max=clip_max  # Pass clip values
    )
    loader = DataLoader(
        dataset,
        batch_size=batch_size,
        shuffle=True,
        num_workers=num_workers,
        pin_memory=True,
        drop_last=True, # Important for MAE if batch-specific ops are used later
        collate_fn=filter_none_tensor # Use collate_fn that handles None
    )
    return loader
