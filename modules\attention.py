import torch
import torch.nn as nn
import torch.nn.functional as F
import math

class WindowAttention(nn.Module):
    """基础窗口自注意力模块"""
    def __init__(self, dim, window_size=8, num_heads=8, qkv_bias=True, attn_drop=0., proj_drop=0.):
        super().__init__()
        self.dim = dim
        self.window_size = window_size
        self.num_heads = num_heads
        assert dim % num_heads == 0, f'Input channels {dim} must be divisible by num_heads {num_heads}'
        head_dim = dim // num_heads
        assert head_dim * num_heads == dim, f"dim {dim} must be divisible by num_heads {num_heads}"
        self.head_dim = head_dim
        self.qkv = nn.Linear(dim, 3 * dim, bias=qkv_bias)  # 修正维度计算匹配model_dim
        self.scale = self.head_dim ** -0.5
        
        # 验证维度设置
        assert dim == num_heads * head_dim, f"dim {dim} should equal num_heads {num_heads} * head_dim {head_dim}"
        
        self.attn_drop = nn.Dropout(attn_drop)
        self.proj = nn.Linear(dim, dim)
        self.proj_drop = nn.Dropout(proj_drop)
        
    def forward(self, x):
        B, C, H, W = x.shape
        
        # 计算需要的填充
        # 确保填充后尺寸是window_size整数倍
        pad_h = (self.window_size - H % self.window_size) % self.window_size
        pad_w = (self.window_size - W % self.window_size) % self.window_size
        H_pad = H + pad_h
        W_pad = W + pad_w
        
        # 验证窗口划分条件
        assert H_pad % self.window_size == 0, f"Height {H_pad} not divisible by window_size {self.window_size}"
        assert W_pad % self.window_size == 0, f"Width {W_pad} not divisible by window_size {self.window_size}"
        
        if pad_h > 0 or pad_w > 0:
            x = F.pad(x, (0, pad_w, 0, pad_h))
        
        _, _, H_pad, W_pad = x.shape
        x = x.permute(0, 2, 3, 1)  # B H W C
        
        # 分割窗口
        # 调整窗口划分维度计算
        num_windows_h = H_pad // self.window_size
        num_windows_w = W_pad // self.window_size
        x = x.view(B, num_windows_h, self.window_size, num_windows_w, self.window_size, C)
        x = x.permute(0, 1, 3, 2, 4, 5).contiguous()
        x = x.view(-1, self.window_size*self.window_size, C)
        
        # 自注意力计算
        # 修复：保持批次和序列长度维度进行线性变换
        B_win, N, C_dim = x.shape  # [B*num_windows, window_size*window_size, C]
        
        qkv = self.qkv(x)  # [B_win, N, 3*num_heads*head_dim]
        qkv = qkv.reshape(B_win, N, 3, self.num_heads, self.head_dim)
        qkv = qkv.permute(2, 0, 3, 1, 4)  # [3, B_win, num_heads, N, head_dim]
        
        q, k, v = qkv[0], qkv[1], qkv[2]  # 每个形状: [B_win, num_heads, N, head_dim]
        
        # 增加维度有效性检查
        assert q.shape[-1] == self.head_dim, \
            f"Query dim {q.shape[-1]} != head_dim {self.head_dim}"
        assert k.shape[-1] == self.head_dim, \
            f"Key dim {k.shape[-1]} != head_dim {self.head_dim}"
        
        attn = (q @ k.transpose(-2, -1)) * self.scale  # [B_win, num_heads, N, N]
        attn = attn.softmax(dim=-1)
        attn = self.attn_drop(attn)
        
        x = (attn @ v).transpose(1, 2).reshape(B_win, N, C_dim)  # [B_win, N, C]
        
        # 应用线性投影
        # 修复：同样需要处理维度
        x = self.proj(x.reshape(B_win*N, C_dim)).reshape(B_win, N, C_dim)
        x = self.proj_drop(x)
        
        # 合并窗口
        x = x.view(B, num_windows_h, num_windows_w, self.window_size, self.window_size, C)
        x = x.permute(0, 1, 3, 2, 4, 5).contiguous()
        x = x.view(B, H_pad, W_pad, C)
        
        # 如果之前进行了填充，这里需要移除填充
        if pad_h > 0 or pad_w > 0:
            x = x[:, :H, :W, :]
        
        return x.permute(0, 3, 1, 2)  # B C H W

class ShiftWindowAttention(nn.Module):
    """移位窗口注意力，增强窗口间信息交流"""
    def __init__(self, dim, window_size=8, shift_size=4, num_heads=8, qkv_bias=True, attn_drop=0., proj_drop=0.):
        super().__init__()
        self.dim = dim
        self.window_size = window_size
        self.shift_size = shift_size
        self.num_heads = num_heads
        
        self.window_attn = WindowAttention(
            dim=dim,
            window_size=window_size,
            num_heads=num_heads,
            qkv_bias=qkv_bias,
            attn_drop=attn_drop,
            proj_drop=proj_drop
        )
        
    def forward(self, x):
        B, C, H, W = x.shape
        
        # 常规窗口注意力
        x1 = self.window_attn(x)
        
        # 移位窗口注意力
        # 生成移位后的特征图
        shifted_x = torch.roll(x, shifts=(self.shift_size, self.shift_size), dims=(2, 3))
        # 移位前进行填充确保窗口划分正确
        _, _, H, W = shifted_x.shape
        pad_h = (self.window_size - H % self.window_size) % self.window_size
        pad_w = (self.window_size - W % self.window_size) % self.window_size
        shifted_x = F.pad(shifted_x, (0, pad_w, 0, pad_h))
        
        # 验证移位后的窗口划分
        B, C, H_pad, W_pad = shifted_x.shape
        num_windows_h = H_pad // self.window_size
        num_windows_w = W_pad // self.window_size
        assert (H_pad % self.window_size) == 0, f"Shifted height {H_pad} must be divisible by window_size {self.window_size}"
        assert (W_pad % self.window_size) == 0, f"Shifted width {W_pad} must be divisible by window_size {self.window_size}"
        
        x2 = self.window_attn(shifted_x)
        # 移除填充并恢复原始尺寸
        x2 = x2[:, :, :H, :W]
        x2 = torch.roll(x2, shifts=(self.shift_size, self.shift_size), dims=(2, 3))
        
        return x1 + x2

class ChannelAttention(nn.Module):
    """通道注意力模块 (使用Linear层)"""
    def __init__(self, channels, reduction=16):
        super().__init__()
        self.avg_pool = nn.AdaptiveAvgPool2d(1)
        self.max_pool = nn.AdaptiveMaxPool2d(1)

        # 使用Linear层代替Conv2d
        self.fc = nn.Sequential(
            nn.Linear(channels, channels // reduction, bias=False),
            nn.ReLU(inplace=True),
            nn.Linear(channels // reduction, channels, bias=False)
        )
        self.sigmoid = nn.Sigmoid()

    def forward(self, x):
        b, c, _, _ = x.size()
        # 平均池化和最大池化，输出 [b, c, 1, 1]
        avg_pooled = self.avg_pool(x)
        max_pooled = self.max_pool(x)

        # 移除空间维度 -> [b, c]
        avg_flat = avg_pooled.view(b, c)
        max_flat = max_pooled.view(b, c)

        # 通过全连接层
        avg_out = self.fc(avg_flat)
        max_out = self.fc(max_flat)

        # 合并结果并应用Sigmoid
        out = self.sigmoid(avg_out + max_out) # [b, c]

        # 扩展维度以匹配输入 x -> [b, c, 1, 1]
        out_expanded = out.view(b, c, 1, 1)

        # 应用注意力
        return x * out_expanded

class SpatialAttention(nn.Module):
    """空间注意力模块"""
    def __init__(self, kernel_size=7):
        super().__init__()
        self.conv = nn.Conv2d(2, 1, kernel_size, padding=kernel_size//2, bias=False)
        self.sigmoid = nn.Sigmoid()
        
    def forward(self, x):
        avg_out = torch.mean(x, dim=1, keepdim=True)
        max_out, _ = torch.max(x, dim=1, keepdim=True)
        y = torch.cat([avg_out, max_out], dim=1)
        y = self.conv(y)
        return x * self.sigmoid(y)

class DualAttention(nn.Module):
    """通道-空间双重注意力模块"""
    def __init__(self, channels, reduction=16):
        super().__init__()
        self.channel_attn = ChannelAttention(channels, reduction)
        self.spatial_attn = SpatialAttention()
        # self.query_conv = nn.Conv2d(channels, channels, kernel_size=1) # Unused

    def forward(self, x):
        # Input x should be [B, C, H, W]
        if x.dim() != 4:
             # If input is [B, C, 1, H, W], squeeze it. Otherwise, raise error.
             if x.dim() == 5 and x.shape[2] == 1:
                 x = x.squeeze(2)
             else:
                 raise ValueError(f"DualAttention expects 4D input [B, C, H, W], but got {x.shape}")

        # Apply Channel Attention first
        x_channel_att = self.channel_attn(x) # Output shape should be same as input: [B, C, H, W]

        # Apply Spatial Attention to the output of Channel Attention
        # SpatialAttention's forward applies attention and returns the attended features
        x_dual_att = self.spatial_attn(x_channel_att) # Output shape: [B, C, H, W]

        # Return the features after both attentions have been applied
        return x_dual_att

class CrossScaleAttention(nn.Module):
    """跨尺度注意力聚合模块"""
    def __init__(self, channels, scales=(1, 2, 4)):
        super().__init__()
        self.scales = scales
        self.pools = nn.ModuleList([
            nn.AvgPool2d(scale, stride=scale) for scale in scales
        ])
        self.fusions = nn.ModuleList([
            nn.Conv2d(channels, channels, kernel_size=1) for _ in range(len(scales))
        ])
        self.attention = DualAttention(channels)
        self.output_conv = nn.Conv2d(channels, channels, kernel_size=1)
        
    def forward(self, x):
        B, C, H, W = x.shape
        multi_scale_features = []
        
        # 多尺度特征提取
        for i, (pool, fusion) in enumerate(zip(self.pools, self.fusions)):
            if i == 0:  # 原始尺度
                feat = fusion(x)
            else:
                # 下采样然后上采样回原始尺度
                pooled = pool(x)
                feat = F.interpolate(
                    fusion(pooled), 
                    size=(H, W), 
                    mode='bilinear', 
                    align_corners=False
                )
            multi_scale_features.append(feat)
            
        # 特征融合
        fused = sum(multi_scale_features)
        
        # 应用注意力
        attended = self.attention(fused)
        
        return self.output_conv(attended) + x


class MultiScaleCrossAttention(nn.Module):
    """
    跨尺度注意力聚合模块 - 接收来自不同输入尺度Patch的特征图字典。
    将特征图调整至参考尺寸，拼接，投影，然后应用注意力机制。
    """
    def __init__(self, channels, num_scales=3, reference_scale_index=0, reduction=16):
        """
        Args:
            channels (int): 每个尺度特征图的通道数。
            num_scales (int): 输入尺度的数量。
            reference_scale_index (int): 用于确定参考空间尺寸的特征图索引 (基于排序后的尺度列表)。
                                         例如，0 表示最小尺度的特征图尺寸。
            reduction (int): DualAttention 中通道缩减的因子。
        """
        super().__init__()
        self.channels = channels
        self.num_scales = num_scales
        self.reference_scale_index = reference_scale_index

        # 投影层，将拼接后的特征 (C * num_scales) 投影回 C
        self.projection = nn.Conv2d(channels * num_scales, channels, kernel_size=1, bias=False)
        self.bn = nn.BatchNorm2d(channels)
        self.relu = nn.ReLU(inplace=True)

        # 应用于融合后特征的注意力模块
        self.attention = DualAttention(channels, reduction)

        # 可选的最终输出卷积
        self.output_conv = nn.Conv2d(channels, channels, kernel_size=1)

    def forward(self, multi_scale_features: dict):
        """
        Args:
            multi_scale_features (dict): 字典，键是patch大小(int)，值是对应的特征张量 [B, C, H, W]。
                                         例如: {32: tensor1, 64: tensor2, 128: tensor3}
        Returns:
            torch.Tensor: 融合后的特征张量 [B, C, H_ref, W_ref]。
        """
        if not isinstance(multi_scale_features, dict) or not multi_scale_features:
            raise ValueError("Input 'multi_scale_features' must be a non-empty dictionary.")
        
        if len(multi_scale_features) != self.num_scales:
             print(f"Warning: Expected {self.num_scales} scales, but received {len(multi_scale_features)}. Adjusting num_scales internally.")
             self.num_scales = len(multi_scale_features)
             # Re-initialize projection layer based on actual number of scales received
             self.projection = nn.Conv2d(self.channels * self.num_scales, self.channels, kernel_size=1, bias=False).to(next(iter(multi_scale_features.values())).device)


        # 按 patch 大小对键进行排序，以确保一致的处理顺序
        sorted_sizes = sorted(multi_scale_features.keys())
        features = [multi_scale_features[size] for size in sorted_sizes]

        # 检查通道数是否一致
        for i, feat in enumerate(features):
            if feat.shape[1] != self.channels:
                raise ValueError(f"Feature at scale {sorted_sizes[i]} has {feat.shape[1]} channels, expected {self.channels}.")

        # 确定参考尺寸 (例如，来自最小或最大尺度的特征图)
        # reference_scale_index=0 -> 最小尺度; reference_scale_index=-1 -> 最大尺度
        ref_feature = features[self.reference_scale_index]
        ref_size = ref_feature.shape[-2:] # (H_ref, W_ref)

        resized_features = []
        for i, feat in enumerate(features):
            if feat.shape[-2:] == ref_size:
                resized_features.append(feat)
            else:
                # 使用双线性插值将其他尺度的特征图调整到参考尺寸
                resized_feat = F.interpolate(feat, size=ref_size, mode='bilinear', align_corners=False)
                resized_features.append(resized_feat)

        # 沿通道维度拼接调整尺寸后的特征图
        # fused_features shape: [B, C * num_scales, H_ref, W_ref]
        fused_features = torch.cat(resized_features, dim=1)

        # 投影回原始通道数 C
        # projected_features shape: [B, C, H_ref, W_ref]
        projected_features = self.relu(self.bn(self.projection(fused_features)))

        # 应用双重注意力机制
        attended_features = self.attention(projected_features)

        # 可选：添加残差连接 (例如，与参考尺度的特征相加)
        # attended_features = attended_features + ref_feature

        # 最终输出卷积
        output = self.output_conv(attended_features)

        return output
