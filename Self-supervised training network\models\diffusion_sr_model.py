# -*- coding: utf-8 -*-
"""
Defines the Conditional U-Net model for the diffusion process
and potentially integrates the pretrained encoder for conditioning.
"""

import torch
import torch.nn as nn
import torch.nn.functional as F
import math # Added for time embedding
import os # Added for path
import sys # Added for path checking during model loading
import numpy as np # Added for pos_embed
import timm # Added for Swin Transformer
from einops import rearrange, repeat # Needed for CrossAttention placeholder
from torch import einsum # Needed for CrossAttention placeholder
assert timm.__version__ >= "0.3.2" # MAE requires timm support, assert version
# from timm.models.vision_transformer import Block as ViTBlock # Use timm's Block for ViT-style decoder # Not used directly here
from timm.models.layers import DropPath, to_2tuple, trunc_normal_ # For Swin Attention
from timm.models.swin_transformer import SwinTransformer # To potentially access internals if needed

# --- Imports from project ---
# Assuming diffusion_utils.py is in ../utils relative to this file
# Assuming attention.py is in ../../modules relative to this file
import sys
# Add project root to path to allow absolute imports like 'modules.attention'
PROJECT_ROOT_GUESS = os.path.abspath(os.path.join(os.path.dirname(__file__), '..', '..'))
if PROJECT_ROOT_GUESS not in sys.path:
    sys.path.insert(0, PROJECT_ROOT_GUESS)
    print(f"Added {PROJECT_ROOT_GUESS} to sys.path for imports")

try:
    from modules.attention import CrossAttention # Import the actual CrossAttention
except ImportError:
    print("Warning: Could not import CrossAttention from modules.attention. Using placeholder.")
    # --- Cross Attention Placeholder ---
    # Using a simplified version based on common implementations
    # Requires einops: pip install einops
    class CrossAttention(nn.Module):
        def __init__(self, query_dim, context_dim=None, heads=8, dim_head=64, dropout=0.):
            super().__init__()
            inner_dim = dim_head * heads
            context_dim = context_dim if context_dim is not None else query_dim
            self.scale = dim_head ** -0.5
            self.heads = heads

            self.to_q = nn.Linear(query_dim, inner_dim, bias=False)
            self.to_k = nn.Linear(context_dim, inner_dim, bias=False)
            self.to_v = nn.Linear(context_dim, inner_dim, bias=False)

            self.to_out = nn.Sequential(
                nn.Linear(inner_dim, query_dim),
                nn.Dropout(dropout)
            )
            # print(f"CrossAttention: q={query_dim}, c={context_dim}, heads={heads}, dim_head={dim_head}") # Debug

        def forward(self, x, context=None, mask=None):
            # x shape: (B, HW, C_query)
            # context shape: (B, N_context, C_context)
            h = self.heads
            q = self.to_q(x)
            context = context if context is not None else x
            k = self.to_k(context)
            v = self.to_v(context)

            q, k, v = map(lambda t: rearrange(t, 'b n (h d) -> (b h) n d', h=h), (q, k, v))

            # Attention score calculation
            sim = einsum('b i d, b j d -> b i j', q, k) * self.scale

            if mask is not None:
                mask = rearrange(mask, 'b ... -> b (...)')
                max_neg_value = -torch.finfo(sim.dtype).max
                mask = repeat(mask, 'b j -> (b h) () j', h=h)
                sim.masked_fill_(~mask, max_neg_value)

            attn = sim.softmax(dim=-1)

            out = einsum('b i j, b j d -> b i d', attn, v)
            out = rearrange(out, '(b h) n d -> b n (h d)', h=h)
            return self.to_out(out)

try:
    from ..utils.diffusion_utils import get_timestep_embedding, get_diffusion_schedule, ddim_sample_loop
except ImportError:
     print("Warning: Could not import diffusion helpers from ..utils.diffusion_utils. Using placeholders.")
     # --- Helper Functions (Placeholders if import fails) ---
     def get_timestep_embedding(timesteps, embedding_dim):
         """ Build sinusoidal embeddings. """
         assert len(timesteps.shape) == 1
         half_dim = embedding_dim // 2
         emb = math.log(10000) / (half_dim - 1)
         emb = torch.exp(torch.arange(half_dim, dtype=torch.float32) * -emb)
         emb = emb.to(device=timesteps.device)
         emb = timesteps.float()[:, None] * emb[None, :]
         emb = torch.cat([torch.sin(emb), torch.cos(emb)], dim=1)
         if embedding_dim % 2 == 1:
             emb = torch.nn.functional.pad(emb, (0, 1, 0, 0))
         return emb

     def get_diffusion_schedule(schedule_name, timesteps):
         """ Returns betas, alphas, alphas_cumprod etc. """
         if schedule_name == "linear":
             scale = 1000 / timesteps
             beta_start = scale * 0.0001
             beta_end = scale * 0.02
             betas = torch.linspace(beta_start, beta_end, timesteps, dtype=torch.float64)
         elif schedule_name == "cosine":
             s = 0.008
             steps = timesteps + 1
             x = torch.linspace(0, timesteps, steps, dtype=torch.float64)
             alphas_cumprod = torch.cos(((x / timesteps) + s) / (1 + s) * math.pi * 0.5) ** 2
             alphas_cumprod = alphas_cumprod / alphas_cumprod[0]
             betas = 1 - (alphas_cumprod[1:] / alphas_cumprod[:-1])
             betas = torch.clip(betas, 0, 0.999)
         else:
             raise NotImplementedError(f"Unknown schedule: {schedule_name}")
         alphas = 1.0 - betas
         alphas_cumprod = torch.cumprod(alphas, axis=0)
         alphas_cumprod_prev = F.pad(alphas_cumprod[:-1], (1, 0), value=1.0)
         sqrt_recip_alphas = torch.sqrt(1.0 / alphas)
         sqrt_alphas_cumprod = torch.sqrt(alphas_cumprod)
         sqrt_one_minus_alphas_cumprod = torch.sqrt(1.0 - alphas_cumprod)
         posterior_variance = betas * (1.0 - alphas_cumprod_prev) / (1.0 - alphas_cumprod)
         return {"betas": betas, "alphas_cumprod": alphas_cumprod, "alphas_cumprod_prev": alphas_cumprod_prev,
                 "sqrt_recip_alphas": sqrt_recip_alphas, "sqrt_alphas_cumprod": sqrt_alphas_cumprod,
                 "sqrt_one_minus_alphas_cumprod": sqrt_one_minus_alphas_cumprod, "posterior_variance": posterior_variance}

     @torch.no_grad()
     def ddim_sample_loop(model, shape, condition, device, schedule_dict, num_train_timesteps, sampling_timesteps, eta=0.0):
         """ DDIM sampling loop """
         batch_size = shape[0]
         img = torch.randn(shape, device=device)
         times = torch.linspace(-1, num_train_timesteps - 1, steps=sampling_timesteps + 1)
         times = list(reversed(times.int().tolist()))
         time_pairs = list(zip(times[:-1], times[1:]))
         alphas_cumprod = schedule_dict['alphas_cumprod'].to(device)
         sqrt_one_minus_alphas_cumprod = schedule_dict['sqrt_one_minus_alphas_cumprod'].to(device)
         sqrt_alphas_cumprod = schedule_dict['sqrt_alphas_cumprod'].to(device)
         print(f"Running DDIM Sampling with {sampling_timesteps} steps...")
         for i, (time, time_next) in enumerate(time_pairs):
             time_cond = torch.full((batch_size,), time, device=device, dtype=torch.long)
             pred_noise = model(img, time_cond, condition)
             alpha = alphas_cumprod[time]
             alpha_next = alphas_cumprod[time_next] if time_next >= 0 else torch.tensor(1.0, device=device)
             sigma = eta * torch.sqrt((1 - alpha_next) / (1 - alpha) * (1 - alpha / alpha_next))
             c = torch.sqrt(1 - alpha_next - sigma ** 2)
             pred_x0 = (img - sqrt_one_minus_alphas_cumprod[time] * pred_noise) / sqrt_alphas_cumprod[time]
             pred_x0 = torch.clamp(pred_x0, -1.0, 1.0)
             pred_dir = c * pred_noise
             noise = torch.randn_like(img) * sigma if sigma > 0 else 0.
             img = sqrt_alphas_cumprod[time_next] * pred_x0 + pred_dir + noise
         print("DDIM Sampling finished.")
         return img


# --- Helper: Positional Embedding (copied from util/pos_embed.py) ---
# --------------------------------------------------------
# 2D sine-cosine position embedding
# References:
# MoCo v3: https://github.com/facebookresearch/moco-v3
# MAE: https://github.com/facebookresearch/mae
# --------------------------------------------------------
def get_1d_sincos_pos_embed_from_grid(embed_dim, pos):
    """
    Generates 1D sine-cosine positional embeddings from grid positions.
    embed_dim: output dimension for each position
    pos: a list of positions to be encoded: size (M,)
    out: (M, D)
    """
    assert embed_dim % 2 == 0
    omega = np.arange(embed_dim // 2, dtype=np.float32)
    omega /= embed_dim / 2.
    omega = 1. / 10000**omega  # (D/2,)

    pos = pos.reshape(-1)  # (M,)
    out = np.einsum('m,d->md', pos, omega)  # (M, D/2), outer product

    emb_sin = np.sin(out) # (M, D/2)
    emb_cos = np.cos(out) # (M, D/2)

    emb = np.concatenate([emb_sin, emb_cos], axis=1)  # (M, D)
    return emb

def get_2d_sincos_pos_embed_from_grid(embed_dim, grid):
    assert embed_dim % 2 == 0

    # use half of dimensions to encode grid_h
    emb_h = get_1d_sincos_pos_embed_from_grid(embed_dim // 2, grid[0])  # (H*W, D/2)
    # use half of dimensions to encode grid_w
    emb_w = get_1d_sincos_pos_embed_from_grid(embed_dim // 2, grid[1])  # (H*W, D/2)

    emb = np.concatenate([emb_h, emb_w], axis=1) # (H*W, D)
    return emb

def get_2d_sincos_pos_embed(embed_dim, grid_size, cls_token=False):
    """
    grid_size: int of the grid height and width
    return:
    pos_embed: [grid_size*grid_size, embed_dim] or [1+grid_size*grid_size, embed_dim] (w/ cls_token)
    """
    # grid_size can be int or tuple (H_patch, W_patch)
    if isinstance(grid_size, int):
        grid_h_size = grid_w_size = grid_size
    else:
        grid_h_size, grid_w_size = grid_size

    grid_h = np.arange(grid_h_size, dtype=np.float32)
    grid_w = np.arange(grid_w_size, dtype=np.float32)
    grid = np.meshgrid(grid_w, grid_h)  # here w goes first
    grid = np.stack(grid, axis=0)

    grid = grid.reshape([2, 1, grid_h_size, grid_w_size])
    pos_embed = get_2d_sincos_pos_embed_from_grid(embed_dim, grid)
    if cls_token:
        pos_embed = np.concatenate([np.zeros([1, embed_dim]), pos_embed], axis=0)
    return pos_embed

# --- Pretrained Swin MAE Definition (copied from train_swin_mae.py) ---
# --------------------------------------------------------
# Based on MAE implementation by Meta Platforms, Inc. and Swin Transformer
# --------------------------------------------------------
class MaskedAutoencoderSwin(nn.Module):
    """ Masked Autoencoder with Swin Transformer backbone
    """
    def __init__(self, img_size=256, patch_size=4, in_chans=1, # Swin uses patch_size=4 typically
                 embed_dim=96, depths=[2, 2, 6, 2], num_heads=[3, 6, 12, 24], # Swin-T defaults
                 window_size=7, mlp_ratio=4., qkv_bias=True, qk_scale=None,
                 drop_rate=0., attn_drop_rate=0., drop_path_rate=0.1,
                 norm_layer=nn.LayerNorm, ape=False, patch_norm=True, # Swin specific params
                 # Decoder args are not directly used when only extracting encoder features
                 # decoder_embed_dim=512, decoder_depth=8, decoder_num_heads=16,
                 # decoder_mlp_ratio=4., decoder_norm_layer=nn.LayerNorm,
                 norm_pix_loss=False, # Not used in encoder forward pass
                 return_intermediate_features=False): # Added flag
        super().__init__()

        self.patch_size = patch_size # Store patch size
        self.in_chans = in_chans # Store in_chans
        self.return_intermediate_features = return_intermediate_features

        # --------------------------------------------------------------------------
        # Swin MAE encoder specifics
        self.encoder = timm.create_model(
            'swin_tiny_patch4_window7_224', # Using standard tiny model name
            pretrained=False,
            in_chans=in_chans,
            img_size=img_size, # Timm handles img_size mismatch from 224
            patch_size=patch_size, # Should be 4 for standard Swin
            embed_dim=embed_dim,
            depths=depths,
            num_heads=num_heads,
            window_size=window_size,
            mlp_ratio=mlp_ratio,
            qkv_bias=qkv_bias,
            qk_scale=qk_scale,
            drop_rate=drop_rate,
            attn_drop_rate=attn_drop_rate,
            drop_path_rate=drop_path_rate,
            norm_layer=norm_layer,
            ape=ape, # Absolute Position Embedding
            patch_norm=patch_norm,
            num_classes=0, # No head for encoder features
            global_pool='', # No pooling
            features_only=return_intermediate_features # Use timm's feature extraction capability
        )
        # Store the feature info if features_only=True
        self.feature_info = self.encoder.feature_info if return_intermediate_features else None
        self.actual_encoder_output_dim = self.encoder.num_features # Final output dim


class ResidualBlock(nn.Module):
    """Basic Residual Block with GroupNorm and SiLU activation.""" # Changed Swish to SiLU
    def __init__(self, in_channels, out_channels, time_emb_dim=None, dropout=0.1, context_dim=None, num_heads=8, head_dim=None): # Added context_dim, num_heads, head_dim
        super().__init__()
        self.in_channels = in_channels
        self.out_channels = out_channels
        self.use_context = context_dim is not None
        attn_dim_head = head_dim if head_dim is not None else out_channels // num_heads # Default head dim

        # Group 1
        self.norm1 = nn.GroupNorm(32, in_channels)
        self.act1 = nn.SiLU() # Use standard SiLU
        self.conv1 = nn.Conv2d(in_channels, out_channels, kernel_size=3, padding=1)

        # Optional time embedding projection
        self.time_proj = None
        if time_emb_dim is not None:
            self.time_proj = nn.Linear(time_emb_dim, out_channels)

        # Optional Cross Attention & Projection
        self.cross_attn = None
        self.context_proj = None
        if self.use_context:
            # Project context features to match the attention dimension if needed
            # For simplicity, let's assume CrossAttention handles projection or context_dim matches query_dim for now
            # More robust: Add projection if context_dim != out_channels
            if context_dim != out_channels:
                 print(f"  ResBlock: Adding projection from context_dim {context_dim} to {out_channels}")
                 self.context_proj = nn.Linear(context_dim, out_channels) # Project context to query dim
                 effective_context_dim = out_channels
            else:
                 self.context_proj = nn.Identity()
                 effective_context_dim = context_dim

            self.norm_context = nn.LayerNorm(out_channels) # Norm U-Net features before attention query
            self.cross_attn = CrossAttention(query_dim=out_channels, context_dim=effective_context_dim, heads=num_heads, dim_head=attn_dim_head)

        # Group 2
        self.norm2 = nn.GroupNorm(32, out_channels)
        self.act2 = nn.SiLU() # Use standard SiLU
        self.dropout = nn.Dropout(dropout)
        self.conv2 = nn.Conv2d(out_channels, out_channels, kernel_size=3, padding=1)

        # Shortcut connection
        if in_channels != out_channels:
            self.shortcut = nn.Conv2d(in_channels, out_channels, kernel_size=1)
        else:
            self.shortcut = nn.Identity()

    def forward(self, x, time_emb=None, context=None): # Changed condition_features to context
        """ Residual block forward pass. """
        h = x
        h = self.act1(self.norm1(h))
        h = self.conv1(h)

        # Add time embedding
        if time_emb is not None and self.time_proj is not None:
            # Project time embedding and add spatially
            time_encoding = self.time_proj(F.silu(time_emb)) # Use SiLU/Swish for time emb activation
            h = h + time_encoding.unsqueeze(-1).unsqueeze(-1) # Add broadcasting across H, W

        # Add Cross Attention (if enabled)
        if self.cross_attn is not None and context is not None:
            B, C, H, W = h.shape
            # context shape should be (B, N_context, D_context)
            # Project context if needed
            context_projected = self.context_proj(context) # Apply projection (or Identity)

            # Reshape h for attention query: B, C, H, W -> B, HW, C
            h_attn = h.view(B, C, H * W).permute(0, 2, 1)
            # Apply layer norm to query
            h_attn = self.norm_context(h_attn)
            # Apply cross attention
            attn_out = self.cross_attn(h_attn, context=context_projected) # Use projected context
            # Reshape output back: B, HW, C -> B, C, HW -> B, C, H, W
            attn_out = attn_out.permute(0, 2, 1).view(B, C, H, W)
            h = h + attn_out # Add residual connection for attention

        h = self.act2(self.norm2(h))
        h = self.dropout(h)
        h = self.conv2(h)

        return h + self.shortcut(x)

class AttentionBlock(nn.Module):
    """Self-Attention Block using MultiheadAttention."""
    def __init__(self, channels, num_heads=4, norm_groups=32):
        super().__init__()
        self.num_heads = num_heads
        assert channels % num_heads == 0, "Channels must be divisible by num_heads"

        self.norm = nn.GroupNorm(norm_groups, channels)
        self.qkv = nn.Conv2d(channels, channels * 3, kernel_size=1, bias=False)
        # Use MultiheadAttention - requires input shape (SeqLen, Batch, EmbedDim)
        # We'll reshape (B, C, H, W) -> (HW, B, C)
        self.attention = nn.MultiheadAttention(channels, num_heads, batch_first=False) # batch_first=False expects (SeqLen, Batch, EmbedDim)
        self.proj_out = nn.Conv2d(channels, channels, kernel_size=1)
        # print(f"AttentionBlock initialized for {channels} channels, {num_heads} heads.") # Less verbose

    def forward(self, x):
        B, C, H, W = x.shape
        h_ = self.norm(x)
        qkv = self.qkv(h_)

        # Reshape B,C,H,W -> B,C,HW -> HW,B,C for MultiheadAttention
        qkv = qkv.reshape(B, C * 3, H * W).permute(2, 0, 1) # HW, B, C*3
        q, k, v = qkv.chunk(3, dim=-1) # Each is HW, B, C

        # Apply attention
        attn_output, _ = self.attention(q, k, v) # attn_output: HW, B, C

        # Reshape back HW,B,C -> B,C,HW -> B,C,H,W
        attn_output = attn_output.permute(1, 2, 0).reshape(B, C, H, W)

        h_ = self.proj_out(attn_output)
        return x + h_ # Add residual connection

class Downsample(nn.Module):
    """Downsampling layer using strided convolution or AvgPool."""
    def __init__(self, channels, use_conv=True):
        super().__init__()
        if use_conv:
            self.op = nn.Conv2d(channels, channels, kernel_size=3, stride=2, padding=1)
        else:
            self.op = nn.AvgPool2d(kernel_size=2, stride=2)

    def forward(self, x):
        return self.op(x)

class Upsample(nn.Module):
    """Upsampling layer using interpolation + convolution."""
    def __init__(self, channels, use_conv=True):
        super().__init__()
        self.use_conv = use_conv
        if use_conv:
            self.conv = nn.Conv2d(channels, channels, kernel_size=3, padding=1)

    def forward(self, x):
        x = F.interpolate(x, scale_factor=2, mode="nearest") # Use nearest for potentially sharper results
        if self.use_conv:
            x = self.conv(x)
        return x


# --- Main Model ---

class DiffusionSRModel(nn.Module):
    """
    Conditional U-Net model for super-resolution diffusion.
    Takes noisy image, time step, and low-resolution condition as input.
    Predicts the noise added to the image.
    """
    def __init__(self, config):
        super().__init__()
        model_config = config['model']
        diffusion_config = config['diffusion']
        self.config = config # Store full config
        model_config = config['model']
        diffusion_config = config['diffusion']
        self.in_channels = model_config['in_channels']
        self.out_channels = model_config['out_channels']
        self.base_channels = model_config['base_channels']
        self.channel_mults = model_config['channel_mults']
        self.attention_resolutions = model_config['attention_resolutions']
        self.num_res_blocks = model_config['num_res_blocks']
        self.dropout = model_config['dropout']
        self.resolution = config['data']['patch_size_hr']
        self.num_heads = model_config.get('num_heads', 8) # Number of heads for attention blocks
        self.head_dim = model_config.get('head_dim', None) # Dimension per head (optional)

        # --- Pretrained Encoder ---
        self.use_pretrained_encoder = model_config.get('use_pretrained_encoder', False)
        self.condition_method = model_config.get('condition_method', 'CrossAttention') # Default to CrossAttention
        self.encoder_injection_levels = model_config.get('encoder_injection_levels', [0, 1, 2, 3]) # Which encoder stages to inject
        self.pretrained_encoder = None
        self.encoder_feature_dims = {} # Store feature dims for each injected level
        if self.use_pretrained_encoder:
            # Load the Swin-MAE model configured to return intermediate features
            self.pretrained_encoder = self._load_pretrained_mae_model(
                model_config['encoder_type'],
                model_config['encoder_checkpoint'],
                return_intermediate_features=True # Request intermediate features
            )
            if self.pretrained_encoder.feature_info:
                 # Store dims for the levels we intend to inject
                 for i, level_idx in enumerate(self.encoder_injection_levels):
                     if level_idx < len(self.pretrained_encoder.feature_info):
                         self.encoder_feature_dims[level_idx] = self.pretrained_encoder.feature_info[level_idx]['num_chs']
                         print(f"Encoder level {level_idx} feature dim: {self.encoder_feature_dims[level_idx]}")
                     else:
                         print(f"Warning: Requested encoder injection level {level_idx} out of bounds.")
            else:
                 print("Warning: Pretrained encoder does not provide feature_info. Using final output dim only.")
                 # Fallback: Use only the final output dimension if feature_info is not available
                 self.encoder_feature_dims = {len(self.channel_mults) - 1: self.pretrained_encoder.actual_encoder_output_dim}
                 self.encoder_injection_levels = [len(self.channel_mults) - 1] # Only inject final layer

            if model_config.get('freeze_encoder', True):
                print("Freezing pretrained encoder weights.")
                for param in self.pretrained_encoder.parameters():
                    param.requires_grad = False
            else:
                print("Pretrained encoder weights will be fine-tuned.")
            # TODO: Add projection layers if U-Net dims don't match encoder dims at injection points


        # --- Time Embedding ---
        self.time_emb_dim = self.base_channels * 4
        self.time_embedding = nn.Sequential(
            nn.Linear(self.base_channels, self.time_emb_dim),
            nn.SiLU(), # Use standard SiLU
            nn.Linear(self.time_emb_dim, self.time_emb_dim),
        )

        # --- Input Convolution ---
        self.input_conv = nn.Conv2d(self.in_channels, self.base_channels, kernel_size=3, padding=1)

        # --- Downsampling Path ---
        self.down_blocks = nn.ModuleList()
        self.down_channels = [self.base_channels] # Keep track of channels for skip connections
        ch = self.base_channels
        current_res = self.resolution
        num_levels = len(self.channel_mults)
        for i_level in range(num_levels):
            out_ch = self.base_channels * self.channel_mults[i_level]
            for i_block in range(self.num_res_blocks):
                # Determine context dim for this level if injecting
                context_dim_for_block = self.encoder_feature_dims.get(i_level) if (self.condition_method == 'CrossAttention' and i_level in self.encoder_injection_levels) else None
                block = ResidualBlock(ch, out_ch, self.time_emb_dim, self.dropout, context_dim=context_dim_for_block, num_heads=self.num_heads, head_dim=self.head_dim)
                ch = out_ch
                if current_res in self.attention_resolutions:
                    self.down_blocks.append(nn.Sequential(block, AttentionBlock(ch, num_heads=self.num_heads))) # Pass num_heads to AttentionBlock
                else:
                    self.down_blocks.append(block)
                self.down_channels.append(ch)
            if i_level != num_levels - 1:
                self.down_blocks.append(Downsample(ch))
                self.down_channels.append(ch)
                current_res //= 2

        # --- Middle Block ---
        middle_level_idx = num_levels - 1
        context_dim_for_middle = self.encoder_feature_dims.get(middle_level_idx) if (self.condition_method == 'CrossAttention' and middle_level_idx in self.encoder_injection_levels) else None
        self.middle_block = nn.Sequential(
            ResidualBlock(ch, ch, self.time_emb_dim, self.dropout, context_dim=context_dim_for_middle, num_heads=self.num_heads, head_dim=self.head_dim),
            AttentionBlock(ch, num_heads=self.num_heads),
            ResidualBlock(ch, ch, self.time_emb_dim, self.dropout, context_dim=context_dim_for_middle, num_heads=self.num_heads, head_dim=self.head_dim)
        )

        # --- Upsampling Path ---
        self.up_blocks = nn.ModuleList()
        for i_level in reversed(range(num_levels)):
            out_ch = self.base_channels * self.channel_mults[i_level]
            for i_block in range(self.num_res_blocks + 1):
                skip_ch = self.down_channels.pop()
                in_ch_up = ch + skip_ch

                # Determine context dim for this level if injecting
                context_dim_for_block = self.encoder_feature_dims.get(i_level) if (self.condition_method == 'CrossAttention' and i_level in self.encoder_injection_levels) else None
                block = ResidualBlock(in_ch_up, out_ch, self.time_emb_dim, self.dropout, context_dim=context_dim_for_block, num_heads=self.num_heads, head_dim=self.head_dim)
                ch = out_ch
                if current_res in self.attention_resolutions:
                    self.up_blocks.append(nn.Sequential(block, AttentionBlock(ch, num_heads=self.num_heads))) # Pass num_heads to AttentionBlock
                else:
                    self.up_blocks.append(block)

            if i_level != 0:
                self.up_blocks.append(Upsample(ch))
                current_res *= 2

        # --- Output Convolution ---
        self.output_norm = nn.GroupNorm(32, ch) # Use final channel size 'ch'
        self.output_act = nn.SiLU() # Use standard SiLU
        self.output_conv = nn.Conv2d(ch, self.out_channels, kernel_size=3, padding=1)


        print(f"DiffusionSRModel initialized with {num_levels} levels.")
        # Print model summary or parameter count if desired


    def _load_pretrained_mae_model(self, encoder_type, checkpoint_path, return_intermediate_features=False):
        """ Loads the Swin-MAE model, potentially configured for feature extraction. """
        print(f"Loading pretrained {encoder_type} model from {checkpoint_path}...")
        print(f"Requesting intermediate features: {return_intermediate_features}")

        if encoder_type != "Swin-MAE":
            raise ValueError(f"Unsupported encoder_type: {encoder_type}. Only Swin-MAE is currently supported.")

        # Ensure PROJECT_ROOT is defined or accessible, needed for potential dynamic imports if used later
        # Assuming PROJECT_ROOT is the root directory 'e:/vscode/非配位超分辨'
        PROJECT_ROOT = os.path.abspath(os.path.join(os.path.dirname(__file__), '..', '..'))
        if PROJECT_ROOT not in sys.path:
            sys.path.insert(0, PROJECT_ROOT)
            print(f"Added {PROJECT_ROOT} to sys.path")

        # --- Instantiate Swin-MAE Encoder ---
        # Use default Swin-T parameters, assuming they match the pretraining setup.
        # Ideally, these should be loaded from the checkpoint or config if available.
        # For now, using common Swin-T defaults.
        try:
            # Instantiate the encoder part directly using timm, enabling feature extraction
            encoder_model = timm.create_model(
                'swin_tiny_patch4_window7_224', # Base model name
                pretrained=False, # We will load weights manually
                in_chans=self.config['model']['in_channels'], # Match U-Net input
                # img_size=self.config['data']['patch_size_lr'], # Input size is LR patch size
                patch_size=4, # Standard Swin patch size
                num_classes=0, # No classification head
                features_only=return_intermediate_features, # KEY: Enable feature extraction
                out_indices=tuple(self.encoder_injection_levels) if return_intermediate_features else (3,) # Specify which stages for features_only
            )
            # Store feature info if available
            self.feature_info = getattr(encoder_model, 'feature_info', None)
            self.actual_encoder_output_dim = encoder_model.num_features # Final layer feature dim

            print(f"Instantiated Swin Transformer encoder (features_only={return_intermediate_features}).")
            if self.feature_info:
                print(f"Feature info: {self.feature_info}")
            else:
                 print("Warning: Timm model did not provide feature_info.")


        except Exception as e_init:
             print(f"ERROR: Failed to instantiate Swin Transformer encoder: {e_init}")
             raise e_init

        # --- Load the state dict ---
        if not os.path.exists(checkpoint_path):
             print(f"ERROR: Checkpoint file not found at {checkpoint_path}")
             raise FileNotFoundError(f"Checkpoint file not found: {checkpoint_path}")

        try:
            checkpoint = torch.load(checkpoint_path, map_location='cpu')
            # Adjust state dict loading based on MAE checkpoint format.
            # Common MAE checkpoints store the model under 'model' key.
            if 'model' in checkpoint:
                model_state_dict = checkpoint['model']
            elif 'state_dict' in checkpoint: # Handle other common formats
                model_state_dict = checkpoint['state_dict']
            elif 'model_state_dict' in checkpoint:
                model_state_dict = checkpoint['model_state_dict']
            else:
                model_state_dict = checkpoint # Assume the checkpoint itself is the state_dict

            # --- Filter state_dict for the encoder part ---
            # MAE checkpoints contain encoder, decoder, etc. We only need encoder weights.
            encoder_state_dict = {}
            for k, v in model_state_dict.items():
                if k.startswith('encoder.'):
                    # Remove 'encoder.' prefix to match the standalone timm model keys
                    encoder_state_dict[k.replace('encoder.', '', 1)] = v
                # Handle potential variations in key naming if necessary

            if not encoder_state_dict:
                 print("Warning: No keys starting with 'encoder.' found in checkpoint. Trying to load full state_dict.")
                 # Fallback: try loading the whole dict if no 'encoder.' prefix found
                 # This might work if the checkpoint *only* contains the encoder
                 encoder_state_dict = model_state_dict


            # Load the filtered state dict into the timm Swin encoder instance
            missing_keys, unexpected_keys = encoder_model.load_state_dict(encoder_state_dict, strict=False)

            # Filter out expected missing/unexpected keys if features_only=True
            # (e.g., norm layer after final stage might be missing if not requested)
            # For simplicity, just print warnings for now.
            if missing_keys:
                print(f"Warning: Missing keys when loading encoder state_dict: {missing_keys}")
            if unexpected_keys:
                print(f"Warning: Unexpected keys when loading encoder state_dict: {unexpected_keys}")

            print("Successfully loaded state_dict into Swin encoder instance.")

        except Exception as e:
            print(f"ERROR: Failed to load state dict from {checkpoint_path}. Check checkpoint format and keys.")
            print(f"Error details: {e}")
            raise e

        return encoder_model # Return the standalone timm encoder model


    def forward(self, x_t, time_step, condition=None):
        """
        Forward pass of the U-Net.

        Args:
            x_t (torch.Tensor): Noisy input image tensor (B, C, H, W).
            time_step (torch.Tensor): Diffusion time steps (B,).
            condition (torch.Tensor, optional): Low-resolution condition tensor (B, C, H_lr, W_lr). Defaults to None.

        Returns:
            torch.Tensor: Predicted noise tensor (B, C, H, W).
        """
        model_config = self.config['model']
        num_levels = len(self.channel_mults)

        # 1. Compute time embeddings
        t_emb = get_timestep_embedding(time_step, self.base_channels) # Use helper function
        time_emb = self.time_embedding(t_emb)

        # 2. (Optional) Extract multi-level features from condition using pretrained encoder
        context_features_list = None
        if condition is not None and self.use_pretrained_encoder and self.pretrained_encoder is not None:
            freeze_encoder = model_config.get('freeze_encoder', True)
            with torch.set_grad_enabled(not freeze_encoder):
                # Encoder expects (B, C, H, W)
                # Ensure condition has the expected input size for the encoder
                # The timm encoder with features_only=True returns a list of feature maps
                context_features_list = self.pretrained_encoder(condition)
                # context_features_list should be a list of tensors:
                # e.g., [(B, C0, H0, W0), (B, C1, H1, W1), ...] matching out_indices

                # Debug: Print shapes
                # if context_features_list is not None:
                #     print(f"Encoder output levels: {len(context_features_list)}")
                #     for i, feat in enumerate(context_features_list):
                #         print(f"  Level {i} shape: {feat.shape}")

        # 3. Pass through U-Net
        # 3. Pass through U-Net
        h = self.input_conv(x_t)
        hs = [h] # Store outputs for skip connections

        # Downsampling path
        down_block_idx = 0
        for i_level in range(num_levels):
            # Get context features for this level if available
            current_context = None
            if context_features_list is not None and i_level in self.encoder_injection_levels:
                 # Find the correct index in the context_features_list based on encoder_injection_levels
                 context_idx = self.encoder_injection_levels.index(i_level)
                 if context_idx < len(context_features_list):
                     current_context = context_features_list[context_idx]
                     # Reshape context if needed for CrossAttention (B, C, H, W) -> (B, HW, C)
                     if self.condition_method == 'CrossAttention' and current_context is not None:
                          B_ctx, C_ctx, H_ctx, W_ctx = current_context.shape
                          current_context = current_context.view(B_ctx, C_ctx, H_ctx * W_ctx).permute(0, 2, 1) # -> (B, HW, C)

            for i_block in range(self.num_res_blocks):
                res_block_module = self.down_blocks[down_block_idx]
                h = res_block_module(h, time_emb, context=current_context) # Pass level-specific context
                hs.append(h)
                down_block_idx += 1

            if i_level != num_levels - 1:
                downsample_block = self.down_blocks[down_block_idx]
                h = downsample_block(h)
                hs.append(h)
                down_block_idx += 1

        # Middle block
        middle_level_idx = num_levels - 1
        middle_context = None
        if context_features_list is not None and middle_level_idx in self.encoder_injection_levels:
             context_idx = self.encoder_injection_levels.index(middle_level_idx)
             if context_idx < len(context_features_list):
                 middle_context = context_features_list[context_idx]
                 if self.condition_method == 'CrossAttention' and middle_context is not None:
                      B_ctx, C_ctx, H_ctx, W_ctx = middle_context.shape
                      middle_context = middle_context.view(B_ctx, C_ctx, H_ctx * W_ctx).permute(0, 2, 1)

        h = self.middle_block[0](h, time_emb, context=middle_context) # ResBlock 1 + Context
        h = self.middle_block[1](h)                                    # Attention
        h = self.middle_block[2](h, time_emb, context=middle_context) # ResBlock 2 + Context

        # Upsampling path
        up_block_idx = 0
        for i_level in reversed(range(num_levels)):
            # Get context features for this level if available
            current_context = None
            if context_features_list is not None and i_level in self.encoder_injection_levels:
                 context_idx = self.encoder_injection_levels.index(i_level)
                 if context_idx < len(context_features_list):
                     current_context = context_features_list[context_idx]
                     if self.condition_method == 'CrossAttention' and current_context is not None:
                          B_ctx, C_ctx, H_ctx, W_ctx = current_context.shape
                          current_context = current_context.view(B_ctx, C_ctx, H_ctx * W_ctx).permute(0, 2, 1)

            for i_block in range(self.num_res_blocks + 1):
                skip_h = hs.pop()
                h = torch.cat([h, skip_h], dim=1)

                res_block_module = self.up_blocks[up_block_idx]
                h = res_block_module(h, time_emb, context=current_context) # Pass level-specific context
                up_block_idx += 1

            if i_level != 0:
                upsample_block = self.up_blocks[up_block_idx]
                h = upsample_block(h)
                up_block_idx += 1

        # Output
        h = self.output_act(self.output_norm(h))
        predicted_noise = self.output_conv(h)

        return predicted_noise

    def sample(self, condition, batch_size, device, sampling_timesteps=50, sampling_method='DDIM', eta=0.0):
        """
        Generate SR images using the diffusion model sampling process (DDIM implemented).

        Args:
            condition (torch.Tensor): Low-resolution condition tensor (B, C, H_lr, W_lr).
            batch_size (int): Number of samples to generate.
            device: Target device.
            sampling_timesteps (int): Number of steps for sampling.
            sampling_method (str): Currently only 'DDIM' is implemented.
            eta (float): DDIM eta parameter (0 for deterministic).

        Returns:
            torch.Tensor: Generated SR image tensor (B, C, H, W).
        """
        if sampling_method != 'DDIM':
            raise NotImplementedError(f"Sampling method '{sampling_method}' not implemented. Use 'DDIM'.")

        print(f"Starting DDIM sampling with {sampling_timesteps} steps (eta={eta})...")
        diffusion_config = self.config['diffusion']
        model_config = self.config['model']
        num_train_timesteps = diffusion_config['timesteps']

        # Get the diffusion schedule dictionary
        # Ensure schedule is on the correct device
        schedule_dict = get_diffusion_schedule( # Use helper function
            diffusion_config['schedule_name'],
            num_train_timesteps
        )
        for key in schedule_dict:
             if isinstance(schedule_dict[key], torch.Tensor):
                 schedule_dict[key] = schedule_dict[key].to(device)


        # Define the shape of the output image
        img_size = self.config['data']['patch_size_hr']
        shape = (batch_size, self.out_channels, img_size, img_size)

        # Run the DDIM sampling loop
        self.eval() # Ensure model is in eval mode for sampling
        generated_img = ddim_sample_loop( # Use helper function
            model=self, # Pass the model instance itself
            shape=shape,
            condition=condition,
            device=device,
            schedule_dict=schedule_dict,
            num_train_timesteps=num_train_timesteps,
            sampling_timesteps=sampling_timesteps,
            eta=eta
        )

        return generated_img # Return the final denoised image x_0

# Note: The helper functions get_timestep_embedding, get_diffusion_schedule,
# and ddim_sample_loop are assumed to be imported correctly from
# Self-supervised training network/utils/diffusion_utils.py
# The CrossAttention class is assumed to be imported from modules/attention.py
