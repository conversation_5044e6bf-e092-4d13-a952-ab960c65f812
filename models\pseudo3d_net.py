import torch
import torch.nn as nn
import torch.nn.functional as F

class EnhancedPseudo3DBlock(nn.Module):
    """增强版伪3D卷积块"""
    def __init__(self, dim, temporal_size=3):
        super().__init__()
        self.spatial_conv = nn.Conv2d(dim, dim, kernel_size=3, padding=1)
        self.temporal_conv = nn.Conv3d(dim, dim, kernel_size=(temporal_size, 1, 1), padding=(temporal_size//2, 0, 0))
        self.norm1 = nn.InstanceNorm3d(dim)
        self.norm2 = nn.InstanceNorm2d(dim)
        self.fusion = nn.Conv2d(dim*2, dim, kernel_size=1)
        # Note: res_conv logic was simplified as input dim == output dim for the block
        # self.res_conv = nn.Conv2d(dim, dim, kernel_size=1) if dim != dim else nn.Identity() # Original line kept for reference

    def forward(self, x_sequence):
        # x_sequence: [B, T, C, H, W]
        B, T, C, H, W = x_sequence.shape
        mid = T // 2
        identity = x_sequence[:, mid] # Residual connection starts from the middle frame input

        # 中间帧空间处理 (Applying Norm + ReLU)
        spatial_feat = F.relu(self.norm2(self.spatial_conv(x_sequence[:, mid])))

        # 时间维度处理 (Applying Norm + ReLU)
        temp_input = x_sequence.permute(0, 2, 1, 3, 4)  # [B, C, T, H, W]
        temp_feat = F.relu(self.norm1(self.temporal_conv(temp_input))[:, :, mid]) # Apply norm3d before selecting mid frame

        # 特征融合
        combined = torch.cat([spatial_feat, temp_feat], dim=1)
        fused_feat = self.fusion(combined)

        # Add residual connection
        return F.relu(fused_feat + identity) # Apply ReLU after adding residual

class Pseudo3DNet(nn.Module):
    """伪3D网络"""
    def __init__(self, in_channels=16, out_channels=64, temporal_size=3, depth=4): # Updated default depth to 4
        super().__init__()
        self.temporal_size = temporal_size
        self.first_conv = nn.Conv2d(in_channels, out_channels, kernel_size=3, padding=1)
        self.blocks = nn.ModuleList([
            EnhancedPseudo3DBlock(out_channels, temporal_size) for _ in range(depth)
        ])
        self.last_conv = nn.Conv2d(out_channels, out_channels, kernel_size=3, padding=1)

    def forward(self, x_sequence):
        # x_sequence: [B, T*C, H, W] → 重组为5D张量
        B, TC, H, W = x_sequence.shape
        C = TC // self.temporal_size
        x_5d = x_sequence.view(B, self.temporal_size, C, H, W)
        
        # 初始特征提取
        x = self.first_conv(x_sequence)
        
        # 处理每个3D卷积块
        for block in self.blocks:
            x = x + block(x_5d)
        
        return self.last_conv(x)
