import torch
import os
import numpy as np
from PIL import Image
from models.enhanced_sr_network import EnhancedSRNetwork
from tqdm import tqdm
import matplotlib.pyplot as plt
import argparse
from skimage.metrics import peak_signal_noise_ratio as psnr
from skimage.metrics import structural_similarity as ssim

def inference_enhanced(model_path, lr_dir, output_dir, temporal_window=5, batch_size=1, visualize=False, vis_dir=None):
    """增强型超分辨率网络推理函数"""
    device = torch.device('cuda' if torch.cuda.is_available() else 'cpu')
    print(f"Using device: {device}")
    
    # 创建输出目录
    os.makedirs(output_dir, exist_ok=True)
    if visualize and vis_dir:
        os.makedirs(vis_dir, exist_ok=True)
    
    # 加载模型
    model = EnhancedSRNetwork(temporal_size=temporal_window).to(device)
    
    # 加载权重
    if model_path.endswith('.pth'):
        model.load_state_dict(torch.load(model_path, map_location=device))
    else:  # 加载检查点
        checkpoint = torch.load(model_path, map_location=device)
        model.load_state_dict(checkpoint['model_state_dict'])
    
    model.eval()
    print("Model loaded successfully!")
    
    # 获取LR数据路径
    lr_paths = sorted([os.path.join(lr_dir, f) for f in os.listdir(lr_dir) if f.endswith('.tif')])
    
    # 图像大小和圆形掩码参数
    image_size = 2700
    center = image_size // 2
    valid_radius = int(image_size * 0.45)
    
    def get_circle_mask(image):
        """生成二维圆形mask"""
        Y, X = np.ogrid[:image.shape[0], :image.shape[1]]
        dist_from_center = np.sqrt((X - center)**2 + (Y - center)**2)
        return dist_from_center <= valid_radius
    
    def get_circular_crop(image):
        """裁剪出圆形区域"""
        mask = get_circle_mask(image)
        y, x = np.where(mask)
        min_y, max_y = y.min(), y.max()
        min_x, max_x = x.min(), x.max()
        # 裁剪出包含圆形区域的最小矩形
        return image[min_y:max_y+1, min_x:max_x+1], (min_y, max_y, min_x, max_x)
    
    # 推理
    with torch.no_grad():
        # 分批处理以节省内存
        for i in range(0, len(lr_paths) - temporal_window + 1, batch_size):
            batch_end = min(i + batch_size, len(lr_paths) - temporal_window + 1)
            
            for j in range(i, batch_end):
                # 加载LR序列
                lr_sequence = []
                crop_coords = None
                
                for k in range(temporal_window):
                    img = Image.open(lr_paths[j+k]).convert('L')
                    img_np = np.array(img, dtype=np.float32) / 65535.0  # 归一化到[0, 1]
                    
                    # 裁剪圆形区域
                    if k == temporal_window // 2:  # 只对中心帧获取裁剪坐标
                        cropped_img, crop_coords = get_circular_crop(img_np)
                    else:
                        cropped_img = get_circular_crop(img_np)[0]
                    
                    lr_sequence.append(torch.from_numpy(cropped_img).unsqueeze(0))  # 添加通道维度
                
                # 转换为批次
                lr_batch = torch.stack(lr_sequence).unsqueeze(0).to(device)  # [1, T, C, H, W]
                
                # 推理
                sr_img = model(lr_batch)
                
                # 处理结果
                sr_img = sr_img.squeeze().cpu().numpy()
                sr_img = np.clip(sr_img, 0, 1) * 65535  # 恢复到原始范围
                sr_img = sr_img.astype(np.uint16)
                
                # 保存结果
                output_path = os.path.join(output_dir, f"sr_{os.path.basename(lr_paths[j + temporal_window // 2])}")
                
                # 创建PIL图像并保存
                sr_pil = Image.fromarray(sr_img, mode='I;16')
                sr_pil.save(output_path)
                
                # 可视化
                if visualize and vis_dir:
                    # 加载原始图像
                    orig_img = np.array(Image.open(lr_paths[j + temporal_window // 2]).convert('L'))
                    orig_cropped = get_circular_crop(orig_img / 65535.0)[0] * 65535
                    
                    plt.figure(figsize=(12, 6))
                    
                    plt.subplot(1, 2, 1)
                    plt.imshow(orig_cropped, cmap='gray')
                    plt.title('Original LR Image')
                    plt.axis('off')
                    
                    plt.subplot(1, 2, 2)
                    plt.imshow(sr_img, cmap='gray')
                    plt.title('Enhanced SR Image')
                    plt.axis('off')
                    
                    plt.tight_layout()
                    plt.savefig(os.path.join(vis_dir, f'comparison_{j}.png'), dpi=300)
                    plt.close()
                
                print(f"Processed and saved: {output_path}")

def calculate_metrics(original_dir, sr_dir):
    """计算超分辨率结果的客观评价指标"""
    # 获取文件路径
    original_files = sorted([f for f in os.listdir(original_dir) if f.endswith('.tif')])
    sr_files = sorted([f for f in os.listdir(sr_dir) if f.startswith('sr_') and f.endswith('.tif')])
    
    # 确保有匹配的文件
    matching_files = []
    for sr_file in sr_files:
        original_file = sr_file[3:]  # 移除'sr_'前缀
        if original_file in original_files:
            matching_files.append((original_file, sr_file))
    
    # 计算指标
    psnr_values = []
    ssim_values = []
    
    for original_file, sr_file in tqdm(matching_files, desc="Calculating metrics"):
        # 加载图像
        original_img = np.array(Image.open(os.path.join(original_dir, original_file)).convert('L')) / 65535.0
        sr_img = np.array(Image.open(os.path.join(sr_dir, sr_file)).convert('L')) / 65535.0
        
        # 计算PSNR
        psnr_value = psnr(original_img, sr_img, data_range=1.0)
        psnr_values.append(psnr_value)
        
        # 计算SSIM
        ssim_value = ssim(original_img, sr_img, data_range=1.0)
        ssim_values.append(ssim_value)
    
    # 计算平均值
    avg_psnr = np.mean(psnr_values)
    avg_ssim = np.mean(ssim_values)
    
    print(f"Average PSNR: {avg_psnr:.2f} dB")
    print(f"Average SSIM: {avg_ssim:.4f}")
    
    return avg_psnr, avg_ssim

if __name__ == "__main__":
    parser = argparse.ArgumentParser(description="Advanced Super-Resolution Inference")
    parser.add_argument("--model", type=str, default="checkpoints/advanced/best_model.pth", help="Path to model weights")
    parser.add_argument("--lr_dir", type=str, default="E:/vscode/2号CT数据", help="Directory containing LR images") # Updated path
    parser.add_argument("--output_dir", type=str, default="results/advanced", help="Output directory for SR images")
    parser.add_argument("--temporal_window", type=int, default=5, help="Temporal window size")
    parser.add_argument("--batch_size", type=int, default=1, help="Batch size for inference")
    parser.add_argument("--visualize", action="store_true", help="Visualize results")
    parser.add_argument("--vis_dir", type=str, default="visualizations", help="Directory for visualizations")
    parser.add_argument("--metrics", action="store_true", help="Calculate metrics")
    
    args = parser.parse_args()
    
    # 执行推理
    inference_enhanced(
        model_path=args.model,
        lr_dir=args.lr_dir,
        output_dir=args.output_dir,
        temporal_window=args.temporal_window,
        batch_size=args.batch_size,
        visualize=args.visualize,
        vis_dir=args.vis_dir
    )
    
    # 可视化结果已在inference_enhanced函数中处理，不需要额外调用
    
    # 计算指标
    if args.metrics:
        calculate_metrics(
            original_dir=args.lr_dir,
            sr_dir=args.output_dir
        )
