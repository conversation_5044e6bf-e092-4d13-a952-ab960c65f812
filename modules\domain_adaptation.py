import torch
import torch.nn as nn
import torch.nn.functional as F

class ContrastiveLearning(nn.Module):
    """对比学习模块，支持体素大小自适应"""
    def __init__(self, encoder, projection_dim=128, temperature=0.07, voxel_size=0.03889):
        super().__init__()
        self.encoder = encoder
        # 根据体素大小动态调整投影维度和温度参数
        self.projection_dim = int(projection_dim * (0.03889 / voxel_size))
        self.temperature = temperature * (voxel_size / 0.03889)
        # 修正projector的输入维度
        self.projector = nn.Sequential(
            nn.Linear(encoder.out_channels * (64 // 4) * (64 // 4), 512),
            nn.LayerNorm(512),
            nn.ReLU(),
            nn.Linear(512, self.projection_dim)
        )

    def forward(self, x1, x2):
        # 提取特征
        f1 = self.encoder(x1)
        f2 = self.encoder(x2)

        # 展平特征
        f1 = f1.view(f1.size(0), -1)
        f2 = f2.view(f2.size(0), -1)

        # 投影到低维空间
        z1 = self.projector(f1)
        z2 = self.projector(f2)

        # 归一化
        z1 = F.normalize(z1, dim=1)
        z2 = F.normalize(z2, dim=1)

        # 计算相似度矩阵
        similarity = torch.mm(z1, z2.t()) / self.temperature

        # 对比损失
        labels = torch.arange(len(z1), device=z1.device)
        loss = F.cross_entropy(similarity, labels)

        return loss

class FeatureEncoder(nn.Module):
    """特征编码器"""
    def __init__(self, in_channels=1, out_channels=64, patch_size=64):
        super().__init__()
        self.out_channels = out_channels
        self.patch_size = patch_size
        self.conv1 = nn.Conv2d(in_channels, 32, kernel_size=3, padding=1)
        self.conv2 = nn.Conv2d(32, out_channels, kernel_size=3, padding=1)
        self.pool = nn.MaxPool2d(2, 2)

    def forward(self, x):
        x = F.relu(self.conv1(x))
        x = self.pool(x)
        x = F.relu(self.conv2(x))
        x = self.pool(x)
        return x
