import torch
import torch.nn as nn
import torch.nn.functional as F
from torchvision.models import vgg16
import numpy as np

class PerceptualLoss(nn.Module):
    def __init__(self):
        super().__init__()
        vgg = vgg16(pretrained=True).features[:16]
        vgg.requires_grad_(False)
        self.vgg = vgg.eval()
        
    def forward(self, input, target):
        # 确保输入是3通道的
        if input.size(1) == 1:
            input = input.repeat(1, 3, 1, 1)
            target = target.repeat(1, 3, 1, 1)
            
        feat_input = self.vgg(input)
        feat_target = self.vgg(target)
        return F.l1_loss(feat_input, feat_target)

class FrequencyLoss(nn.Module):
    """频域损失，通过傅里叶变换计算频域差异，支持体素大小自适应"""
    def __init__(self, voxel_size=0.03889):
        super().__init__()
        self.voxel_size = voxel_size
        # 根据体素大小动态调整频域权重
        self.magnitude_weight = 1.0 * (0.00956 / voxel_size)
        self.phase_weight = 0.1 * (0.00956 / voxel_size)
        
    def forward(self, pred, target):
        # FFT变换
        pred_freq = torch.fft.rfft2(pred)
        target_freq = torch.fft.rfft2(target)
        
        # 计算频域差异
        magnitude_diff = torch.abs(torch.abs(pred_freq) - torch.abs(target_freq))
        phase_diff = torch.abs(torch.angle(pred_freq) - torch.angle(target_freq))
        
        # 应用自适应权重
        freq_loss = self.magnitude_weight * magnitude_diff.mean() + \
                   self.phase_weight * phase_diff.mean()
        
        # 添加频域一致性约束
        freq_consistency = torch.abs(torch.fft.irfft2(pred_freq) - pred).mean()
        
        return freq_loss + 0.05 * freq_consistency

class StructureSimilarityLoss(nn.Module):
    """结构相似性损失，关注结构而非像素级差异"""
    def __init__(self, window_size=11, size_average=True):
        super().__init__()
        self.window_size = window_size
        self.size_average = size_average
        self.channel = 1
        self.window = self._create_window(window_size, self.channel)

    def _create_window(self, window_size, channel):
        _1D_window = self._gaussian(window_size, 1.5).unsqueeze(1)
        _2D_window = _1D_window.mm(_1D_window.t()).float().unsqueeze(0).unsqueeze(0)
        window = _2D_window.expand(channel, 1, window_size, window_size).contiguous()
        return window

    def _gaussian(self, window_size, sigma):
        gauss = torch.Tensor([np.exp(-(x - window_size//2)**2/float(2*sigma**2)) for x in range(window_size)])
        return gauss/gauss.sum()

    def forward(self, pred, target):
        (_, channel, _, _) = pred.size()
        
        if channel == self.channel and self.window.data.type() == pred.data.type():
            window = self.window
        else:
            window = self._create_window(self.window_size, channel)
            window = window.to(pred.device).type_as(pred)
            self.window = window
            self.channel = channel
            
        # SSIM计算
        C1 = 0.01**2
        C2 = 0.03**2
        
        mu1 = F.conv2d(pred, window, padding=self.window_size//2, groups=channel)
        mu2 = F.conv2d(target, window, padding=self.window_size//2, groups=channel)
        
        mu1_sq = mu1.pow(2)
        mu2_sq = mu2.pow(2)
        mu1_mu2 = mu1 * mu2
        
        sigma1_sq = F.conv2d(pred * pred, window, padding=self.window_size//2, groups=channel) - mu1_sq
        sigma2_sq = F.conv2d(target * target, window, padding=self.window_size//2, groups=channel) - mu2_sq
        sigma12 = F.conv2d(pred * target, window, padding=self.window_size//2, groups=channel) - mu1_mu2
        
        ssim_map = ((2 * mu1_mu2 + C1) * (2 * sigma12 + C2)) / ((mu1_sq + mu2_sq + C1) * (sigma1_sq + sigma2_sq + C2))
        
        # 转换为损失（1-SSIM）
        return 1 - ssim_map.mean()

class TextureLoss(nn.Module):
    """纹理保真度损失，特别关注岩心纹理特征"""
    def __init__(self, patch_size=16):
        super().__init__()
        self.patch_size = patch_size
        
    def gram_matrix(self, x):
        b, c, h, w = x.size()
        features = x.view(b, c, h * w)
        gram = torch.bmm(features, features.transpose(1, 2))
        return gram.div(c * h * w)
        
    def forward(self, pred, target):
        # 提取局部纹理特征
        kernel_x = torch.tensor([[-1, 0, 1], [-2, 0, 2], [-1, 0, 1]], dtype=torch.float32).view(1, 1, 3, 3).to(pred.device)
        kernel_y = torch.tensor([[-1, -2, -1], [0, 0, 0], [1, 2, 1]], dtype=torch.float32).view(1, 1, 3, 3).to(pred.device)
        
        # 计算梯度
        pred_grad_x = F.conv2d(pred, kernel_x, padding=1)
        pred_grad_y = F.conv2d(pred, kernel_y, padding=1)
        target_grad_x = F.conv2d(target, kernel_x, padding=1)
        target_grad_y = F.conv2d(target, kernel_y, padding=1)
        
        # 计算梯度幅度
        pred_grad = torch.sqrt(pred_grad_x.pow(2) + pred_grad_y.pow(2))
        target_grad = torch.sqrt(target_grad_x.pow(2) + target_grad_y.pow(2))
        
        # 计算Gram矩阵
        pred_gram = self.gram_matrix(pred_grad)
        target_gram = self.gram_matrix(target_grad)
        
        # 计算纹理损失
        return F.l1_loss(pred_gram, target_gram)

class CompositeLoss(nn.Module):
    """复合损失函数，考虑体素大小差异"""
    def __init__(self, voxel_weight=True):
        super().__init__()
        self.l1_loss = nn.L1Loss()
        self.perceptual_loss = PerceptualLoss()
        
        # 添加体素大小权重
        self.voxel_weight = voxel_weight
        if voxel_weight:
            # 旧路径 '2号CT数据' 体素大小为0.03889mm，旧路径 '3号CT数据' 体素大小为0.00956mm
            self.lr_voxel_size = 0.03889 # 基于旧路径 '2号CT数据'
            self.hr_voxel_size = 0.00956 # 基于旧路径 '3号CT数据'
            self.voxel_ratio = self.hr_voxel_size / self.lr_voxel_size  # 约4.07
            
    def forward(self, sr, hr):
        # 计算基础损失
        l1 = self.l1_loss(sr, hr)
        perceptual = self.perceptual_loss(sr, hr)
        
        # 根据体素大小差异调整损失权重
        if self.voxel_weight:
            # 体素大小差异越大，对高分辨率细节的要求越高
            voxel_weight = torch.log1p(torch.tensor(self.voxel_ratio))
            l1 = l1 * voxel_weight
            perceptual = perceptual * voxel_weight
            
        # 组合损失
        total_loss = l1 + 0.1 * perceptual
        return total_loss
