# -*- coding: utf-8 -*-
"""
Custom loss functions for Super-Resolution training.
Includes Perceptual Loss, GAN Loss, etc.
"""

import torch
import torch.nn as nn
import torch.nn.functional as F
import torchvision.models as models
from torchvision.models import VGG19_Weights # Import weights enum

# Attempt to import SSIM/MS-SSIM
try:
    from pytorch_msssim import ssim, ms_ssim # MS_SSIM requires pytorch_msssim package
    _ssim_available = True
except ImportError:
    print("Warning: pytorch_msssim not found. SSIM/MS-SSIM loss will not be available.")
    _ssim_available = False

# Import Sobel filter if available
try:
    from kornia.filters import Sobel
    _sobel_available = True
except ImportError:
    print("Warning: kornia.filters.Sobel not found. Gradient loss using Sobel will not be available.")
    _sobel_available = False


# Add other necessary imports

# VGG Feature Extractor Helper
class VGGFeatureExtractor(nn.Module):
    def __init__(self, layers_to_extract, requires_grad=False, use_input_norm=True):
        super().__init__()
        # Use VGG19 with recommended weights
        vgg19 = models.vgg19(weights=VGG19_Weights.IMAGENET1K_V1).features
        self.layers_to_extract = sorted(layers_to_extract) # Ensure order
        self.use_input_norm = use_input_norm

        # ImageNet normalization parameters
        self.mean = torch.tensor([0.485, 0.456, 0.406]).view(1, 3, 1, 1)
        self.std = torch.tensor([0.229, 0.224, 0.225]).view(1, 3, 1, 1)

        # Extract relevant layers
        self.features = nn.Sequential()
        max_layer_idx = max(self.layers_to_extract)
        for i, layer in enumerate(vgg19):
            self.features.add_module(str(i), layer)
            if i == max_layer_idx:
                break

        if not requires_grad:
            for param in self.parameters():
                param.requires_grad = False
        self.eval() # Set to evaluation mode

    def forward(self, x):
        # Normalize input if required
        if self.use_input_norm:
            # Ensure input has 3 channels if needed by VGG
            if x.shape[1] == 1:
                x = x.repeat(1, 3, 1, 1) # Repeat grayscale channel
            x = (x - self.mean.to(x.device)) / self.std.to(x.device)

        # Extract features
        extracted_features = {}
        current_feature = x
        for i, layer in enumerate(self.features):
            current_feature = layer(current_feature)
            if i in self.layers_to_extract:
                extracted_features[i] = current_feature
        return extracted_features


class PerceptualLoss(nn.Module):
    """
    Calculates perceptual loss using features from a pretrained VGG19 network.
    """
    def __init__(self, loss_type='l1', layer_weights=None, device='cuda'):
        super().__init__()
        self.device = device
        # Commonly used layers for perceptual loss in VGG19
        # Indices correspond to layers *after* activation (ReLU)
        # VGG19 features: 0:conv1_1, 1:relu1_1, 2:conv1_2, 3:relu1_2, 4:pool1, ...
        # 8: relu2_2, 17: relu3_4, 26: relu4_4, 35: relu5_4
        default_layers = {8: 1.0, 17: 1.0, 26: 1.0, 35: 1.0} # Example: relu2_2, relu3_4, relu4_4, relu5_4
        self.layer_weights = layer_weights if layer_weights is not None else default_layers
        layer_indices = list(self.layer_weights.keys())

        print(f"Initializing Perceptual Loss using VGG19 on layers: {layer_indices}")

        # Create the feature extractor
        self.feature_extractor = VGGFeatureExtractor(layers_to_extract=layer_indices, requires_grad=False).to(device)

        # Define the criterion for comparing features
        if loss_type == 'l1':
            self.criterion = nn.L1Loss()
        elif loss_type == 'mse':
            self.criterion = nn.MSELoss()
        else:
            raise ValueError(f"Unsupported perceptual loss type: {loss_type}")


    def forward(self, generated_img, target_img):
        """
        Calculate perceptual loss between generated and target images.
        Assumes input images are in range [-1, 1]. They will be rescaled to [0, 1]
        before VGG normalization if needed.

        Args:
            generated_img (torch.Tensor): The generated SR image (B, C, H, W), range [-1, 1].
            target_img (torch.Tensor): The real HR image (B, C, H, W), range [-1, 1].

        Returns:
            torch.Tensor: The calculated perceptual loss.
        """
        # Rescale images from [-1, 1] to [0, 1] for VGG input normalization
        generated_img_01 = (generated_img + 1.0) / 2.0
        target_img_01 = (target_img + 1.0) / 2.0

        # Extract features
        generated_features = self.feature_extractor(generated_img_01)
        target_features = self.feature_extractor(target_img_01)

        # Calculate loss across specified layers
        total_loss = 0.0
        for layer_idx in self.layer_weights:
            gen_feat = generated_features[layer_idx]
            target_feat = target_features[layer_idx]
            loss = self.criterion(gen_feat, target_feat)
            total_loss += loss * self.layer_weights[layer_idx]

        return total_loss

# --- Optional: GAN Loss Implementations ---

class GANLoss(nn.Module):
    """
    Base class for GAN losses (LSGAN, WGAN-GP, Hinge, etc.).
    Simplifies switching between different GAN objectives.
    """
    def __init__(self, gan_mode='lsgan', target_real_label=1.0, target_fake_label=0.0, device='cuda'):
        super().__init__()
        self.gan_mode = gan_mode
        self.target_real_label = torch.tensor(target_real_label).to(device)
        self.target_fake_label = torch.tensor(target_fake_label).to(device)
        self.device = device

        if gan_mode == 'lsgan':
            self.criterion = nn.MSELoss()
        elif gan_mode == 'vanilla':
            self.criterion = nn.BCEWithLogitsLoss()
        elif gan_mode == 'wgangp':
            self.criterion = None # WGAN-GP loss is calculated differently
        elif gan_mode == 'hinge':
             self.criterion = None # Hinge loss is calculated differently
        else:
            raise NotImplementedError(f'GAN mode {gan_mode} not implemented')

    def get_target_tensor(self, prediction, target_is_real):
        """Create label tensors with the same size as the input."""
        if target_is_real:
            target_tensor = self.target_real_label
        else:
            target_tensor = self.target_fake_label
        return target_tensor.expand_as(prediction).to(self.device)

    def forward(self, prediction, target_is_real):
        """
        Calculate GAN loss for the given prediction.

        Args:
            prediction (torch.Tensor): Discriminator output.
            target_is_real (bool): Whether the target is real or fake.

        Returns:
            torch.Tensor: Calculated GAN loss.
        """
        if self.gan_mode in ['lsgan', 'vanilla']:
            target_tensor = self.get_target_tensor(prediction, target_is_real)
            loss = self.criterion(prediction, target_tensor)
        elif self.gan_mode == 'wgangp':
            if target_is_real:
                loss = -prediction.mean()
            else:
                loss = prediction.mean()
        elif self.gan_mode == 'hinge':
             if target_is_real:
                 loss = F.relu(1.0 - prediction).mean()
             else:
                 loss = F.relu(1.0 + prediction).mean()
        else:
             raise NotImplementedError()
        return loss

# --- SSIM/MS-SSIM Loss ---
class SSIMLoss(nn.Module):
    """ Structural Similarity Index Measure Loss """
    def __init__(self, data_range=1.0, size_average=True, channel=3, use_ms_ssim=False):
        """
        Args:
            data_range (float or int): Value range of input images (usually 1.0 or 255).
                                       Assumes input range is [0, data_range] for SSIM calculation.
            size_average (bool): If True, average the loss over the batch.
            channel (int): Input image channels.
            use_ms_ssim (bool): If True, use MS-SSIM instead of SSIM.
        """
        super().__init__()
        if not _ssim_available and (use_ms_ssim or not use_ms_ssim): # Check if needed
             raise ImportError("SSIMLoss requires the 'pytorch-msssim' package. Please install it.")

        self.data_range = data_range
        self.size_average = size_average
        self.channel = channel
        self.use_ms_ssim = use_ms_ssim
        self.ssim_module = ms_ssim if use_ms_ssim else ssim

    def forward(self, prediction, target):
        """
        Args:
            prediction (torch.Tensor): Predicted image (B, C, H, W), range assumed [-1, 1].
            target (torch.Tensor): Target image (B, C, H, W), range assumed [-1, 1].
        Returns:
            torch.Tensor: SSIM loss.
        """
        # 完全重构的维度处理
        def normalize_tensor_dims(tensor):
            """标准化tensor维度为4D (B, C, H, W)"""
            # 移除所有大小为1的维度
            tensor = tensor.squeeze()

            # 根据剩余维度数量进行处理
            if tensor.dim() == 2:  # (H, W)
                tensor = tensor.unsqueeze(0).unsqueeze(0)  # -> (1, 1, H, W)
            elif tensor.dim() == 3:  # (C, H, W) 或 (B, H, W)
                if tensor.shape[0] <= 3:  # 假设是通道维度
                    tensor = tensor.unsqueeze(0)  # -> (1, C, H, W)
                else:  # 假设是批次维度
                    tensor = tensor.unsqueeze(1)  # -> (B, 1, H, W)
            elif tensor.dim() > 4:
                # 如果维度过多，保留最后4个维度
                tensor = tensor.view(-1, *tensor.shape[-3:])

            return tensor

        # 标准化两个tensor的维度
        prediction = normalize_tensor_dims(prediction)
        target = normalize_tensor_dims(target)

        # 确保两个tensor形状匹配
        if prediction.shape != target.shape:
            # 调整到相同的batch size和channel数
            batch_size = max(prediction.shape[0], target.shape[0])
            channels = max(prediction.shape[1], target.shape[1])
            height = min(prediction.shape[2], target.shape[2])
            width = min(prediction.shape[3], target.shape[3])

            # 裁剪到相同尺寸
            prediction = prediction[:batch_size, :channels, :height, :width]
            target = target[:batch_size, :channels, :height, :width]

        # 检查最小尺寸要求
        min_size = 32  # 提高最小尺寸要求，确保SSIM计算有意义
        if prediction.shape[-1] < min_size or prediction.shape[-2] < min_size:
            # 对于太小的图像，使用插值放大到最小尺寸
            prediction = F.interpolate(prediction, size=(min_size, min_size),
                                     mode='bilinear', align_corners=False)
            target = F.interpolate(target, size=(min_size, min_size),
                                 mode='bilinear', align_corners=False)

        # Rescale images from [-1, 1] to [0, 1]
        prediction_01 = torch.clamp((prediction + 1.0) / 2.0, 0.0, 1.0)
        target_01 = torch.clamp((target + 1.0) / 2.0, 0.0, 1.0)

        try:
            ssim_val = self.ssim_module(
                prediction_01,
                target_01,
                data_range=1.0,
                size_average=self.size_average,
            )
            # SSIM is a similarity measure (higher is better), loss is 1 - SSIM
            loss = 1.0 - ssim_val

            # 确保loss是有效的
            if torch.isnan(loss) or torch.isinf(loss):
                loss = torch.tensor(0.0, device=prediction.device, requires_grad=True)

        except Exception as e:
            # 静默处理错误，避免日志污染
            loss = torch.tensor(0.0, device=prediction.device, requires_grad=True)

        return loss

# --- Gradient Loss ---
class GradientLoss(nn.Module):
    """ Computes the L1 loss between the gradients of the prediction and the target. """
    def __init__(self, loss_weight=1.0, reduction='mean'):
        super().__init__()
        if not _sobel_available:
             raise ImportError("GradientLoss requires kornia.filters.Sobel. Please install kornia.")
        self.loss_weight = loss_weight
        self.reduction = reduction
        self.sobel = Sobel().to(torch.device('cuda' if torch.cuda.is_available() else 'cpu')) # Initialize Sobel filter

    def forward(self, prediction, target):
        """
        Args:
            prediction (torch.Tensor): Predicted image (B, C, H, W).
            target (torch.Tensor): Target image (B, C, H, W).
        Returns:
            torch.Tensor: Gradient loss.
        """
        # 维度检查和调整
        if prediction.dim() > 4:
            while prediction.dim() > 4:
                prediction = prediction.squeeze(0)
        if target.dim() > 4:
            while target.dim() > 4:
                target = target.squeeze(0)

        # 确保至少是4维 (B, C, H, W)
        if prediction.dim() == 3:
            prediction = prediction.unsqueeze(0)
        if target.dim() == 3:
            target = target.unsqueeze(0)

        try:
            pred_grad = self.sobel(prediction)
            target_grad = self.sobel(target)
            loss = F.l1_loss(pred_grad, target_grad, reduction=self.reduction)
        except Exception as e:
            print(f"梯度损失计算错误: {e}, prediction shape: {prediction.shape}, target shape: {target.shape}")
            loss = torch.tensor(0.0, device=prediction.device, requires_grad=True)

        return loss * self.loss_weight


# TODO: Add other potential losses if needed (e.g., Style Loss, Total Variation Loss)
