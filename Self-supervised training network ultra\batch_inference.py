#!/usr/bin/env python3
"""
Batch inference script for processing entire CT datasets
Optimized for memory efficiency and processing speed
"""

import os
import sys
import argparse
import torch
import yaml
import time
from pathlib import Path
from tqdm import tqdm
import multiprocessing as mp
from concurrent.futures import ThreadPoolExecutor

# Add project root to path
PROJECT_ROOT = os.path.abspath(os.path.join(os.path.dirname(__file__), '..'))
if PROJECT_ROOT not in sys.path:
    sys.path.insert(0, PROJECT_ROOT)

from inference_sr_ultra import load_config, load_model, inference_single_image

def process_batch(args_tuple):
    """Process a batch of images (for multiprocessing)"""
    model_config, checkpoint_path, input_files, output_dir, device_id, patch_based, patch_size, overlap = args_tuple
    
    # Set device for this process
    device = torch.device(f'cuda:{device_id}' if torch.cuda.is_available() and device_id >= 0 else 'cpu')
    
    # Load model for this process
    model = load_model(model_config, checkpoint_path, device)
    
    results = []
    for input_path in input_files:
        try:
            start_time = time.time()
            
            # Generate output path
            filename = os.path.basename(input_path)
            output_filename = f"sr_{os.path.splitext(filename)[0]}.tif"
            output_path = os.path.join(output_dir, output_filename)
            
            # Skip if already processed
            if os.path.exists(output_path):
                results.append({
                    'file': filename,
                    'status': 'skipped',
                    'time': 0,
                    'error': None
                })
                continue
            
            # Process image
            inference_single_image(
                model, input_path, output_path, model_config, device,
                patch_based=patch_based,
                patch_size=patch_size,
                overlap=overlap
            )
            
            processing_time = time.time() - start_time
            results.append({
                'file': filename,
                'status': 'success',
                'time': processing_time,
                'error': None
            })
            
        except Exception as e:
            results.append({
                'file': os.path.basename(input_path),
                'status': 'error',
                'time': 0,
                'error': str(e)
            })
    
    return results

def batch_inference(config_path, checkpoint_path, input_dir, output_dir, 
                   num_gpus=1, batch_size_per_gpu=4, patch_based=True, 
                   patch_size=32, overlap=8, resume=True):
    """
    Perform batch inference on entire dataset
    
    Args:
        config_path: Path to model config
        checkpoint_path: Path to model checkpoint
        input_dir: Directory containing input images
        output_dir: Directory to save results
        num_gpus: Number of GPUs to use
        batch_size_per_gpu: Number of images per GPU batch
        patch_based: Whether to use patch-based inference
        patch_size: Size of patches for inference
        overlap: Overlap between patches
        resume: Whether to skip already processed images
    """
    
    # Load config
    config = load_config(config_path)
    
    # Create output directory
    os.makedirs(output_dir, exist_ok=True)
    
    # Get input files
    input_files = []
    for ext in ['.tif', '.tiff', '.png', '.jpg', '.jpeg']:
        input_files.extend(Path(input_dir).glob(f'*{ext}'))
        input_files.extend(Path(input_dir).glob(f'*{ext.upper()}'))
    
    input_files = [str(f) for f in sorted(input_files)]
    print(f"Found {len(input_files)} input images")
    
    if len(input_files) == 0:
        print("No input images found!")
        return
    
    # Filter out already processed files if resume=True
    if resume:
        remaining_files = []
        for input_path in input_files:
            filename = os.path.basename(input_path)
            output_filename = f"sr_{os.path.splitext(filename)[0]}.tif"
            output_path = os.path.join(output_dir, output_filename)
            if not os.path.exists(output_path):
                remaining_files.append(input_path)
        
        print(f"Resuming: {len(input_files) - len(remaining_files)} already processed, {len(remaining_files)} remaining")
        input_files = remaining_files
    
    if len(input_files) == 0:
        print("All images already processed!")
        return
    
    # Setup multiprocessing
    if num_gpus > 1 and torch.cuda.is_available():
        print(f"Using {num_gpus} GPUs for parallel processing")
        
        # Split files among GPUs
        files_per_gpu = len(input_files) // num_gpus
        gpu_batches = []
        
        for gpu_id in range(num_gpus):
            start_idx = gpu_id * files_per_gpu
            if gpu_id == num_gpus - 1:  # Last GPU gets remaining files
                end_idx = len(input_files)
            else:
                end_idx = (gpu_id + 1) * files_per_gpu
            
            gpu_files = input_files[start_idx:end_idx]
            
            # Further split into batches for this GPU
            for i in range(0, len(gpu_files), batch_size_per_gpu):
                batch_files = gpu_files[i:i+batch_size_per_gpu]
                gpu_batches.append((
                    config, checkpoint_path, batch_files, output_dir, 
                    gpu_id, patch_based, patch_size, overlap
                ))
        
        # Process batches in parallel
        with mp.Pool(processes=num_gpus) as pool:
            all_results = []
            with tqdm(total=len(input_files), desc="Processing images") as pbar:
                for batch_results in pool.imap(process_batch, gpu_batches):
                    all_results.extend(batch_results)
                    pbar.update(len(batch_results))
    
    else:
        # Single GPU/CPU processing
        device = torch.device('cuda:0' if torch.cuda.is_available() else 'cpu')
        print(f"Using single device: {device}")
        
        model = load_model(config, checkpoint_path, device)
        
        all_results = []
        for input_path in tqdm(input_files, desc="Processing images"):
            try:
                start_time = time.time()
                
                filename = os.path.basename(input_path)
                output_filename = f"sr_{os.path.splitext(filename)[0]}.tif"
                output_path = os.path.join(output_dir, output_filename)
                
                inference_single_image(
                    model, input_path, output_path, config, device,
                    patch_based=patch_based,
                    patch_size=patch_size,
                    overlap=overlap
                )
                
                processing_time = time.time() - start_time
                all_results.append({
                    'file': filename,
                    'status': 'success',
                    'time': processing_time,
                    'error': None
                })
                
            except Exception as e:
                all_results.append({
                    'file': os.path.basename(input_path),
                    'status': 'error',
                    'time': 0,
                    'error': str(e)
                })
    
    # Generate summary report
    generate_summary_report(all_results, output_dir)

def generate_summary_report(results, output_dir):
    """Generate summary report of batch processing"""
    
    total_files = len(results)
    successful = len([r for r in results if r['status'] == 'success'])
    skipped = len([r for r in results if r['status'] == 'skipped'])
    errors = len([r for r in results if r['status'] == 'error'])
    
    processing_times = [r['time'] for r in results if r['status'] == 'success' and r['time'] > 0]
    avg_time = sum(processing_times) / len(processing_times) if processing_times else 0
    total_time = sum(processing_times)
    
    # Write summary report
    report_path = os.path.join(output_dir, 'batch_processing_report.txt')
    with open(report_path, 'w') as f:
        f.write("Batch Super-Resolution Processing Report\n")
        f.write("="*50 + "\n\n")
        f.write(f"Total files: {total_files}\n")
        f.write(f"Successfully processed: {successful}\n")
        f.write(f"Skipped (already exists): {skipped}\n")
        f.write(f"Errors: {errors}\n")
        f.write(f"Success rate: {successful/total_files*100:.1f}%\n\n")
        
        if processing_times:
            f.write(f"Average processing time: {avg_time:.2f} seconds\n")
            f.write(f"Total processing time: {total_time/3600:.2f} hours\n")
            f.write(f"Processing speed: {successful/total_time*3600:.1f} images/hour\n\n")
        
        if errors > 0:
            f.write("Error details:\n")
            for result in results:
                if result['status'] == 'error':
                    f.write(f"  {result['file']}: {result['error']}\n")
    
    print(f"\nBatch processing completed!")
    print(f"Successfully processed: {successful}/{total_files} images")
    print(f"Success rate: {successful/total_files*100:.1f}%")
    if processing_times:
        print(f"Average processing time: {avg_time:.2f} seconds per image")
    print(f"Summary report saved to: {report_path}")

def main():
    parser = argparse.ArgumentParser(description='Batch Super-Resolution Inference')
    parser.add_argument('--config', type=str, required=True, help='Path to config file')
    parser.add_argument('--checkpoint', type=str, required=True, help='Path to model checkpoint')
    parser.add_argument('--input_dir', type=str, required=True, help='Input directory with LR images')
    parser.add_argument('--output_dir', type=str, required=True, help='Output directory for SR images')
    parser.add_argument('--num_gpus', type=int, default=1, help='Number of GPUs to use')
    parser.add_argument('--batch_size_per_gpu', type=int, default=4, help='Batch size per GPU')
    parser.add_argument('--patch_based', action='store_true', default=True, help='Use patch-based inference')
    parser.add_argument('--patch_size', type=int, default=32, help='Patch size for inference')
    parser.add_argument('--overlap', type=int, default=8, help='Overlap between patches')
    parser.add_argument('--no_resume', action='store_true', help='Disable resume (reprocess all images)')
    
    args = parser.parse_args()
    
    batch_inference(
        config_path=args.config,
        checkpoint_path=args.checkpoint,
        input_dir=args.input_dir,
        output_dir=args.output_dir,
        num_gpus=args.num_gpus,
        batch_size_per_gpu=args.batch_size_per_gpu,
        patch_based=args.patch_based,
        patch_size=args.patch_size,
        overlap=args.overlap,
        resume=not args.no_resume
    )

if __name__ == '__main__':
    main()
